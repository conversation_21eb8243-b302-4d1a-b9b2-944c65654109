#!/bin/bash

# Odoo 启动脚本
echo "正在启动 Odoo ERP 系统..."

# 检查 Python 依赖
echo "检查 Python 依赖..."
python3 -c "import odoo" 2>/dev/null || {
    echo "Odoo 模块未找到，正在设置 Python 路径..."
    export PYTHONPATH="../odoo:$PYTHONPATH"
}

# 创建必要的目录
mkdir -p filestore
mkdir -p logs

# 启动 Odoo
echo "启动 Odoo 服务器..."
echo "访问地址: http://localhost:8069"
echo "管理员密码: admin123"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 使用 Python 直接运行 Odoo
cd ../odoo
python3 odoo-bin -c ../hex-erp/odoo.conf --dev=all
