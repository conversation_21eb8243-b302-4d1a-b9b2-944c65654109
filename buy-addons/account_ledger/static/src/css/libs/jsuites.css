
/**
 * (c) jSuites Javascript Web Components
 *
 * Author: <PERSON> <<EMAIL>>
 * Website: https://bossanova.uk/jsuites/
 * Description: Create amazing web based applications.
 *
 * MIT License
 *
 */

/** General **/

.jdragging {
    opacity:0.2;
    filter: alpha(opacity=20);
}

.jupload {
    background-image: url(data:image/png;base64,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);
    background-repeat: no-repeat;
    background-size: 100px;
    background-position: center;
    background-color: rgb(230, 230, 230);
    border: 1px dotted #eee;
    cursor: pointer;
    box-sizing: border-box;
    width:100%;
    min-height:180px;
}

.jbackdrop {
    position:fixed;
    top:0px;
    left:0px;
    min-width:100%;
    min-height:100%;
    background-color:rgba(0,0,0,0.5);
    border:0px;
    padding:0px;
    z-index:8000;
    
  -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
     -khtml-user-select: none; /* Konqueror HTML */
       -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently
                                  supported by Chrome and Opera */
}

.jremove {
    opacity: 0.2;
    filter: alpha(opacity=20);
}

/** Animations **/
.fade-in {
    animation: fade-in 2s forwards;
    -webkit-animation: fade-in 2s forwards;
}

.fade-out {
    animation: fade-out 1s forwards;
    -webkit-animation: fade-out 1s forwards;
}

.slide-left-in {
    animation: slide-left-in 0.4s forwards;
    -webkit-animation: slide-left-in 0.4s forwards;
}

.slide-left-out {
    animation: slide-left-out 0.4s forwards;
    -webkit-animation: slide-left-out 0.4s forwards;
}

.slide-right-in {
    animation: slide-right-in 0.4s forwards;
    -webkit-animation: slide-right-in 0.4s forwards;
}

.slide-right-out {
    animation: slide-right-out 0.4s forwards;
    -webkit-animation: slide-right-out 0.4s forwards;
}

.slide-top-in {
    animation: slide-top-in 0.4s forwards;
    -webkit-animation: slide-top-in 0.4s forwards;
}

.slide-top-out {
    animation: slide-top-out 0.2s forwards;
    -webkit-animation: slide-top-out 0.2s forwards;
}

.slide-bottom-in {
    animation: slide-bottom-in 0.4s forwards;
    -webkit-animation: slide-bottom-in 0.4s forwards;
}

.slide-bottom-out {
    animation: slide-bottom-out 0.1s forwards;
    -webkit-animation: slide-bottom-out 0.1s forwards;
}

/** Fadein and Fadeout **/
@keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 100; }
}

@-webkit-keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 100; }
}

@keyframes fade-out {
    0% { opacity: 100; }
    100% { opacity: 0; }
}

@-webkit-keyframes fade-out {
    0% { opacity: 100; }
    100% { opacity: 0; }
}

/** Keyframes Left to Right **/
@keyframes slide-left-in {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(0%); }
}

@-webkit-keyframes slide-left-in {
    0% { transform: translateX(-100%); }
    100% { -webkit-transform: translateX(0%); }
}
    
@keyframes slide-left-out {
    0% { transform: translateX(0%); }
    100% { transform: translateX(-100%); }
}

@-webkit-keyframes slide-left-out {
    0% { -webkit-transform: translateX(0%); }
    100% { -webkit-transform: translateX(-100%); }
}

/** Keyframes Right to Left **/
@keyframes slide-right-in {
    0% { transform: translateX(100%); }
    100% { transform: translateX(0%); }
}

@-webkit-keyframes slide-right-in
{
    0% { transform: translateX(100%); }
    100% { -webkit-transform: translateX(0%); }
}
    
@keyframes slide-right-out {
    0% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

@-webkit-keyframes slide-right-out {
    0% { -webkit-transform: translateX(0%); }
    100% { -webkit-transform: translateX(100%); }
}

/** Keyframes Top to Bottom **/
@keyframes slide-top-in {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(0%); }
}

@-webkit-keyframes slide-top-in {
    0% { transform: translateY(-100%); }
    100% { -webkit-transform: translateY(0%); }
}
    
@keyframes slide-top-out {
    0% { transform: translateY(0%); }
    100% { transform: translateY(-100%); }
}

@-webkit-keyframes slide-top-out {
    0% { -webkit-transform: translateY(0%); }
    100% { -webkit-transform: translateY(-100%); }
}

/** Keyframes Bottom to Top **/
@keyframes slide-bottom-in {
    0% { transform: translateY(100%); }
    100% { transform: translateY(0%); }
}

@-webkit-keyframes slide-bottom-in {
    0% { transform: translateY(100%); }
    100% { -webkit-transform: translateY(0%); }
}
    
@keyframes slide-bottom-out {
    0% { transform: translateY(0%); }
    100% { transform: translateY(100%); }
}

@-webkit-keyframes slide-bottom-out {
    0% { -webkit-transform: translateY(0%); }
    100% { -webkit-transform: translateY(100%); }
}


@supports (-webkit-overflow-scrolling: touch) {
    .app .options input:checked:before {
        top:-12px;
    }
}

@-webkit-keyframes spin {
    from {
        -webkit-transform:rotate(0deg);
    }
    to {
        -webkit-transform:rotate(360deg);
    }
}

@keyframes spin {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}

.unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/**
 * Date & Datetime picker v1.0.1
 * Author: <EMAIL>
 * https://github.com/paulhodel/jtools
 */
 
.jcalendar {
    position:relative;
    z-index:9000;
    display:none;
    box-sizing:border-box;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    min-width:280px;
}

.jcalendar-focus {
    display:block;
}

.jcalendar-backdrop {
    position:fixed;
    top:0px;
    left:0px;
    z-index:9000;
    min-width:100%;
    min-height:100%;
    background-color:rgba(0,0,0,0.5);
    border:0px;
    padding:0px;
    display:none;
}

.jcalendar-container {
    position:relative;
    box-sizing:border-box;
}

.jcalendar-content {
    position:absolute;
    z-index:9001;
    -webkit-box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    -moz-box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    background-color:#fff;
}

.jcalendar-content > table {
    width:100%;
    background-color:#fff;
}

.jcalendar-content > table > tbody td {
    box-sizing:border-box;
    cursor:pointer;
    padding:9px;
    font-size:0.9em;
}

.jcalendar-content > table > thead {
    cursor:pointer;
}

.jcalendar-header {
    text-align:center;
}

.jcalendar-header span {
    margin-right:4px;
    font-size:1.2em;
    font-weight:bold;
}

.jcalendar-prev {
    cursor:pointer;
    background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z' fill='%23000' /%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3C/svg%3E");
    background-position:center;
    background-repeat:no-repeat;
}

.jcalendar-next {
    cursor:pointer;
    background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z' fill='%23000' /%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3C/svg%3E");
    background-position:center;
    background-repeat:no-repeat;
}

.jcalendar-weekday {
    font-weight:550;
    background-color:#fcfcfc;
    padding:14px;
}

.jcalendar thead td {
    padding:10px;
    height:40px;
}

.jcalendar tfoot td {
    padding:10px;
}

.jcalendar-months td, .jcalendar-years td {
    height:24px;
} 

.jcalendar-input {
    padding-right:18px;
    background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='gray'%3E%3Cpath d='M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z'/%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3C/svg%3E");
    background-position:top 50% right 5px;
    background-repeat:no-repeat;
    box-sizing: border-box;
}

.jcalendar-done {
    -webkit-box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    -moz-box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    box-shadow: 1px 1px 5px 0px rgba(0,0,0,0.39);
    background-color:#fff;
}

.jcalendar-update {
    border:1px solid #ccc;
    background-color:#fff;
    border-radius:4px;
    padding:5px;
    width:100%;
}

.jcalendar select {
    width:55px;
    display:inline-block;
    border:0px;
    padding:4px;
    text-align:center;
    font-size:1.1em;
    user-select:none;
    margin-right:10px;
}

.jcalendar select:first-child
{
    margin-right:2px;
}

.jcalendar-selected {
    background-color:#eee;
}

.jcalendar-reset, .jcalendar-confirm {
    text-transform:uppercase;
    cursor:pointer;
    color: var(--active-color);
}

.jcalendar-controls {
    padding:15px;

    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align:middle;

    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -webkit-flex-flow: row wrap;
    justify-content: space-between;
    align-items:center;

    border-bottom:1px solid #ddd;
}

.jcalendar-controls div {
    font-weight:bold;
}

.jcalendar-fullsize  {
    position:fixed;
    width:100%;
    top:0px;
    left:0px;
}

.jcalendar-fullsize .jcalendar-content
{
    position:fixed;
    width:100%;
    left:0px;
    bottom:0px;
}

.jcalendar-focus.jcalendar-fullsize .jcalendar-backdrop {
    display:block;
}


/**
 * Color Picker v1.0.1
 * Author: <EMAIL>
 * https://github.com/paulhodel/jtools
 */

.jcolor {
    position:relative;
    display:none;
    outline:none;
}

.jcolor-content {
    position:absolute;
    z-index:9000;
    user-select:none;
    -webkit-font-smoothing: antialiased;
    font-size: .875rem;
    letter-spacing: .2px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
    box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
    padding:10px;
    background-color:#fff;
}

.jcolor-focus {
    display:block;
}

.jcolor td {
    border:2px solid #fff;
}

.jcolor-selected {
    border:2px solid #000 !important;
    background-repeat:no-repeat;
    background-size: cover;
    background-position:0 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z' fill='white'/%3E%3C/svg%3E");
}

/**
 * Contextmenu v1.0.1
 * Author: <EMAIL>
 * https://github.com/paulhodel/jsuites
 */
 
.jcontextmenu {
    position:fixed;
    z-index:10000;
    background:#fff;
    color: #555;
    font-family: sans-serif;
    font-size: 11px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-box-shadow: 2px 2px 2px 0px rgba(143, 144, 145, 1);
    -moz-box-shadow: 2px 2px 2px 0px rgba(143, 144, 145, 1);
    box-shadow: 2px 2px 2px 0px rgba(143, 144, 145, 1);
    border: 1px solid #C6C6C6;
    padding: 0px;
    padding-top:4px;
    padding-bottom:4px;
    margin:0px;
    outline:none;
    display:none;
}

.jcontextmenu.jcontextmenu-focus {
    display:inline-block;
}

.jcontextmenu li {
    box-sizing: border-box;
    display: block;
    padding: 8px 8px 8px 30px;
    width: 250px;
    position: relative;
    cursor: default;
}

.jcontextmenu li a {
    color: #555;
    text-decoration: none;
}

.jcontextmenu li span {
    float: right;
    margin-right:10px;
}

.jcontextmenu .contextmenu-disabled {
    color: #a1a192;
}

.jcontextmenu li:not(.contextmenu-line):hover {
    background: #ebebeb;
}

.jcontextmenu li.contextmenu-line {
    border-top: 1px solid #e9e9e9;
    margin-top:5px;
    padding:2px;
}

.jcontextmenu hr {
    border: 1px solid #e9e9e9;
    border-bottom: 0;
    margin-top:5px;
    margin-bottom:5px;
}

/**
 * Dialog v1.0.1
 * Author: <EMAIL>
 * https://github.com/paulhodel/jtools
 */
 
.jdialog
{
    position:fixed;
    left: 0;
    top: 0;
    width: 100%;
    min-height:100%;
    z-index:10000;

    background-color:rgba(0,0,0,0.5);
    -webkit-transition-duration: .1s;
    transition-duration: .1s;
    display: flex;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -webkit-box-align: center;

    align-items: center;
}

.jdialog .jdialog-container
{
    width:100%;
    max-width:280px;
    box-sizing: border-box;
    margin:0 auto;
    vertical-align:middle;
    background-color:#fff;
    border-radius:10px;

    opacity: 0;
    transition: opacity .2s;
    -webkit-transition: opacity .2s;
}

.jdialog .jdialog-header
{
    padding:10px;
    text-align:center;
}

.jdialog .jdialog-header .jdialog-title
{
    font-size:1em;
    font-weight:bold;
    padding:8px;
}

.jdialog .jdialog-header .jdialog-message
{
    padding:4px;
}

.jdialog .jdialog-footer
{
    border-top:1px solid #ccc;
    display: flex;
    flex-wrap:row wrap;
    align-content: stretch;
    align-items:center;
    text-align:center;
}

.jdialog .jdialog-footer > div
{
    padding:4px;
    border-left:1px solid #ccc;
    text-align:center;
    width:100%;
}
.jdialog .jdialog-footer > div:first-child
{
    border-left:0px;
}

.jdialog .jdialog-footer input
{
    border:0px;
    margin:0px;
    background-color:transparent;
    color:var(--active-color);
    width:100%;
    outline:none;
}

/**
 * (c) 2013 jDropdown
 * http://www.github.com/paulhodel/jdropdown
 *
 * @author: Paul Hodel <<EMAIL>>
 * @description: Custom dropdowns
 */

.jdropdown
{
    cursor:pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    box-sizing: border-box;
    background:#fff;
    -webkit-tap-highlight-color: transparent;
}

.jdropdown-header::placeholder
{
    color:#000;
}

.jdropdown-backdrop
{
    position:fixed;
    top:0px;
    left:0px;
    min-width:100%;
    min-height:100%;
    background-color:rgba(0,0,0,0.5);
    border:0px;
    padding:0px;
    z-index:8000;
    display:none;
}

.jdropdown-focus
{
    position:relative;
}

.jdropdown-focus .jdropdown-container
{
    display:block;
}

.jdropdown-focus .jdropdown-header
{
    outline:auto 5px -webkit-focus-ring-color;
}

.jdropdown-container-header
{
    padding:0px;
    margin:0px;
    position:relative;
}

.jdropdown-header
{
    width:100%;
    appearance: none;
    background-repeat: no-repeat;
    background-position:top 50% right 5px;
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0V0z'/%3E%3Cpath d='M7 10l5 5 5-5H7z' fill='gray'/%3E%3C/svg%3E");
    text-overflow: ellipsis;
    cursor:pointer;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.jdropdown-insert-button
{
    font-size: 1.4em;
    text-transform: uppercase;
    position:absolute;
    right: 30px;
    top: 4px;
    display:none;
}

.jdropdown-container
{
    display:none;
}

.jdropdown-close
{
    display:none;
    font-size:1em;
    color: var(--active-color);
    text-transform:uppercase;
    text-align:right;
    padding:10px;
    padding-right:15px;
    font-weight:bold;
}

.jdropdown-content
{
    min-width:inherit;
    margin:0px;
    box-sizing:border-box;
}

.jdropdown-content:empty
{
}

.jdropdown-item
{
    white-space: nowrap;
    text-align:left;
    text-overflow: ellipsis;
    overflow-x:hidden;
    color:#000;
    display:flex;
    align-items:center;
}

.jdropdown-image
{
    margin-right:10px;
    width:40px;
    height:40px;
    border-radius:20px;
}

.jdropdown-image-small
{
    width:24px;
    height:24px;
}

.jdropdown-title
{
    font-size:0.7em;
    color:#aaa;
    text-overflow: ellipsis;
    overflow-x:hidden;
    display:block;
}

/** Default visual **/

.jdropdown-default .jdropdown-header
{
    border:1px solid #ccc;
    padding:5px;
    padding-left:10px;
    padding-right:16px;
}

.jdropdown-default .jdropdown-container
{
    position:absolute;
    z-index:9001;
    background-color:#fff;
}

.jdropdown-default.jdropdown-focus.jdropdown-insert .jdropdown-header {
    padding-right:50px;
}

.jdropdown-default.jdropdown-focus.jdropdown-insert .jdropdown-insert-button {
    display:block;
}

.jdropdown-default .jdropdown-content
{
    min-width:inherit;
    border:1px solid #8fb1e3;
    margin:0px;
    background-color:#fff;
    box-sizing:border-box;
    min-height:10px;
    max-height:215px;
    overflow-y:auto;
}

.jdropdown-default .jdropdown-item
{
    padding:4px;
    padding-left:8px;
    padding-right:40px;
}

.jdropdown-default .jdropdown-item:hover
{
    background-color:#1f93ff;
    color:#fff;
}

.jdropdown-default .jdropdown-cursor
{
    background-color:#1f93ff;
    color:#fff;
}

.jdropdown-default .jdropdown-selected
{
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIiAvPjxwYXRoIGQ9Ik05IDE2LjE3TDQuODMgMTJsLTEuNDIgMS40MUw5IDE5IDIxIDdsLTEuNDEtMS40MXoiIGZpbGw9IndoaXRlIiAvPjwvc3ZnPgo=');
    background-repeat:no-repeat;
    background-position:top 50% right 5px;
    background-color:#1f93ff;
    color:#fff;
}

.jdropdown-default .jdropdown-group
{
    margin-top:5px;
}

.jdropdown-default .jdropdown-group .jdropdown-item
{
    padding-left:16px;
}

.jdropdown-default .jdropdown-group-name
{
    padding-left:8px;
    font-weight:bold;
}


/** Default render for mobile **/

.jdropdown-picker
{
    width:100% !important;
    border-top:1px solid #e6e6e8;
    border-bottom:1px solid #e6e6e8;
    padding-top:10px;
    padding-bottom:10px;
    box-sizing: border-box;
}

.jdropdown-picker.jdropdown-focus .jdropdown-backdrop
{
    display:block;
}

.jdropdown-picker .jdropdown-header
{
    border:0px;
    padding:0px;
    margin:0px;
    outline:none;
    text-transform:uppercase;
    font-size:1em;
    padding-right:24px;
}

.jdropdown-picker .jdropdown-container
{
    position:fixed;
    bottom:0px;
    left:0px;
    border-bottom:1px solid #e6e6e8;
    width:100%;
    background-color:#fff;
    box-sizing: border-box;
    z-index:9000;
}

.jdropdown-picker .jdropdown-close
{
    -webkit-box-shadow: 0px -1px 5px 0px rgba(0,0,0,0.39);
    -moz-box-shadow: 0px -1px 5px 0px rgba(0,0,0,0.39);
    box-shadow: 0px -1px 5px 0px rgba(0,0,0,0.39);
    background-color:#fff;
    display:block;
}

.jdropdown-picker .jdropdown-content
{
    overflow-y:scroll;
    height:280px;
    background-color:#fafafa;
    border-top:1px solid #e6e6e8;
}

.jdropdown-picker .jdropdown-group-name
{
    font-size: 1em;
    text-transform: uppercase;
    padding-top:10px;
    padding-bottom:10px;
    display: block;
    border-bottom: 1px solid #e6e6e8;
    padding-left:20px;
    padding-right:20px;
    text-align:center;
    font-weight:bold;
}

.jdropdown-picker .jdropdown-item
{
    font-size: 1em;
    text-transform: uppercase;
    padding-top:10px;
    padding-bottom:10px;
    display: block;
    border-bottom: 1px solid #e6e6e8;
    padding-left:20px;
    padding-right:20px;
}

.jdropdown-picker .jdropdown-selected
{
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIiAvPjxwYXRoIGQ9Ik05IDE2LjE3TDQuODMgMTJsLTEuNDIgMS40MUw5IDE5IDIxIDdsLTEuNDEtMS40MXoiIGZpbGw9IndoaXRlIiAvPjwvc3ZnPgo=');
    background-repeat:no-repeat;
    background-position:top 50% right 15px;
    background-color:#1f93ff;
    color:#fff;
}

.jdropdown-picker .jdropdown-cursor
{
    background-color:#1f93ff;
    color:#fff;
}

/** Default render for mobile searchbar **/

.jdropdown-searchbar
{
    width:100%;
    border-top:1px solid #e6e6e8;
    border-bottom:1px solid #e6e6e8;
    padding-top:10px;
    padding-bottom:10px;
}

.jdropdown-searchbar.jdropdown-focus
{
    position:absolute;
    top:0px;
    left:0px;
    min-height:100%;
    background-color:#fafafa;
    padding:0px;
    padding-top:40px !important;
    z-index:9001;
}

.jdropdown-searchbar.jdropdown-focus .jdropdown-container-header
{
    padding:10px;
    background-color:#fff;
    box-shadow: 0 1px 2px rgba(0,0,0,.1);
    position:fixed;
    top:0px;
    left:0px;
    width:100%;
}

.jdropdown-searchbar.jdropdown-focus .jdropdown-header
{
    background-repeat: no-repeat;
    background-position-x: 0%;
    background-position-y: 40%;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTUuNSAxNGgtLjc5bC0uMjgtLjI3QzE1LjQxIDEyLjU5IDE2IDExLjExIDE2IDkuNSAxNiA1LjkxIDEzLjA5IDMgOS41IDNTMyA1LjkxIDMgOS41IDUuOTEgMTYgOS41IDE2YzEuNjEgMCAzLjA5LS41OSA0LjIzLTEuNTdsLjI3LjI4di43OWw1IDQuOTlMMjAuNDkgMTlsLTQuOTktNXptLTYgMEM3LjAxIDE0IDUgMTEuOTkgNSA5LjVTNy4wMSA1IDkuNSA1IDE0IDcuMDEgMTQgOS41IDExLjk5IDE0IDkuNSAxNHoiIGZpbGw9IiNlNmU2ZTgiLz48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PC9zdmc+);
    padding-left:30px !important;
    padding-right:60px !important;
}

.jdropdown-searchbar.jdropdown-focus .jdropdown-close
{
    display:block;
}

.jdropdown-searchbar .jdropdown-header
{
    border:0px;
    padding:0px;
    margin:0px;
    outline:none;
    text-transform:uppercase;
    font-size:1em;
    padding-right:30px;
}

.jdropdown-searchbar .jdropdown-close
{
    position:fixed;
    top:0px;
    right:0px;
}

.jdropdown-searchbar .jdropdown-content
{
    margin-top:10px;
}

.jdropdown-searchbar .jdropdown-group
{
    margin-top:10px;
    margin-bottom:15px;
    background-color:#fff;
}

.jdropdown-searchbar .jdropdown-group-name
{
    border-top: 1px solid #e6e6e8;
    border-bottom: 1px solid #e6e6e8;
    padding:10px;
    padding-left:12px;
    font-weight:bold;
}

.jdropdown-searchbar .jdropdown-group-arrow
{
    float:right;
    width:24px;
    height:24px;
    background-repeat:no-repeat;
}

.jdropdown-searchbar .jdropdown-group-arrow-down
{
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNy40MSA4LjU5TDEyIDEzLjE3bDQuNTktNC41OEwxOCAxMGwtNiA2LTYtNiAxLjQxLTEuNDF6Ii8+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgyNHYyNEgwVjB6Ii8+PC9zdmc+);
}

.jdropdown-searchbar .jdropdown-group-arrow-up
{
    background-image: url(data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTcuNDEgMTUuNDFMMTIgMTAuODNsNC41OSA0LjU4TDE4IDE0bC02LTYtNiA2eiIvPjxwYXRoIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz48L3N2Zz4=);
}

.jdropdown-searchbar .jdropdown-item
{
    padding-top:10px;
    padding-bottom:10px;
    border-bottom: 1px solid #e6e6e8;
    padding-left:15px;
    padding-right:40px;
    background-color:#fff;
    font-size:0.9em;
}

.jdropdown-searchbar .jdropdown-description {
    text-overflow: ellipsis;
    overflow: hidden;
    max-width:calc(100%-20px);
}

.jdropdown-searchbar .jdropdown-content > .jdropdown-item:first-child
{
    border-top: 1px solid #e6e6e8;
}

.jdropdown-searchbar .jdropdown-selected
{
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTkgMTYuMTdMNC44MyAxMmwtMS40MiAxLjQxTDkgMTkgMjEgN2wtMS40MS0xLjQxeiIgZmlsbD0iIzAwN2FmZiIvPjwvc3ZnPg==');
    background-repeat:no-repeat;
    background-position:top 50% right 15px;
}

/** List render **/

.jdropdown-list
{
}

.jdropdown-list .jdropdown-container
{
    display:block;
}

.jdropdown-list .jdropdown-header
{
    display:none;
}

.jdropdown-list .jdropdown-group
{
    background-color:#fff;
}

.jdropdown-list .jdropdown-group-name
{
    border-bottom: 1px solid #e6e6e8;
    padding-top:10px;
    padding-bottom:10px;
    font-weight:bold;
}

.jdropdown-list .jdropdown-item
{
    padding-top:10px;
    padding-bottom:10px;
    border-bottom: 1px solid #e6e6e8;
    padding-left:10px;
    padding-right:40px;
    background-color:#fff;
}

.jdropdown-list .jdropdown-selected
{
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTkgMTYuMTdMNC44MyAxMmwtMS40MiAxLjQxTDkgMTkgMjEgN2wtMS40MS0xLjQxeiIgZmlsbD0iIzAwN2FmZiIvPjwvc3ZnPg==');
    background-repeat:no-repeat;
    background-position:top 50% right 10px;
}

@media only screen and (max-device-width : 800px)
{
    .jdropdown-list
    {
        width:100% !important;
        border:0px;
        padding:0px;
    }

    .jdropdown-list .jdropdown-container
    {
        min-width:100%;
    }
}

.app .jdropdown-item {
    text-transform:uppercase;
}

/**
 * (c) jTools Text Editor
 * https://github.com/paulhodel/jtools
 *
 * @author: Paul Hodel <<EMAIL>>
 * @description: Inline richtext editor
 */

 .jeditor-container {
    border:1px solid #ccc;
}

.jeditor-container.jeditor-padding {
    padding:10px;
}

.jeditor {
    outline:none;
    word-break: break-word;

}

.jeditor-container.jeditor-padding .jeditor {
    min-height:120px;
    margin-bottom:10px;
    padding:10px;
}

.jeditor-toolbar {
    cursor: pointer;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-align-items: center;
    align-items: center;
    border: none;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    -webkit-box-shadow: 0 4px 5px 0 rgba(0,0,0,0.14), 0 1px 10px 0 rgba(0,0,0,0.12), 0 2px 4px -1px rgba(0,0,0,0.2);
    box-shadow: 0 4px 5px 0 rgba(0,0,0,0.14), 0 1px 10px 0 rgba(0,0,0,0.12), 0 2px 4px -1px rgba(0,0,0,0.2);
    margin: 0px;
    margin-bottom:5px;
    padding: 5px;
    white-space: nowrap;
    border-radius:2px;
}

.jeditor-toolbar .jeditor-toolbar-group {
    background-color:#f3f3f3;
}

.jeditor-toolbar div {
    padding:4px;
}

.jeditor-toolbar div.jeditor-toolbar-button {
    padding-left:20px;
    padding-right:20px;
}

.jeditor-toolbar div i {
    display:block;
    font-size:20px;
    width:20px;
    height:20px;
    line-height:20px;
    color:#777;
    
    -webkit-user-select: none;  /* Chrome all / Safari all */
    -moz-user-select: none;     /* Firefox all */
    -ms-user-select: none;      /* IE 10+ */
    user-select: none;          /* Likely future */   
}

.jeditor-toolbar select {
    border:0px;
    background-color:transparent;
    padding:0px;
    padding-left:5px;
    padding-right:5px;
    display:block;
    height:32px;
    outline:none;
    border-radius:4px;
    margin:1px;
}

.jeditor-toolbar .jtoolbar-divisor {
    width:2px;
    heigh:28px;
    padding:0px;
    margin:4px;
    background-color:#f2f2f2;
}

.jeditor-toolbar select option {
    background-color:#fff;
    padding:20px;
}

.jeditor-toolbar div:hover, .toolbar select:hover {
    background-color:#f2f2f2;
    border-radius:2px;
}

.jeditor-toolbar div:hover i {
    color:#222;
}

.jeditor-toolbar div.selected {
    background-color:#cecece;
}

.jeditor-thumbs {
    display:inline-block;
    margin-right:5px;
}

.jeditor-thumbs-container {
    margin-top:15px;
}

.jeditor-thumbs .close {
    position:absolute;
    height:1px;
}

.jeditor-thumbs .close i {
    color:#fff;
    text-shadow: 0px 0px 2px #000;
    position:relative;
    top:5px;
    left:72px;
}

.jeditor-thumbs img {
    border:1px solid #eee;
    width:100px;
    height:100px;
}

.jeditor-users {
    position:absolute;
    border:1px solid #ccc;
    background-color:#fff;
    padding-top:5px;
    padding-bottom:5px;
    max-height:220px;
    overflow-y:scroll;
}

.jeditor-users > div
{
    display:flex;
    align-items:center;
    min-width:220px;
    padding:10px;
    padding-top:5px;
    padding-bottom:5px;
}

.jeditor-users > div:hover
{
    background-color:#efefef;
}

.jeditor-users > div img
{
    width:24px;
    height:24px;
    border-radius:12px;
    margin-right:5px;
}

/** Snippet **/

.snippet {
    margin-top:15px;
    cursor:pointer;
    border: 1px solid #eee;
    position:relative;
}

.snippet:focus {
    outline: none;
}

.snippet img {
    width:25%;
    max-width:120px;
    float:left;
    margin-right:10px;
    margin-bottom:10px;
}

.snippet .snippet-title {
    margin-top:15px;
    font-size:1.4em;
}

.snippet .snippet-description {
    margin-top:5px;
    font-size:1em;
}

.snippet .snippet-host {
    margin-top:10px;
    margin-bottom:10px;
    text-transform:uppercase;
    font-size:0.8em;
    color:#777;
    text-align:right;
}

.snippet .snippet-url {
    display:none;
}

.jeditor .snippet:after {
    content:'';
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
    position:absolute;
    top:0;
    right:0;
    margin:14px;
    font-size:24px;
    width:24px;
    height:24px;
    cursor:pointer;
    text-shadow: 0px 0px 5px #fff;
}

.snippet div, .snippet img {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;

    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

.jeditor img {
    border:2px solid transparent;
    box-sizing: border-box;
}

.jeditor img:focus {
    border:2px solid #0096FD;
}

.jeditor .pdf {
    background-image: url("data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Cpath style='fill:%23C30B15;' d='M511.344,274.266C511.77,268.231,512,262.143,512,256C512,114.615,397.385,0,256,0S0,114.615,0,256 c0,117.769,79.53,216.949,187.809,246.801L511.344,274.266z'/%3E%3Cpath style='fill:%2385080E;' d='M511.344,274.266L314.991,77.913L119.096,434.087l68.714,68.714C209.522,508.787,232.385,512,256,512 C391.243,512,501.976,407.125,511.344,274.266z'/%3E%3Cpolygon style='fill:%23FFFFFF;' points='278.328,333.913 255.711,77.913 119.096,77.913 119.096,311.652 '/%3E%3Cpolygon style='fill:%23E8E6E6;' points='392.904,311.652 392.904,155.826 337.252,133.565 314.991,77.913 255.711,77.913 256.067,333.913 '/%3E%3Cpolygon style='fill:%23FFFFFF;' points='314.991,155.826 314.991,77.913 392.904,155.826 '/%3E%3Crect x='119.096' y='311.652' style='fill:%23FC0F1A;' width='273.809' height='122.435'/%3E%3Cg%3E%3Cpath style='fill:%23FFFFFF;' d='M204.871,346.387c13.547,0,21.341,6.659,21.341,18.465c0,12.412-7.795,19.601-21.341,19.601h-9.611 v14.909h-13.471v-52.975L204.871,346.387L204.871,346.387z M195.26,373.858h8.93c5.904,0,9.308-2.952,9.308-8.552 c0-5.525-3.406-8.324-9.308-8.324h-8.93V373.858z'/%3E%3Cpath style='fill:%23FFFFFF;' d='M257.928,346.387c16.649,0,28.152,10.746,28.152,26.487c0,15.666-11.655,26.488-28.683,26.488 h-22.25v-52.975H257.928z M248.619,388.615h9.611c8.249,0,14.151-6.357,14.151-15.665c0-9.384-6.205-15.817-14.757-15.817h-9.006 V388.615z'/%3E%3Cpath style='fill:%23FFFFFF;' d='M308.563,356.982v12.26h23.763v10.596h-23.763v19.525h-13.471v-52.975h39.277v10.595h-25.806 V356.982z'/%3E%3C/g%3E%3C/svg%3E%0A");
    background-repeat: no-repeat;
    background-size: cover;
    width:60px;
    height:60px;
}



/**
 * (c) jLoading
 * https://github.com/paulhodel/jtools
 *
 * @author: Paul Hodel <<EMAIL>>
 * @description: Page loading spin
 */

.jloading {
    position:fixed;
    z-index:10001;
    width:100%;
    left:0;
    right:0;
    top:0;
    bottom:0;
    background-color: rgba(0,0,0,0.7);
}

.jloading::after {
    content:'';
    display:block;
    margin:0 auto;
    margin-top:50vh;
    width:40px;
    height:40px;
    border-style:solid;
    border-color:white;
    border-top-color:transparent;
    border-width:4px;
    border-radius:50%;
    -webkit-animation: spin .8s linear infinite;
    animation: spin .8s linear infinite;
}

.jloading.spin {
    background-color:transparent;
}

.jloading.spin::after {
    margin:0 auto;
    margin-top:80px;
    border-color:#aaa;
    border-top-color:transparent;
}


.jlogin {
    width: 100%;
    box-sizing: border-box;
    margin:0 auto;
    vertical-align:middle;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    font-size:1em;
}

.jlogin-fullscreen {
    position:absolute;
    top:0px;
    left:0px;
    width:100%;
    height:100%;
    background-color:#fff;
    z-index:9000;
}

.jlogin > form {
    max-width:480px;
    padding:20px;
    margin: 0 auto;
}

.jlogin > form > div {
    margin-bottom:10px;
}

.jlogin label {
    display:block;
}

.jlogin > form > div > input {
    width:100%;
    outline:none;
    padding:15px;
    margin:0px;
}

.jlogin input[type='checkbox'] {
    float:left;
}

.jlogin input[type='button'] {
    padding:10px;
    background-color: #2c8ba9;
    border: 1px solid #2c8ba9;
    color: #fff;
    cursor:pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.jlogin span
{
    margin:0 auto;
}
.jlogin img
{
    text-align:center;
    max-width:220px;
}

.jlogin .jlogin-logo
{
    text-align:center;
    padding:20px;
}

.jlogin .captcha
{
    width:100%;
    margin-top:4px;
    margin-bottom:4px;
    border:1px solid #ccc;
    display:block;
}

.jlogin .facebookButton
{
    padding:10px;
    background-color: #2c8ba9;
    border: 1px solid #2c8ba9;
    color: #fff;
    text-align:center;
    background-repeat:no-repeat;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24px' height='24px'%3E%3Cpath d='M19,3H5C3.895,3,3,3.895,3,5v14c0,1.105,0.895,2,2,2h7.621v-6.961h-2.343v-2.725h2.343V9.309 c0-2.324,1.421-3.591,3.495-3.591c0.699-0.002,1.397,0.034,2.092,0.105v2.43h-1.428c-1.13,0-1.35,0.534-1.35,1.322v1.735h2.7 l-0.351,2.725h-2.365V21H19c1.105,0,2-0.895,2-2V5C21,3.895,20.105,3,19,3z' fill='white'/%3E%3C/svg%3E%0A");
    background-position:10px 40%;
    cursor:pointer;
}

.jlogin .rememberButton
{
    padding:10px;
    display:none;
}

.jlogin .requestButton, .jlogin .cancelButton, .jlogin .newProfileButton
{
    padding:20px;
    padding-bottom:0px;
    text-align:center;
    cursor:pointer;
}

.jlogin-captcha img {
    min-width:280px;
}

.jlogin-loading:before {
  display: block;
  position: absolute;
  content: "";
  left: -200px;
  width: 200px;
  height: 4px;
  background-color: #2c8ba9;
  animation: loading 2s linear infinite;
}

@keyframes loading {
    from { left: -200px; width: 30%; }
    50% { width: 30%; }
    70% { width: 70%; }
    80% { left: 50%; }
    95% { left: 120%; }
    to { left: 100%; }
}

:root {
    --active-color:#007aff;
}

@media only screen and (max-device-width : 800px)
{
    .jsuites * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
    }

    html.jsuites {
        height:100%;
    }

    .jsuites body {
        margin:0px;
        padding:0px;
        width:100%;
        height:100%;
        min-height:100%;
        line-height:1.4;
        font-family:-apple-system,SF UI Text,Helvetica Neue,Helvetica,Arial,sans-serif !important;
        background-color:#fff;
    }

    .jsuites input, .jsuites select, .jsuites textarea, .jsuites div {
        font-family:-apple-system,SF UI Text,Helvetica Neue,Helvetica,Arial,sans-serif !important;
    }
}

.japp
{
    height:100%;
    box-sizing: border-box;
    overflow:scroll;
    background-color:#fafafa;
}

.jwarning::before {
    content:'No internet connection';
    background-color:red;
    color:#fff;
    padding:2px;
    position:fixed;
    top:0px;
    left:0px;
    z-index:20000;
    width:100%;
    font-size:0.55em;
    text-align:center;
}

/*
.japp .activeHighLight
{
    color:var(--active-color);
}

.japp .active a, .japp .active span, .japp .active i
{
    color:var(--active-color);
}

.japp .no-padding
{
    padding:0px;
}

.japp .action
{
    color:var(--active-color);
    cursor:pointer;
}


.japp i.action
{
    position:absolute;
    right:0px;
    margin-right:15px;
    cursor:pointer;
}
*/

.japp button
{
    border-radius:4px;
    width:100%;
    border-color:1px solid var(--active-color);
    color:var(--active-color);
    background-color:transparent;
    padding:4px;
    outline:none;
}

.japp button:active
{
    border-color:1px solid #000;
    color:#000;
}

.japp .red
{
    color:red;
    border-color:1px solid red;
}

.japp .link
{
    color:var(--active-color);
    cursor:pointer;
}

.japp .uppercase
{
    text-transform:uppercase;
}

/** Navbar **/

.japp .navbar {
    position:fixed;
    top: 0;
    width: 100%;
    z-index: 500;
    margin: 0;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0,0,0);
    -webkit-box-sizing: border-box;
    backface-visibility: hidden;
    box-sizing: border-box;
    transform: translate3d(0,0,0);
    height: 44px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0,0,0,.1);
}

.jwarning .navbar {
    top:16px;
}

.japp .navbar-container {
    padding: 0 8px;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align:middle;

    display: flex;

    -webkit-flex-flow: row wrap;
    justify-content: space-between;
    align-items:center;
}

.japp .navbar-container div {
    text-align:center;
}

.japp .navbar-container div.title {
    display: flex;
}

.japp .navbar-container div.title div
{
    line-height:24px;
}

.japp .navbar-container div.icon {
    width:24px;
}

.japp .navbar-container div i {
    display:block;
}

.japp .navbar-container div img {
    height:28px;
    display:block;
}

.japp .navbar-container div.title div img {
    border-radius:12px;
    width:24px;
    height:24px;
    margin-right:6px;
}

.japp .navbar .icon {
    color:var(--active-color);
}

.japp .title {
    font-size:1.2em;
}

.japp .block {
    padding:15px;
    color: #6d6d72;
    font-size:0.9em;
    box-sizing: border-box;
}

.japp .block-title {
    text-transform: uppercase;
    color: #6d6d72;
    margin: 25px 15px 10px;
    line-height: 17px;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 1;
    font-size:0.9em;
}

.japp .block-strong {
    background-color:#fff;
    color: #000;
}

.japp .block-border {
    border-top:1px solid #ddd;
    border-bottom:1px solid #ddd; 
}

.japp .block-footer {
	text-transform: uppercase;
    padding:15px;
    color: #6d6d72;
    font-size:0.7em;
}

.japp .block-collapse {
    max-height:100px;
    overflow-y:hidden;
}

.japp .block-instruction {
    text-transform: uppercase;
    padding:15px;
    color: #6d6d72;
    font-size:0.7em;
    text-align:center;
}

.japp .pages {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
    height: 100%;
}

.japp .page {
    padding-top: 45px;
    padding-bottom: 45px;
    min-height: 100%;
    min-width: 100vw;
    box-sizing: border-box;
    overflow-y: auto;
    /*will-change: scroll-position;
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translateZ(0px);
    -webkit-transform: translate3d(0,0,0);
    -webkit-perspective: 1000;*/
}

.warning .page {
    padding-top: 60px;
}

.japp .progressbar-container {
    margin: 15px;
    margin-left: 0px;
    margin-right: 0px;
    margin-top: 5px;
}

.japp .progressbar {
    width:100%;
    border:1px solid var(--active-color);
    border-radius:1px;
}

.japp .progressbar-title {
    margin-bottom:2px;
    text-transform:uppercase;
    display:flex;
    justify-content:space-between;
}

.japp .progressbar div {
    background-color:var(--active-color);
    height:4px;
    width:0%;
}

.japp .panel {
    position:fixed;
    top: 0;
    left: 0;
    margin: 0;
    width:60vw;
    max-width:220px;
    height:100vh;
    background: #fff;
    z-index: 1003;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    box-shadow: 0 -1px 2px rgba(0,0,0,.1);
    padding:4px;
}

.japp .panel-left {
    -webkit-box-shadow: 0px 2px 15px -5px rgba(0, 0, 0, 0.7);
    box-shadow: 0px 2px 15px -5px rgba(0, 0, 0, 0.7);
}

.japp .panel a {
    color:var(--active-color);
}

/** Toolbar **/

.japp .jtoolbar
{
    position:fixed;
    bottom: 0;
    margin: 0;
    left: 0;
    width: 100%;
    background: #f7f7f8;
    z-index: 500;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    box-shadow: 0 -1px 2px rgba(0,0,0,.1);
    padding:4px;
}

.japp .jtoolbar > div
{
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align:middle;

    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -webkit-flex-flow: row wrap;
    justify-content: space-evenly;
    align-items:center;
    width: 100%;
    cursor:pointer;
}

.japp .jtoolbar > div > div
{
    text-align:center;
    flex:1;
    margin:auto;
}

.japp .jtoolbar a
{
    text-decoration:none;
    display:inline-block;
}

.japp .jtoolbar i
{
    color:#929292;
}

.japp .jtoolbar span
{
    font-size:0.7em;
    display:block;
    color:#929292;
}

.japp .jtoolbar .selected a,
.japp .jtoolbar .selected i,
.japp .jtoolbar .selected span
{
    color:var(--active-color);
}


/** Options **/

.japp .options
{
    background-color:#fff;
    border-top:1px solid #e6e6e8;
    border-bottom:1px solid #e6e6e8;
    padding-left:15px;
    margin-top:10px;
}

.japp .options:empty
{
    display:none;
}

.japp .options .option
{
    padding-top:10px;
    padding-bottom:10px;
    text-transform:uppercase;
    font-size:1em;
    border-bottom:1px solid #e6e6e8;
    overflow-x:hidden;

    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align:middle;

    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -webkit-flex-flow: row nowrap;
    justify-content: space-between;
    align-items:center;
    flex-wrap:nowrap;
}

.japp .options .option:last-child
{
    border-bottom:0px;
}

.japp .options label
{
    width:100%;
}

.japp .options input[type='text'], .japp .options textarea
{
    outline:none;
    border:1px solid transparent;
    font-size:1em;
    margin:0px;
    padding:0px;
    width:100%;
    box-sizing:border-box;
}

.japp .options textarea
{
    height:100px;
}

.japp .options input[type='checkbox'], .japp .options input[type='radio']
{
    visibility: hidden;
}

.japp .options input[type='checkbox'] ~ i, .japp .options input[type='radio'] ~ i
{
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTkgMTYuMTdMNC44MyAxMmwtMS40MiAxLjQxTDkgMTkgMjEgN2wtMS40MS0xLjQxeiIgZmlsbD0iIzAwN2FmZiIvPjwvc3ZnPg==');
    background-repeat: no-repeat;
    content:'';
    visibility: hidden;
    margin-right:10px;
    line-height:24px;
    width:24px;
    height:24px;
}

.japp .options input[type='checkbox']:checked ~ i, .japp .options input[type='radio']:checked  ~ i
{
    visibility: visible;
}

.japp .options .option .jdropdown
{
     border-top: 0px solid #e6e6e8;
    border-bottom: 0px solid #e6e6e8;
    padding-top: 0px;
    padding-bottom: 0px;
}

.japp .options .icon {
    float:left;
    margin-right:10px;
    max-width:40px;
    max-height:40px;
    border-radius:20px;
    color:#929292;
}

.japp .options .option .option-actions {
    display:flex;
    transform: translateX(100%);
    width:0px;
}

.japp .options .option .option-actions > div {
    padding-right:5px;
}

.japp .options .option .option-actions > div > i {
    width:40px;
    height:40px;
    color:#fff;
    font-size:24px;
    line-height:40px;
    text-align:center;
    border-radius:30px;
    background-color:red;
}

.japp .options .option .option-actions.small > div > i
{
    color:#000;
    width:24px;
    height:24px;
    font-size:24px;
    line-height:24px;
    border-radius:24px;
    background-color:transparent;
}

.japp .options .option-title {
    display:block;
    padding-top:10px;
    padding-bottom:10px;
    text-transform:uppercase;
    font-size:1em;
    vertical-align:center;
}

.japp .options .option-title::after {
    content:'\e313';
    font-family: 'material icons';
    font-size:24px;
    margin-right:10px;
    float:right;
    width:24px;
    height:24px;
    line-height:24px;
}

.japp .options .option-title.selected::after
{
    content:'\e316';
}

.japp .options .option-title.selected ~ div {
    display:block;
}

.japp .options .option-group {
    border-top:1px solid #e6e6e8;
    background-color:#fff;
    display:none;
}

.japp .options .option-link:after {
    content:'\e315';
    font-family: 'material icons';
    color:var(--active-color);
    font-size:24px;
    margin-right:10px;
    float:right;
    width:24px;
    height:24px;
    line-height:24px;
    display:block;
}

.japp .options .option-header {
    padding-left:10px;
    padding-right:10px;
    flex-grow:10;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.japp .options .option .option-name {
    font-size:1em;
    text-overflow: ellipsis;
    width: 100%;
    overflow: hidden;
}

.japp .options .option .option-small {
    font-size:0.7em;
    color: #6d6d72;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display:block;
}

.japp .options .option .option-image img {
    width:40px;
    height:40px;
    border-radius:20px;
    display:block;
}

.japp .options .option .option-image-small img {
    width:24px;
    height:auto;
    border-radius:50px;
    display:block;
}

.japp .options .option .option-image .option-badge {
    position: absolute;
    background-color: red;
    color: #fff;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border-radius: 16px;
    text-align: center;
    font-size: 7px;
    margin: 28px;
}

.japp .options .option .option-image-small .option-badge {
    position: absolute;
    background-color: red;
    color: #fff;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border-radius: 16px;
    text-align: center;
    font-size: 7px;
    margin: 12px;
}

.japp .options .option .option-badge.solid {
    background-color: red;
    color: red;
}

.japp .options .option .option-badge.solid-green {
    background-color: green;
    color: green;
}

.japp .options .option .option-badge:empty {
    display:none;
}

.japp .options .option .option-date {
    float:right;
    font-size:0.6em;
    color:#888;
    margin-right:15px;
    white-space: nowrap;
}

.japp .options .option .option-right {
    margin-right:15px;
}

.japp .options .option .option-badget {
    background-color:red;
    border-radius:12px;
    width:24px;
    height:24px;
    line-height:24px;
    font-size:0.7em;
    color:#fff;
    text-align:center;
}

.japp .options .option .option-badget:empty {
    display:none;
}

/** Badge **/

.japp .jbadge
{
    position:relative;
    display:inline-block;
    top:-12px;
    left:-12px;
}

.japp .jbadge > div
{
    background-color:red;
    color:#fff;
    width:16px;
    height:16px;
    line-height:16px;
    border-radius:16px;
    text-align:center;
    font-size:8px;
    position:absolute;
}

.japp .jbadge > div:empty
{
    display:none;
}

/** Picker **/

.japp .picker 
{
    background-color:#fff;
    border-top:1px solid #e6e6e8;
    border-bottom:1px solid #e6e6e8;
    padding-left:15px;
    margin-top:10px;
    position:absolute;
    bottom:0px;
    width:100%;
    z-index:700;
}

.japp .picker .picker-options
{
    -webkit-mask-image: linear-gradient(to bottom, rgba(0,0,0,1), rgba(0,0,0,.2)), linear-gradient(to top, rgba(0,0,0,1), rgba(0,0,0,.2));
    -webkit-mask-size: 100% 50%;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center bottom, top center;
    overflow-y:scroll;
    padding-top:100px;
    padding-bottom:100px;
}

.japp .picker label
{
    display: block;
    padding-top: 10px;
    padding-bottom: 10px;
}

.japp .picker .picker-selected
{
    border-top:1px solid #e6e6e8;
    border-bottom:1px solid #e6e6e8;
}

.japp .picker input
{
    display:none;
}

/** Topsearch **/

.japp .top-search {
    margin:0px;
    padding:10px;
    border-bottom:1px solid #ddd; 
}

.japp .top-search input {
    background-color: #e6e6e6;
    border: 0px;
    border-radius: 4px;
    outline: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='gray'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: 5px center;
    padding-left: 32px;
    width: 100%;
}

.japp .searchbar-container i {
    position:absolute;
    margin-top:5px;
    margin-left:8px;
    color:#bbb;
}

/** Range **/

.japp .range
{
  -webkit-appearance: none;
  margin: 18px 0;
  width: 100%;
}

.japp .range:focus
{
  outline: none;
}

.japp .range::-webkit-slider-runnable-track
{
    width: 100%;
    height: 4px;
    cursor: pointer;
    animate: 0.2s;
    background: #ccc;
    border-radius: 1.3px;
}

.japp .range::-webkit-slider-thumb
{
    -webkit-appearance:none;
    appearance:none;
    width:24px;
    height:24px;
    background:#fff;
    cursor:pointer;
    border-radius:24px;
    margin-top:-10px;
    border:0px;
    box-shadow:0px 1px 3px 1px #bbb;
}

.japp .range::-moz-range-track
{
    width:100%;
    cursor:pointer;
    animate:0.2s;
    background:#ccc;
    border-radius:1.3px;
}

.japp .range::-moz-range-thumb
{
    width:24px;
    height:24px;
    background:#fff;
    cursor:pointer;
    border-radius:24px;
    border:0px;
    box-shadow:0px 1px 3px 1px #bbb;
}

.japp .range::-ms-track
{
    width: 100%;
    cursor: pointer;
    animate: 0.2s;
    background: transparent;
    border-color: transparent;
    border-width: 16px 0;
    color: transparent;
    border-radius:1.3px;
}

.japp .range::-ms-fill-lower
{
    background:#ccc;
    height:5px;
}

.japp .range::-ms-fill-upper
{
    background:#ccc;
    height:5px;
}

.japp .range::-ms-thumb {
    width:24px;
    height:24px;
    background:#fff;
    cursor:pointer;
    border-radius:24px;
    border:0px;
    box-shadow:0px 1px 3px 1px #bbb;
}

.japp .range:focus::-ms-fill-lower
{
  background: #ccc;
}

.japp .range:focus::-ms-fill-upper
{
  background: #ccc;
}


/** Actionsheet **/

.jactionsheet
{
    position:fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    min-height:100%;
    z-index:10000;

    background-color:rgba(0,0,0,0.5);
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
    display: flex;
    -ms-flex-align: baseline;
    -webkit-align-items: baseline;
    -webkit-box-align: baseline;

    align-items: baseline;
}

.jactionsheet .jactionsheet-content
{
    width:100%;
    align-self: flex-end;
}

.jactionsheet .jactionsheet-title
{
    font-size:1em;
    font-weight:bold;
    padding:8px;
}

.jactionsheet .jactionsheet-message
{
    padding:4px;
}

.jactionsheet .jactionsheet-group
{
    box-sizing: border-box;
    margin:10px;
    background-color:#fff;
    border-radius:5px;
}

.jactionsheet .jactionsheet-group > div
{
    font-size:1.4em;
    font-weight:bold;
    padding:4px;
    border-top:1px solid #ccc;
    text-align:center;
    width:100%;
}
.jactionsheet .jactionsheet-group > div:first-child
{
    border-top:0px;
}

.jactionsheet .jactionsheet-group input
{
    border:0px;
    margin:0px;
    background-color:transparent;
    color:var(--active-color);
    width:100%;
    outline:none;
    font-size:1em;
}

.jactionsheet .jactionsheet-group input.jactionsheet-cancel
{
    color:red;
}

/** Toggle **/

.jtoggle {
    display: inline-block;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

.jtoggle i {
    position: relative;
    display: inline-block;
    margin-right: .5rem;
    border-radius: 23px;
    vertical-align: text-bottom;
    transition: all 0.3s linear;
    width: 46px !important;
    height: 27px !important;
    background: #e6e6e6 !important;
    visibility: visible !important;
}

.jtoggle i::before {
  content: "";
  position: absolute;
  left: 0;
  width: 42px;
  height: 22px;
  background-color: #fff;
  border-radius: 11px;
  transform: translate3d(2px, 2px, 0) scale3d(1, 1, 1);
  transition: all 0.25s linear;
}

.jtoggle i::after {
  content: "";
  position: absolute;
  left: 0;
  width: 22px;
  height: 22px;
  background-color: #fff;
  border-radius: 11px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.24);
  transform: translate3d(2px, 2px, 0);
  transition: all 0.2s ease-in-out;
}

.jtoggle:active i::after {
  width: 28px;
  transform: translate3d(2px, 2px, 0);
}

.jtoggle:active input:checked + i::after {
    transform: translate3d(16px, 2px, 0);
}

.jtoggle input {
    display: none;
}

.jtoggle input:checked + i {
    background-color: #4BD763 !important;
}

.jtoggle input:checked + i::before {
    transform: translate3d(18px, 2px, 0) scale3d(0, 0, 0);
}

.jtoggle input:checked + i::after {
    transform: translate3d(22px, 2px, 0);
}

/** Placeholders **/

.japp .options input::-webkit-input-placeholder
{
    text-tranform:uppercase;
}

.japp .options input::-moz-placeholder
{
    text-tranform:uppercase;
}

.japp .options input:-ms-input-placeholder
{
    text-tranform:uppercase;
}

.japp .options input:-moz-placeholder
{
    text-tranform:uppercase;
}

::placeholder
{
    color: #cccccc;
    opacity: 1;
}

:-ms-input-placeholder
{
    color: #cccccc;
}

::-ms-input-placeholder
{
    color: #cccccc;
}

::-webkit-input-placeholder
{
    color: #cccccc;
}


/**
 * (c) jTools Modal page
 * https://github.com/paulhodel/jtools
 *
 * @author: Paul Hodel <<EMAIL>>
 * @description: Modal page
 */

.jmodal {
    position:fixed;
    top:50%;
    left:50%;
    width:60%;
    height:60%;
    -webkit-box-shadow: 0 2px 10px rgba(0,0,0,.2);
    -moz-box-shadow: 0 2px 10px rgba(0,0,0,.2);
    border:1px solid #ccc;
    background-color:#fff;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    padding-top:50px;
    z-index:9002;
    display:none;
    border-radius:5px;
}

.jmodal:before {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    content:attr(title);
    padding:15px;
    box-sizing: border-box;
    background: #e3e3e3;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ededed', endColorstr='#e3e3e3');
    background: -webkit-gradient(linear, left top, left bottom, from(#ededed), to(#e3e3e3));
    background: -moz-linear-gradient(top,  #ededed,  #e3e3e3);
    font-size:1.2em;
}

.jmodal > div {
    padding:20px;
    overflow-y:auto;
    max-height:100%;
}
.jmodal.no-title {
    padding-top:0px;
}

.jmodal.no-title:before {
    display:none;
}

.jmodal:after {
    content:'';
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
    position:absolute;
    top:0;
    right:0;
    margin:14px;
    font-size:24px;
    width:24px;
    height:24px;
    cursor:pointer;
    text-shadow: 0px 0px 5px #fff;
}

.jnotification {
    position: fixed;
    z-index:10000;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px;
}

.jnotification-container {
    -webkit-box-shadow: 0px 2px 15px -5px rgba(0, 0, 0, 0.7);
    box-shadow: 0px 2px 15px -5px rgba(0, 0, 0, 0.7);
    padding:12px;
    border-radius: 12px;
}

.jnotification-close {
    content:'';
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='gray'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
    font-size:20px;
    width:20px;
    height:20px;
    cursor:pointer;
}

.jnotification-title {
    font-weight:bold;
}

.jnotification-header {
    display:flex;
    padding-bottom:5px;
}

.jnotification-header:empty {
    display:none;
}

.jnotification-image {
    margin-right: 5px;
}

.jnotification-image:empty {
    display:none;
}

.jnotification-image img {
    width:24px;
}

.jnotification-name {
    text-transform: uppercase;
    font-size:0.9em;
    flex:1;
}

@media (min-width: 320px) and (max-width: 800px) {
    .jnotification {
        top: 0px;
        width: 100%;
    }
    .jnotification-container {
        background: rgb(255,255,255,0.95);
        border: 1px solid #eee;
    }
}

@media (min-width: 801px) {
    .jnotification {
        bottom: 0px;
    }
    .jnotification-container {
        backgroun-color: #000;
        background: rgb(92,92,92);
        background: linear-gradient(0deg, rgba(92,92,92,1) 0%, rgba(77,77,77,1) 100%);
        color: #fff;
        width: 320px;
        margin: 30px;
        padding: 20px;
    }
}

.jnotification-header {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.jrating {
    display:flex;
}
.jrating > div {
    width:24px;
    height:24px;
    line-height:24px;
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z' fill='gray'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
}

.jrating .jrating-over {
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='gray'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
}

.jrating .jrating-selected {
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='red'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
}


/**
 * (c) Image slider
 * https://github.com/paulhodel/jtools
 *
 * @author: Paul Hodel <<EMAIL>>
 * @description: Image Slider
 */

.jslider {
    margin-top:10px;
    margin-bottom:10px;
}

.jslider-container img {
    width:60px;
    margin-right:5px;
    margin-bottom:5px;
}

.jslider-attach {
    cursor:pointer;
}

.jslider-left::before {
    content:'arrow_back_ios';
    color:#fff;
    position:absolute;
    left:15px;
    font-family:'Material Icons';
    font-size:30px;
    text-shadow: 0px 0px 0px #000;
}

.jslider-right::after {
    content:'arrow_forward_ios';
    color:#fff;
    position:absolute;
    right:10px;
    font-family:'Material Icons';
    font-size:30px;
    text-shadow: 0px 0px 0px #000;
    text-align:center;
}

.jslider-close {
    width:24px;
    height:24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3C/svg%3E");
    position:fixed;
    top:15px;
    right:15px;
    display:none;
    cursor:pointer;
    z-index:3000;
}

.jslider-preview {
    position:fixed;
    left:0;
    top:0;
    width: 100%;
    min-height:100%;
    z-index:2000;
    margin:0px;
    box-sizing:border-box;

    background-color:rgba(0,0,0,0.9);
    -webkit-transition-duration: .4s;
    transition-duration: .4s;
    display: flex;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -webkit-box-align: center;

    align-items: center;
}

.jslider-preview img {
    min-width:70%;
    max-width:100%;
    height:auto;
    box-sizing: border-box;
    margin:0 auto;
    vertical-align:middle;
    display:none;
}


.jtabs {
    position:relative;
    max-width: calc(100vw - 24px);
}
.jtabs > .jtabs-headers {
    display:flex;
    padding:6px;
    overflow-x:auto;
    width:100%;
}

.jtabs > .jtabs-headers > div {
    padding:10px;
    padding-left:20px;
    padding-right:20px;
    margin-right:5px;
    margin-bottom:5px;
    background-color:#f4f4f4;
    cursor:pointer;
}

.jtabs > .jtabs-headers > div.jtabs-selected {
    background-color:#eee;
    border-bottom:2px solid black;
}

.jtabs > .jtabs-content > div {
    display:none;
    padding:10px;
}

.jtabs > .jtabs-content > div.jtabs-selected {
    display:block;
}


