.account_table {
    table-layout: fixed!important;
}
.account_table th {
    padding: 10px;
}
.account_table td th {
    border-style: solid;
    border-width: 1px;
    border-color: #f0f0f0; /* 淡灰色 */
}

.account_table th span {
    text-align: center !important;
}


.account_table tr th:first-child {
    width: 30px;
}

.account_table tr td:first-child {
    width: 30px;
}

.account_table tr th,
.account_table tr td {
    vertical-align: middle;
    border-collapse: collapse;
    border: 1px #a29d9d solid;
}

.account_table tr .o_list_record_remove {
    width: 50px;
}

.account_table .o_data_row td {
    border: 1px #a29d9d solid !important;
}

.account_table tr th {
    font-weight: 500;
    /*font-size: 24px;*/
}

.account_table tr td {
    height: 48px;
    line-height: 48px;
}

.account_table .o_field_x2many_list_row_add {
    text-align: left;
    font-size: 14px;
}

.account_table tr  td {
    font-size: 14px;
}

.account_table tr input {
    font-size: 14px;
}

.account_table tr select {
    font-size: 14px;
    width: 100%;
    height: 100%;
    outline: none;
    text-align: center;
}