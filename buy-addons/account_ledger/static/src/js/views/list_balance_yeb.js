odoo.define('account_ledger.BalanceListViewYeb', function (require) {
    "use strict";
    var relational_fields = require('web.relational_fields');
    var ListFieldMany2One = relational_fields.ListFieldMany2One;
    var fieldRegistry = require('web.field_registry');
    var core = require('web.core');
    var field_utils = require('web.field_utils');

    var ListController = require('web.ListController');
    var ListRenderer = require('web.ListRenderer');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');


    var BalanceListRenderer = ListRenderer.extend({

    });
    // 科目余额表查询结果列表视图
    var BalanceListControllerYeb = ListController.extend({
        /**
         * @private
         */
        _onOpenRecord: function (event) {
            event.stopPropagation();
            var self = this;
            var record = this.model.get(event.data.id, {raw: true});
            this._rpc({
                model: 'account.move.line',
                method: 'action_move_line_list_balance',
                args: [[record.data.id],record.data.account_id,record.data.company_id,record.data.partner_id,
                record.data.date_start, record.data.date_end],
            }).then(function (res) {
                    self.do_action(res);
                }
            );
        },
    });

    var BalanceListViewYeb = ListView.extend({

        config: _.extend({}, ListView.prototype.config, {
            Controller: BalanceListControllerYeb,
        }),

    });

    viewRegistry.add('balance_list_yeb', BalanceListViewYeb);

    return BalanceListViewYeb
});