odoo.define('account_ledger.BalanceListView', function (require) {
    "use strict";
    var ListController = require('web.ListController');
    var ListRenderer = require('web.ListRenderer');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');

    var BalanceListRenderer = ListRenderer.extend({

    });

    // 余额表查询后，列表视图扩展
    var BalanceListController = ListController.extend({

        renderButtons: function ($node) {
            this._super.apply(this, arguments);

            // 添加按钮
            this.$buttons.append($('<button class="btn btn-primary o_list_button_query">查询</button>'));
            this.$buttons.append($('<button class="btn btn-secondary o_list_button_print">打印</button>'));

            // 绑定事件
            this.$buttons.on('click', '.o_list_button_query', this._onLedgerQuery.bind(this));
            this.$buttons.on('click', '.o_list_button_print', this._onLedgerPrint.bind(this));
        },

        _onLedgerQuery: function (event) {
            if (event) {
                event.stopPropagation();
            }

            this.do_action({
                type: 'ir.actions.act_window',
                name: '科目余额表查询',
                res_model: 'balance.inquiry.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {default_model: this.modelName},
            });
        },

        // 余额表调用打印方法
        _onLedgerPrint: function (event) {
            if (event) {
                event.stopPropagation();
            }
            var self = this;
            var record = this.getSelectedIds();
            var displayName = this._title;
            this._rpc({
                model: 'account.balance.temp',
                method: 'print_quotation',
                args: [record, displayName],
            }).then(function (res){
                    if (res){
                        self.do_action(res);
                    }
                });
        },

        ///////////////////////////////////////////////////////////////
        _onOpenRecord: function (event) {
            event.stopPropagation();
            var self = this;
            var record = this.model.get(event.data.id, {raw: true});
            this._rpc({
                model: 'account.balance.temp',
                method: 'action_move_line_list_yeb',
                args: [[record.data.id,]],
            }).then(function (res) {
                    if (res){
                        self.do_action(res);
                    }else {
                        self._rpc({
                            model: 'account.balance.temp',
                            method: 'action_move_line_list',
                            args: [[record.data.id,]],
                        }).then(function (res) {
                            self.do_action(res);
                            }
                        );
                    }

                }
            );
        },

        ////////////////////////////////////////////////////////////////////////
    });

    var BalanceListView = ListView.extend({

        config: _.extend({}, ListView.prototype.config, {
            Renderer: BalanceListRenderer,
            Controller: BalanceListController,
        }),

    });

    viewRegistry.add('balance_list', BalanceListView);
    return BalanceListView
});