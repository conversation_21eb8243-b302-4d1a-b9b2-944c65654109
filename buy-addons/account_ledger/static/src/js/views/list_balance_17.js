/** @odoo-module **/
import {registry} from "@web/core/registry";
import {listView} from "@web/views/list/list_view";
import {ListController} from "@web/views/list/list_controller";
import {useService} from "@web/core/utils/hooks";

export class ListBalanceButtons extends ListController {

    setup() {

        this.action = useService("action");
        this.orm = useService("orm");
        super.setup();
    }

    _onSearchQuery(event) {
        if (event) {
            event.stopPropagation();
        }
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '科目余额表查询',
            res_model: 'balance.inquiry.wizard',
            views: [[false, 'form']],
            target: 'new',
            context: {default_model: this.props.resModel},
        });
    }

    // 余额表调用打印方法
    async _onPrint(event) {
        if (event) {
            event.stopPropagation();
        }
        var self = this;
        const resIds = await this.getSelectedResIds();
        // var displayName = this._title;
        var res = await self.orm.call(
            'account.balance.temp',
            'print_quotation',
            [resIds, '科目余额表'],
        )

        if (res) {
            self.action.doAction(res);
        }
    }

    async openRecord(record) {
        var self = this
        var record_id = record.resId
        self.orm.call(
            'account.balance.temp',
            'action_move_line_list_yeb',
            [[record_id,]],
        ).then(function (res) {
            if (res) {
                self.action.doAction(res);
            } else {
                self.orm.call(
                    'account.balance.temp',
                    'action_move_line_list',
                    [[record_id,]],
                ).then(function (res) {
                        self.action.doAction(res);
                    }
                );
            }
        })
    }

}

export const ListBalanceButtonsView = {
    ...listView,
    Controller: ListBalanceButtons,
    buttonTemplate: "ListReportButtons",
}

registry.category("views").add("balance_list", ListBalanceButtonsView);
