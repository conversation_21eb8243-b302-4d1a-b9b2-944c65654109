odoo.define('account_ledger.DetailListView', function (require) {
    "use strict";

    var ListController = require('web.ListController');
    var ListRenderer = require('web.ListRenderer');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');

    // 明细账列表视图
    var DetailListRenderer = ListRenderer.extend({

        _renderAggregateCells: function (aggregateValues, isHeader) {

            return _.map(this.columns, function (column, index) {
                if (isHeader  && index === 0) {
                    return;
                }
                return $('<td>');
            });
        },
    });

    var DetailListController = ListController.extend({

        renderButtons: function ($node) {
            this._super.apply(this, arguments);

            // 添加按钮
            this.$buttons.append($('<button class="btn btn-primary o_list_button_query">查询</button>'));
            this.$buttons.append($('<button class="btn btn-secondary o_list_button_print">打印</button>'));

            // 绑定事件
            this.$buttons.on('click', '.o_list_button_query', this._onDetailQuery.bind(this));
            this.$buttons.on('click', '.o_list_button_print', this._onDetailPrint.bind(this));
        },

        _onDetailQuery: function (event) {

            if (event) {
                event.stopPropagation();
            }

            this.do_action({
                type: 'ir.actions.act_window',
                name: '明细账查询',
                res_model: 'detail.inquiry.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {},
            });
        },

        _onDetailPrint: function (event) {
            if (event) {
                event.stopPropagation();
            }

            this.do_action({
                type: 'ir.actions.act_window',
                name: '明细帐打印',
                res_model: 'detail.print.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {},
            });
        }
    });

    var DetailListView = ListView.extend({

        config: _.extend({}, ListView.prototype.config, {
            Renderer: DetailListRenderer,
            Controller: DetailListController,
        }),
    });

    viewRegistry.add('detail_list', DetailListView);
    return DetailListView
});
