/** @odoo-module **/
import {registry} from "@web/core/registry";
import {listView} from "@web/views/list/list_view";
import {ListController} from "@web/views/list/list_controller";
import {useService} from "@web/core/utils/hooks";

export class BalanceListControllerYeb extends ListController {

    setup() {

        this.action = useService("action");
        this.orm = useService("orm");
        super.setup();
    }


    async openRecord(record) {
        var self = this
        var record_id = record.resId
        var account_id = record.data.account_id[0]
        var company_id = record.data.company_id[0]
        var partner_id = record.data.partner_id[0]
        var date_start = record.data.date_start
        var date_end = record.data.date_end
        self.orm.call(
            'account.move.line',
            'action_move_line_list_balance',
            [[record_id], account_id, company_id, partner_id, date_start, date_end],
        ).then(function (res) {
            self.action.doAction(res);

        })
    }

}

export const BalanceListControllerYebView = {
    ...listView,
    Controller: BalanceListControllerYeb,
}

registry.category("views").add("balance_list_yeb", BalanceListControllerYebView);
