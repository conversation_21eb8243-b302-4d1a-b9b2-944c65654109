/** @odoo-module **/

import {registry} from "@web/core/registry";

const {Component} = owl;

export class AccountTable extends Component {
    static template = 'account_ledger.account_table'

    setup() {
        this.props.value
        this.state = [
            {
                value: 'cash',
                name: "现金"
            },
            {
                value: "bank",
                name: '银行'
            },
            {
                value: "other",
                name: '其他'
            }
        ]
        console.log(this)
        super.setup();
    }

    _add_line(ev) {
        ev.preventDefault()
        const parentNode = ev.target.parentNode
        console.log(parentNode)


        const tr = document.createElement("tr")
        document.createElement("")
        console.log('click', ev)
    }
}

export const AccountTableField = {
    component:AccountTable
}

registry.category("fields").add("account_table", AccountTableField);