odoo.define('account_ledger.LedgerListView', function (require) {
    "use strict";
    var ListController = require('web.ListController');
    var ListRenderer = require('web.ListRenderer');
    var ListView = require('web.ListView');
    var viewRegistry = require('web.view_registry');

    //总账列表视图
    var LedgerListRenderer = ListRenderer.extend({

        _renderAggregateCells: function (aggregateValues, isHeader) {

            return _.map(this.columns, function (column, index) {
                if (isHeader  && index === 0) {
                    return;
                }
                return $('<td>');
            });
        },
    });

    var LedgerListController = ListController.extend({

        /**
         * Renders the button element associated to the given node and record.
         *
         * @private
         * @param {Object} record
         * @param {Object} node
         * @returns {jQuery} a <button> element
         */
        renderButtons: function ($node) {
            this._super.apply(this, arguments);

            // 添加按钮
            this.$buttons.append($('<button class="btn btn-primary o_list_button_query">查询</button>'));
            this.$buttons.append($('<button class="btn btn-secondary o_list_button_print">打印</button>'));

            // 绑定事件
            this.$buttons.on('click', '.o_list_button_query', this._onLedgerQuery.bind(this));
            this.$buttons.on('click', '.o_list_button_print', this._onLedgerPrint.bind(this));
        },

        /**
         * When the user clicks on the 'Import Attachments' button. We
         * can switch to the Import Attachments's wizard form view with no active res_id
         *
         * @private
         * @param {MouseEvent} event
         */
        _onLedgerQuery: function (event) {

            if (event) {
                event.stopPropagation();
            }

            this.do_action({
                type: 'ir.actions.act_window',
                name: '总账查询：',
                res_model: 'ledger.inquiry.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {default_model: this.modelName},
            });
        },

        _onLedgerPrint: function (event) {
            if (event) {
                event.stopPropagation();
            }

            this.do_action({
                type: 'ir.actions.act_window',
                name: '总账打印预览：',
                res_model: 'ledger.print.wizard',
                views: [[false, 'form']],
                target: 'new',
                context: {default_model: this.modelName},
            });
        },

        _onOpenRecord: function (event) {
            event.stopPropagation();
            var self = this;
            var record = this.model.get(event.data.id, {raw: true});
            this._rpc({
                model: 'account.account',
                method: 'get_descendant_ids',
                args: [record.data['account_id']],
            }).then(function (account_ids) {
                self.do_action({
                    type: 'ir.actions.act_window',
                    res_model: 'account.move.line',
                    name: '日记账项目',
                    views: [[false, 'list'], [false, 'form']],
                    domain: [['fr_period_id', '=', record.data['period_id']], ['account_id', 'in', account_ids], ['fr_state', '=', 'posted']],
                    context: {'create': false},
                    });
                });
            },
    });

    var LedgerListView = ListView.extend({

        config: _.extend({}, ListView.prototype.config, {
            Renderer: LedgerListRenderer,
            Controller: LedgerListController,
        }),

    });

    viewRegistry.add('ledger_list', LedgerListView);
    return LedgerListView
});