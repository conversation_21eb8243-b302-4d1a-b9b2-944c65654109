import {useService} from "@web/core/utils/hooks";
import {registry} from "@web/core/registry";

const {Component} = owl;

export class AccountDateSearch extends Component {
    setup() {
        super.setup();
        this.rpc = useService('rpc');
        this.rpc
    }


    /**
     * 获取会计期间
     * @returns {Promise<*>}
     * @private
     */
    async _get_all_account_date() {
        let data = await this.rpc('/web/dataset/call_button', {
            model: 'payment.details',
            method: 'get_year_span',
            args: [[]],
            kwargs: {},
        });
        return await span
    }
}
