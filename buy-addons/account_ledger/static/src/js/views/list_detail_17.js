/** @odoo-module **/
import {registry} from "@web/core/registry";
import {listView} from "@web/views/list/list_view";
import {ListController} from "@web/views/list/list_controller";
import {useService} from "@web/core/utils/hooks";

export class ListDetailButtons extends ListController {

    setup() {

        this.action = useService("action");
        this.orm = useService("orm");
        super.setup();
    }

    _onSearchQuery(event) {
        if (event) {
            event.stopPropagation();
        }
       this.action.doAction({
            type: 'ir.actions.act_window',
            name: '明细账查询',
            res_model: 'detail.inquiry.wizard',
            views: [[false, 'form']],
            target: 'new',
            context: {},
        });
    }

    // 余额表调用打印方法
    async _onPrint(event) {
        if (event) {
            event.stopPropagation();
        }

        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '明细帐打印',
            res_model: 'detail.print.wizard',
            views: [[false, 'form']],
            target: 'new',
            context: {},
        });
    }

}

export const ListDetailButtonsView = {
    ...listView,
    Controller: ListDetailButtons,
    buttonTemplate: "ListReportButtons",
}

registry.category("views").add("detail_list", ListDetailButtonsView);