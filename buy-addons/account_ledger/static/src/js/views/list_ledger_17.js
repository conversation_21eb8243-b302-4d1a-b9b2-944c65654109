/** @odoo-module **/
import {registry} from "@web/core/registry";
import {listView} from "@web/views/list/list_view";
import {ListController} from "@web/views/list/list_controller";
import {useService} from "@web/core/utils/hooks";

export class ListLedgerButtons extends ListController {

    setup() {

        this.action = useService("action");
        this.orm = useService("orm");
        super.setup();
    }

    _onSearchQuery(event) {
        if (event) {
            event.stopPropagation();
        }

        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '总账查询：',
            res_model: 'ledger.inquiry.wizard',
            views: [[false, 'form']],
            target: 'new',
            context: {default_model: this.props.resModel},
        });
    }

    // 余额表调用打印方法
    async _onPrint(event) {
        if (event) {
            event.stopPropagation();
        }

        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '总账打印预览：',
            res_model: 'ledger.print.wizard',
            views: [[false, 'form']],
            target: 'new',
            context: {default_model: this.props.resModel},
        });
    }

    async openRecord(record) {
        var self = this
        var account_id = record.data.account_id[0]
        var period_id = record.data.period_id[0]
        self.orm.call(
            'account.account',
            'get_descendant_ids',
            [[account_id,]],
        ).then(function (account_ids) {
            debugger
            self.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'account.move.line',
                name: '日记账项目',
                views: [[false, 'list'], [false, 'form']],
                domain: [['fr_period_id', '=', period_id], ['account_id', 'in', account_ids], ['fr_state', '=', 'posted']],
                context: {'create': false},
            });
        })
    }

}

export const ListLedgerButtonsView = {
    ...listView,
    Controller: ListLedgerButtons,
    buttonTemplate: "ListReportButtons",
}

registry.category("views").add("ledger_list", ListLedgerButtonsView);