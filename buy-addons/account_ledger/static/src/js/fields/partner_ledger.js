/** @odoo-module **/

import {registry} from "@web/core/registry";
import {Many2OneField, many2OneField} from "@web/views/fields/many2one/many2one_field";

export class PartnerLedgerMany2One extends Many2OneField {

    // onClick(ev) {
    //     if (this.props.canOpen && this.props.readonly) {
    //         ev.preventDefault();
    //         ev.stopPropagation();
    //         this.action.doAction({
    //             'type': 'ir.actions.client',
    //             'name': '业务伙伴分类账',
    //             'tag': 'account_report',
    //             'target': 'new',
    //             // 'options': {'partner_ids': [this.props.value.id]},
    //             'options': {'partner_ids': [this.props.record.data.partner_id[0]]},
    //             'ignore_session': 'both',
    //             'context': "{'model':'account.partner.ledger'}"
    //         })
    //     }
    // }
}

export const PartnerLedgerMany2OneField = {
    ...many2OneField,
    component: PartnerLedgerMany2One
}
registry.category("fields").add("partner_ledger", PartnerLedgerMany2OneField);