odoo.define('fr_ledger.Many2oneLimit', function (require) {
    "use strict";

    var relational_fields = require('web.relational_fields');
    var FieldMany2One = relational_fields.FieldMany2One;

    return FieldMany2One.include({
        init: function (parent, name, record, options) {
            this._super.apply(this, arguments);
            this.limit = this.nodeOptions.limit || 7;
        },
    });
});