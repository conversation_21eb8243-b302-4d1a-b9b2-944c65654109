/** @odoo-module **/

import {registry} from "@web/core/registry";
import {Component} from "@odoo/owl";

export class MinusMonetary extends Component {
    static template = "account_ledger.minus_monetary"

    setup() {
        super.setup();
    }

    get value() {
        return this.props.record.data[this.props.name] || 0
    }
}

export const MinusMonetaryField = {
    component: MinusMonetary,
}
registry.category("fields").add("minus_monetary", MinusMonetaryField);