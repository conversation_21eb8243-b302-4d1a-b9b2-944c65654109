odoo.define('fr_ledger.analytic_tags', ['web.AbstractField', 'web.relational_fields', 'web.field_registry', 'web.core', 'web.field_utils'], function (require) {
    "use strict";
    var AbstractField = require('web.AbstractField');
    var relational_fields = require('web.relational_fields');
    var FieldMany2One = relational_fields.FieldMany2One;
    var FieldMany2ManyTags = relational_fields.FieldMany2ManyTags;
    var core = require('web.core');
    var qweb = core.qweb;
    var field_utils = require('web.field_utils');
    var _t = core._t;

    var choiceItemsMany2one = FieldMany2One.extend({
        // events: _.extend({}, FieldMany2One.prototype.events, {
        //     'blur input': '_onBlur',
        //     'keydown input': '_onKeydown',
        // }),
        // _onBlur: function (e) {},

        // /**输入时按tab键,跳到下一个项目
        //  * @param  {} e
        //  */
        // _onKeydown: function (e) {
        //     var self = this;
        //     e.stopImmediatePropagation();
        //     switch (e.which) {
        //         case $.ui.keyCode.ENTER:
        //             var d = $(e.target).parent().parent().parent().parent();
        //             var d2 = d.next().find('.o_input');
        //             if (d2.length > 0) {
        //                 d2.focus();
        //             } else {
        //                 self._super.apply(self, arguments);
        //             };
        //             // $('.itemChoice').nextUntil('.o_input').first().focus();
        //             break;
        //         default:
        //             self._super.apply(self, arguments);
        //     }
        // },

        init: function (parent, name, record, options, category_id, tag_name, tag_id) {
            this._super.apply(this, arguments);
            this.al_category_id = category_id;
            this.al_tag_name = tag_name;
            this.al_tag_id = tag_id;
            this.m2o_value = tag_name;
        },

        _search: function (search_val) {
            var self = this;
            var def = $.Deferred();
            this.orderer.add(def);
            var context = this.record.getContext(this.recordParams);
            var domain = this.record.getDomain(this.recordParams);
            _.extend(context, this.additionalContext);

            var blacklisted_ids = this._getSearchBlacklist();
            if (blacklisted_ids.length > 0) {
                domain.push(['id', 'not in', blacklisted_ids]);
            }

            // 使分析标签下拉列表只选择对应类别
            domain.push(['category_id', '=', this.al_category_id]);

            this._rpc({
                    model: this.field.relation,
                    method: "name_search",
                    kwargs: {
                        name: search_val,
                        args: domain,
                        operator: "ilike",
                        limit: this.limit + 1,
                        context: context,
                    }
                })
                .then(function (result) {
                    var values = _.map(result, function (x) {
                        x[1] = self._getDisplayName(x[1]);
                        return {
                            label: _.str.escapeHTML(x[1].trim()) || data.noDisplayContent,
                            value: x[1],
                            name: x[1],
                            id: x[0],
                        };
                    });

                    // search more... if more results than limit
                    if (values.length > self.limit) {
                        values = values.slice(0, self.limit);
                        values.push({
                            label: _t("Search More..."),
                            action: function () {
                                self._rpc({
                                        model: self.field.relation,
                                        method: 'name_search',
                                        kwargs: {
                                            name: search_val,
                                            args: domain,
                                            operator: "ilike",
                                            limit: 160,
                                            context: context,
                                        },
                                    })
                                    .then(self._searchCreatePopup.bind(self, "search"));
                            },
                            classname: 'o_m2o_dropdown_option',
                        });
                    }
                    var create_enabled = self.can_create && !self.nodeOptions.no_create;
                    // quick create
                    var raw_result = _.map(result, function (x) {
                        return x[1];
                    });
                    if (create_enabled && !self.nodeOptions.no_quick_create &&
                        search_val.length > 0 && !_.contains(raw_result, search_val)) {
                        values.push({
                            label: _.str.sprintf(_t('Create "<strong>%s</strong>"'),
                                $('<span />').text(search_val).html()),
                            action: self._quickCreate.bind(self, search_val),
                            classname: 'o_m2o_dropdown_option'
                        });
                    }
                    // create and edit ...
                    if (create_enabled && !self.nodeOptions.no_create_edit) {
                        var createAndEditAction = function () {
                            // Clear the value in case the user clicks on discard
                            self.$('input').val('');
                            return self._searchCreatePopup("form", false, self._createContext(search_val));
                        };
                        values.push({
                            label: _t("Create and Edit..."),
                            action: createAndEditAction,
                            classname: 'o_m2o_dropdown_option',
                        });
                    } else if (values.length === 0) {
                        values.push({
                            label: _t("No results to show..."),
                        });
                    }

                    def.resolve(values);
                });

            return def;
        },

        _formatValue: function (value) {
            if (this.al_tag_name) {
                return this.al_tag_name;
            }
            return "";
        },

        _onFieldChanged: function (event) {
            this.lastChangeEvent = event;
            this.$input.val(event.data.changes.analytic_tag_ids.display_name);
        },
    });


    var choiceItemsModel = FieldMany2ManyTags.extend({

        supportedFieldTypes: ['many2many'],
        custom_events: _.extend({}, FieldMany2ManyTags.prototype.custom_events, {

        }),
        events: _.extend({}, FieldMany2ManyTags.prototype.events, {

        }),

        init: function (parent, name, record, options) {
            this._super.apply(this, arguments);
            this.limit = 8;
            this.al_tags = []; //分析标签
            this.al_categories = []; //分析类别
            this.al_many2ones = [];
            this.ac_focus_n = 0;
        },

        willStart: function () {
            var def1 = this._super.apply(this, arguments);
            var def2 = this._get_tags();
            var def3 = this._get_categories();
            return $.when(def1, def2, def3);
        },

        activate: function (options) {
            if (this.isFocusable()) {
                var $focusable = this.$el.find(".o_input:eq(" + this.ac_focus_n + ")");
                if (this.ac_focus_n === 0) {
                    $focusable.focus();
                    this.ac_focus_n += 1;
                }
                return true;
            }
            return false;
        },

        getFocusableElement: function () {
            return this.mode === 'edit' && this.$('input').first() || this.$el;
        },

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------
        /**
         * 获取项目的分析标签
         */
        _get_tags: function () {
            var self = this;
            var res_ids = this.record.data.analytic_tag_ids.res_ids;
            return this._rpc({
                model: 'account.analytic.tag',
                method: 'get_tags',
                args: [res_ids],
            }).then(function (res) {
                self.al_tags = res;
            });
        },

        /**
         * 获取科目的分析类别
         */
        _get_categories: function () {
            var self = this;
            var account_id = this.record.data.account_id.res_id;
            return this._rpc({
                model: 'account.account',
                method: 'get_categories',
                args: [account_id],
            }).then(function (res) {
                self.al_categories = res;
            });
        },

        _renderEdit: function () {

            this._renderMany2ones();
        },

        _renderMany2ones: function () {
            var self = this;
            var tags = this.al_tags;
            var categories = this.al_categories;
            var many2ones = this.al_many2ones;

            // 按分析类别渲染Many2one
            for(let i = 0; i < categories.length; i++){
                var category_name = categories[i].name;
                var category_id = categories[i].id;
                var tag_name = '';
                var tag_id = null;
                var many2one = null;

                // 判断分析类别的Many2one字段是否存在,避免重复渲染
                for(let i = 0; i < many2ones.length; i++){
                    if (many2ones[i].al_category_id === category_id){
                        many2one = many2ones[i]
                    }
                }

                // 分析类别的Many2one字段不存在则新建
                if (many2one === null){
                    // 获取类别已有的分析标签
                    for(let i = 0; i < tags.length; i++) {
                        if (tags[i].category_id[0] === category_id) {
                            tag_name = tags[i].name;
                            tag_id = tags[i].id;
                        }
                    }

                    // 新建Many2one字段
                    many2one = new choiceItemsMany2one(this, this.name, this.record, {
                        mode: 'edit',
                        noOpen: true,
                        viewType: this.viewType,
                        attrs: this.attrs,
                    }, category_id, tag_name, tag_id);
                    many2ones.push(many2one);

                    // 渲染类别名称和Many2one字段
                    var category_row = $(`<div class='row itemChoice ml-0 mr-0'><div class='col-3 pl-0 pr-0'><strong>${category_name}：</strong></div><div class='col-9 ac-item-selection pl-2 pr-0'></div>`);
                    category_row.appendTo(this.$el);
                    many2one.appendTo(category_row.find('.ac-item-selection').first());
                    many2one.$el.find('input').attr('id', 'category_' + category_id);
                }
            }
        },

        _onFieldChanged: function (ev) {
            var self = this;
            if ($.inArray(ev.target, this.al_many2ones) === -1) {
                return;
            }

            ev.stopPropagation();
            var newValue = ev.data.changes[self.name];
            if (newValue) {

                if (ev.target.al_tag_id === null){
                    self._addTag(newValue);
                }

                else if (ev.target.al_tag_id !== newValue.id){
                    self._removeTag(ev.target.al_tag_id);
                    self._addTag(newValue);
                }

                ev.target.al_tag_name = newValue.name;
                ev.target.al_tag_id = newValue.id;
            }
        },
    });

    var fieldRegistry = require('web.field_registry');
    fieldRegistry.add('analytic_tags', choiceItemsModel);

    return {
        choiceItemsMany2one: choiceItemsMany2one,
        choiceItemsModel: choiceItemsModel,
    }
});