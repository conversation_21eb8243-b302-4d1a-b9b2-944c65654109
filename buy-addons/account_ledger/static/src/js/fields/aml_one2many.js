odoo.define('fr_ledger.AccMoveLineFieldOne2Many', function (require) {
    "use strict";

    var relational_fields = require('web.relational_fields');
    var FieldOne2Many = relational_fields.FieldOne2Many;
    var fieldRegistry = require('web.field_registry');

    var AccMoveLineFieldOne2Many = FieldOne2Many.extend({
        // 动态显示隐藏凭证则明细表字段
        willStart: function () {
            var self = this;
            var def = $.Deferred();

            this._rpc({
                model: 'account.move.line',
                method: 'get_invisible_columns',
                args: [[]],
            }).then(function (res) {
                $.extend(self.columnInvisibleFields, res);
                def.resolve();
                return def;
            });

            return $.when(def, this._super.apply(this, arguments));
        },
    });

    fieldRegistry.add('acc_ml_one2many', AccMoveLineFieldOne2Many);
    return AccMoveLineFieldOne2Many;
});