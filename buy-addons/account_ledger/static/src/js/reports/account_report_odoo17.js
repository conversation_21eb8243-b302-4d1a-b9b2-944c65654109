/** @odoo-module **/

import {registry} from "@web/core/registry";
import {Component, markup, onRendered, onWillStart, useState} from "@odoo/owl";

const actionRegistry = registry.category("actions");
import {useService} from "@web/core/utils/hooks";

var text = {
    noRecordsFound: '没有记录',
    showingPage: '显示页',
    show: '显示',
    entries: '明细',
    openAccountFormula: '设置科目取数公式',
    insertANewColumnBefore: '在前面插入一列',
    insertANewColumnAfter: '在后面插入一列',
    deleteSelectedColumns: '删除选中列',
    renameThisColumn: '重命名该列',
    orderAscending: '按升序排列',
    orderDescending: '按降序排列',
    insertANewRowBefore: '在前面插入一行',
    insertANewRowAfter: '在后面插入一行',
    deleteSelectedRows: '删除选中行',
    editComments: '编辑批批注',
    addComments: '添加批注',
    comments: '批注',
    clearComments: '清除批注',
    copy: '复制',
    paste: '粘贴',
    saveAs: '下载保存',
    // about: ​ '关于', 修改后将无法使用
    areYouSureToDeleteTheSelectedRows: '你确定要删除选中行?',
    areYouSureToDeleteTheSelectedColumns: '你确定要删除选中列?',
    thisActionWillDestroyAnyExistingMergedCellsAreYouSure: '你是否确定要取消合并单元格?',
    thisActionWillClearYourSearchResultsAreYouSure: '该操作会清除你的搜索结果，你是否确定?',
    thereIsAConflictWithAnotherMergedCell: '与另一个合并的单元格有冲突!',
    invalidMergeProperties: '无效的合并',
    cellAlreadyMerged: '单元格已经被合并,可以取消合并',
    noCellsSelected: '没有选中任何单元格',
};
var columns = {
    'assets_liability':
        [
            {
                type: 'text',
                title: '资产',
                width: 299,
            },
            {
                type: 'numeric',
                title: '期末余额',
                mask: '[-]#,##0.00',
                width: 115,
            },
            {
                type: 'numeric',
                title: '上年年末余额',
                mask: '[-]#,##0.00',
                width: 115,
            },
            {
                type: 'text',
                title: '负债和所有者权益（或股东权益）',
                width: 299,
            },
            {
                type: 'numeric',
                title: '期末余额',
                mask: '[-]#,##0.00',
                width: 115,
            },
            {
                type: 'numeric',
                title: '上年年末余额',
                mask: '[-]#,##0.00',
                width: 115,
            },
        ],
    'profit':
        [
            {
                type: 'text',
                title: '项目',
                width: 598,
            },
            {
                type: 'numeric',
                title: '本年累计',
                mask: '[-]#,##0.00',
                width: 230,
            },
            {
                type: 'numeric',
                title: '本期金额',
                mask: '[-]#,##0.00',
                width: 230,
            },
        ],
    'cash_flow':
        [
            {
                type: 'text',
                title: '项目',
                width: 598,
            },
            {
                type: 'numeric',
                title: '本年累计',
                mask: '[-]#,##0.00',
                width: 230,
            },
            {
                type: 'numeric',
                title: '本期金额',
                mask: '[-]#,##0.00',
                width: 230,
            },
        ],
    'owner':
        [
            {
                type: 'text',
                title: '项目',
                width: 255,
            },
            {
                type: 'numeric',
                title: '实收资本',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '优先股',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '永续债',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '其他',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '资本公积',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '减：库存股',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '其他综合收益',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '盈余公积',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '未分配利润',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '其他',
                mask: '[-]#,##0.00',
                width: 100,
            },
            {
                type: 'numeric',
                title: '少数股东权益',
                mask: '[-]#,##0.00',
                width: 230,
            },
            {
                type: 'numeric',
                title: '所有者权益合计',
                mask: '[-]#,##0.00',
                width: 230,
            },
        ],
};
var nested_header = {
    'owner':
        [
            [
                {
                    title: '所有者权益变动表',
                    colspan: '13',
                    fontWeight: 'bold'
                },
            ],
            [
                {
                    title: '',
                    colspan: '12',
                    align: 'left'
                },
                {
                    title: '会企04表',
                    colspan: '1',
                },
            ],
            [
                {
                    title: '编制单位：',
                    colspan: '12',
                    align: 'left'
                },
                {
                    title: '单位：元',
                    colspan: '1',
                },
            ],
            // [
            //     {
            //         title: '本年金额',
            //         colspan: '6',
            //     },{
            //         title: '-',
            //         colspan: '6',
            //     },
            // ],
        ],
    'assets_liability':
        [
            [
                {
                    title: '资产负债表',
                    colspan: '6',
                    fontWeight: 'bold'
                },
            ],
            [
                {
                    title: '',
                    colspan: '5',
                    align: 'left'
                },
                {
                    title: '会企01表',
                    colspan: '1',
                },
            ],
            [
                {
                    title: '编制单位：',
                    colspan: '5',
                    align: 'left'
                },
                {
                    title: '单位：元',
                    colspan: '1',
                },
            ],
            [
                {
                    title: '所属期起：',
                    colspan: '3',
                }, {
                title: '所属期止：',
                colspan: '3',
            }
            ]
        ],
    'profit':
        [
            [
                {
                    title: '利润表',
                    colspan: '3',
                    fontWeight: 'bold'
                },
            ],
            [
                {
                    title: '',
                    colspan: '2',
                    align: 'left'
                },
                {
                    title: '会企02表',
                    colspan: '1',
                },
            ],
            [
                {
                    title: '编制单位：',
                    colspan: '2',
                    align: 'left'
                }, {
                title: '单位：元',
                colspan: '1'
            }
            ],
        ],
    'cash_flow':
        [
            [
                {
                    title: '现金流量表',
                    colspan: '3',
                    fontWeight: 'bold'
                },
            ],
            [
                {
                    title: '',
                    colspan: '2',
                    align: 'left'
                },
                {
                    title: '会企03表',
                    colspan: '1',
                },
            ],
            [
                {
                    title: '编制单位：',
                    colspan: '2',
                    align: 'left'
                },
                {
                    title: '单位：元',
                    colspan: '1',
                },
            ],
        ],
};


export class ReportAction extends Component {
    static template = "account_ledger.ledger_report"

    setup() {
        this.action = useService("action");
        this.orm = useService("orm");
        this.rpc = useService('rpc');
        this.report_model = this.props.action.context.model || 'account.ledger.report';
        this.report_id = this.props.action.context.report_id || false;
        this.period_from_id = this.props.action.context.period_from_id || false;
        this.period_to_id = this.props.action.context.period_to_id || false;
        this.odoo_context = this.props.action.context;
        this.report_options = this.props.action.options || false;
        this.ignore_session = this.props.action.ignore_session;
        onWillStart(() => {
            this._render_grid();
        });

    }

    /**
     * Private methods
     */
    _render_grid() {
        var self = this;

        // 请求表格数据
        this.orm.call(
            self.report_model,
            'get_report_data',
            [self.report_id, self.period_from_id, self.period_to_id]
        ).then(function (result) {
            if (result && result.length != 0) {
                // 渲染表格
                localStorage.setItem('odoo_account_report_data', JSON.stringify(result));
                self._render_grid_data(result);
            } else if (localStorage.getItem('odoo_account_report_data')) {
                result = JSON.parse(localStorage.getItem('odoo_account_report_data'))
                self._render_grid_data(result);
            }

        });
    }

    _render_grid_data(result) {
        let self = this;
        self.report_type = result['report_type'];
        self.report_data = result['report_data'];
        self.company_name = result['company'];
        self.$el = $('.ledger_report')
        if (self.report_type) {
            var nested_header_rt = JSON.parse(JSON.stringify(nested_header[self.report_type]));

            nested_header_rt[2][0]['title'] = '编制单位：' + self.company_name;
            if (result.period_name) {
                nested_header_rt[1][0]['title'] = nested_header_rt[1][0]['title'] + result.period_name;
            }
            if (result.period_start && result.period_end && nested_header_rt.length > 3) {
                nested_header_rt[3][0]['title'] = nested_header_rt[3][0]['title'] + result.period_start;
                nested_header_rt[3][1]['title'] = nested_header_rt[3][1]['title'] + result.period_end;
            }

            // 对于所有者权益变动表，表单靠左才能不留空隙
            if (self.report_type !== 'owner') {
                self.$el.addClass('container');
                self.$el.find('.o_content').remove();
            }
            $('.o_action_manager').addClass('overflow_y');
            self.$el.find('#spreadsheet').remove();
            self.$el.append('<div id="spreadsheet"></div>');
            if (self.report_type) {

                //var table = jexcel(self.$el.find('#spreadsheet')[0], {
                var table = jexcel(self.$el.find('#spreadsheet')[0], {
                    editable: false,
                    data: result['report_data'],
                    columns: columns[result['report_type']],
                    nestedHeaders: nested_header_rt,
                    // mergeCells:{
                    //     A1: [2,1]
                    // },
                    text: text,
                    minDimensions: [3, 1],
                    toolbar:
                        [
                            {
                                type: 'i',
                                content: 'undo',
                                onclick: function () {
                                    table.undo();
                                }
                            },
                            {
                                type: 'i',
                                content: 'redo',
                                onclick: function () {
                                    table.redo();
                                }
                            },
                            {
                                type: 'select',
                                tooltip: '切换字体',
                                k: 'font-family',
                                v: ['Arial', 'Verdana']
                            },
                            {
                                type: 'select',
                                tooltip: '字体大小',
                                k: 'font-size',
                                v: ['9px', '10px', '11px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '40px', '48px', '64px', '80px',]
                            },
                            {
                                type: 'i',
                                content: 'format_align_left',
                                tooltip: '左对齐',
                                k: 'text-align',
                                v: 'left'
                            },
                            {
                                type: 'i',
                                content: 'format_align_center',
                                tooltip: '居中对齐',
                                k: 'text-align',
                                v: 'center'
                            },
                            {
                                type: 'i',
                                content: 'format_align_right',
                                tooltip: '右对齐',
                                k: 'text-align',
                                v: 'right'
                            },
                            {
                                type: 'i',
                                content: 'format_bold',
                                tooltip: '加粗字体',
                                k: 'font-weight',
                                v: 'bold'
                            },
                            {
                                type: 'color',
                                content: 'format_color_text',
                                tooltip: '设置字体颜色',
                                k: 'color'
                            },
                            {
                                type: 'color',
                                content: 'format_color_fill',
                                tooltip: '设置背景颜色',
                                k: 'background-color'
                            },
                            {
                                type: 'i',
                                content: 'create',
                                tooltip: '编辑模板',
                                onclick: function () {
                                    self.action.doAction({
                                        "type": "ir.actions.act_window",
                                        "name": "报表编辑",
                                        "res_model": "account.ledger.report",
                                        "views": [[false, "form"]],
                                        "res_id": self.report_id,
                                        "context": {},
                                        "target": "current",
                                    });
                                }
                            },
                            {
                                type: 'i',
                                content: 'save_alt',
                                tooltip: '下载为Excel',
                                onclick: function () {
                                    var myDate = new Date();
                                    self._reportDownload({
                                        report_name: self.company_name + '_' + nested_header_rt[0][0]['title'] + '_' + myDate.toLocaleDateString()
                                    })
                                    // self.trigger_up('report_download', );
                                }
                            },
                            {
                                type: 'i',
                                content: 'save_alt',
                                tooltip: '下载为PDF',
                                onclick: function () {
                                    var myDate = new Date();
                                    var filename = self.company_name + '_' + nested_header_rt[0][0]['title'] + '_' + myDate.toLocaleDateString() + '.pdf';
                                    var htmlNode = table.content;
                                    html2canvas(htmlNode).then(canvas => {
                                        const imageData = canvas.toDataURL('image/png');
                                        const pdf = new jsPDF();
                                        pdf.setFontSize(11);
                                        pdf.setFontType('normal');
                                        pdf.addImage(imageData, 'PNG', 10, 10, 170, 170);
                                        pdf.save(filename);
                                    });
                                }
                            },
                            {
                                type: 'i',
                                content: 'print',
                                tooltip: '打印',
                                onclick: function () {
                                    var myDate = new Date();
                                    var filename = self.company_name + '_' + nested_header_rt[0][0]['title'] + '_' + myDate.toLocaleDateString();
                                    let htmlNode = table.content;
                                    html2canvas(htmlNode).then(canvas => {
                                        let dataURL = canvas.toDataURL('image/png');
                                        let new_iframe = document.createElement('iframe');
                                        $(new_iframe).attr({
                                            style: 'position:absolute;width:0px;height:0px;left:-100px;top:-100px;',
                                            id: 'odoo-temp-iframe'
                                        });
                                        document.body.appendChild(new_iframe);
                                        new_iframe.contentWindow.document.write(`  
                                                <html>  
                                                  <head>  
                                                    <title>${filename}</title>  
                                                  </head>  
                                                  <body>  
                                                    <img src="${dataURL}" width="680" alt="打印内容">  
                                                  </body>  
                                                </html>  
                                             `);
                                        // new_iframe.contentWindow.document.close();
                                        setTimeout(() => {
                                            new_iframe.contentWindow.close();
                                            new_iframe.contentWindow.focus();
                                            new_iframe.contentWindow.print();
                                        }, 500);
                                    })
                                }
                            },
                        ],
                });
            }
            // self.$el.parent().removeClass();
        }
    }

    _reportDownload(e) {

        //base64转码
        var base64 = function (s) {
            return window.btoa(unescape(encodeURIComponent(s)));
        };

        //替换table数据和worksheet名字
        var format = function (s, c) {
            return s.replace(/{(\w+)}/g,
                function (m, p) {
                    return c[p];
                });
        };

        var uri = 'data:application/vnd.ms-excel;base64,';
        var template =
            '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">' +
            '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name>' +
            '<x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>' +
            '<body><table>{table}</table></body></html>';

        var table = $('#spreadsheet table.jexcel');
        var ctx = {worksheet: e.report_name || 'Sheet1', table: table[0].innerHTML};
        var alink = document.createElement('a');
        alink.href = uri + base64(format(template, ctx));
        alink.download = e.report_name;
        alink.style.display = 'none';
        document.body.appendChild(alink);
        alink.click();
        alink.parentNode.removeChild(alink);
    }
}

actionRegistry.add("ledger_report", ReportAction);