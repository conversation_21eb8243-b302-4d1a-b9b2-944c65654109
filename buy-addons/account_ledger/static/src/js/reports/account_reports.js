odoo.define('ledger.ledger_report', function (require) {
    'use strict';

    // var ControlPanelMixin = require('web.ControlPanelMixin');
    var AbstractAction = require('web.AbstractAction');
    var core = require('web.core');
    var QWeb = core.qweb;
    var text = {
        noRecordsFound: '没有记录',
        showingPage: '显示页',
        show: '显示',
        entries: '明细',
        openAccountFormula: '设置科目取数公式',
        insertANewColumnBefore: '在前面插入一列',
        insertANewColumnAfter: '在后面插入一列',
        deleteSelectedColumns: '删除选中列',
        renameThisColumn: '重命名该列',
        orderAscending: '按升序排列',
        orderDescending: '按降序排列',
        insertANewRowBefore: '在前面插入一行',
        insertANewRowAfter: '在后面插入一行',
        deleteSelectedRows: '删除选中行',
        editComments: '编辑批批注',
        addComments: '添加批注',
        comments: '批注',
        clearComments: '清除批注',
        copy: '复制',
        paste: '粘贴',
        saveAs: '下载保存',
        // about: ​ '关于', 修改后将无法使用
        areYouSureToDeleteTheSelectedRows: '你确定要删除选中行?',
        areYouSureToDeleteTheSelectedColumns: '你确定要删除选中列?',
        thisActionWillDestroyAnyExistingMergedCellsAreYouSure: '你是否确定要取消合并单元格?',
        thisActionWillClearYourSearchResultsAreYouSure: '该操作会清除你的搜索结果，你是否确定?',
        thereIsAConflictWithAnotherMergedCell: '与另一个合并的单元格有冲突!',
        invalidMergeProperties: '无效的合并',
        cellAlreadyMerged: '单元格已经被合并,可以取消合并',
        noCellsSelected: '没有选中任何单元格',
    };
    var columns = {
        'assets_liability':
            [
                {
                    type: 'text',
                    title: '资产',
                    width:299,
                },
                {
                    type: 'numeric',
                    title: '期末余额',
                    mask:'[-]#,##0.00',
                    width:115,
                },
                {
                    type: 'numeric',
                    title: '年初余额',
                    mask:'[-]#,##0.00',
                    width:115,
                },
                {
                    type: 'text',
                    title: '负债和所有者权益（或股东权益）',
                    width:299,
                },
                {
                    type: 'numeric',
                    title: '期末余额',
                    mask:'[-]#,##0.00',
                    width:115,
                },
                {
                    type: 'numeric',
                    title: '年初余额',
                    mask:'[-]#,##0.00',
                    width:115,
                },
            ],
         'profit':
            [
                {
                    type: 'text',
                    title: '项目',
                    width:598,
                },
                {
                    type: 'numeric',
                    title: '本年累计',
                    mask:'[-]#,##0.00',
                    width:230,
                },
                {
                    type: 'numeric',
                    title: '本期金额',
                    mask:'[-]#,##0.00',
                    width:230,
                },
            ],
        'cash_flow':
            [
                {
                    type: 'text',
                    title: '项目',
                    width:598,
                },
                {
                    type: 'numeric',
                    title: '本年累计',
                    mask:'[-]#,##0.00',
                    width:230,
                },
                {
                    type: 'numeric',
                    title: '本期金额',
                    mask:'[-]#,##0.00',
                    width:230,
                },
            ],
        'owner':
            [
                {
                    type: 'text',
                    title: '项目',
                    width:200,
                },
                {
                    type: 'numeric',
                    title: '实收资本',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '优先股',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '永续债',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '其他',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '资本公积',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '减：库存股',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '其他综合收益',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '盈余公积',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '未分配利润',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '其他',
                    mask:'[-]#,##0.00',
                    width:100,
                },
                {
                    type: 'numeric',
                    title: '少数股东权益',
                    mask:'[-]#,##0.00',
                    width:230,
                },
                {
                    type: 'numeric',
                    title: '所有者权益合计',
                    mask:'[-]#,##0.00',
                    width:230,
                },
            ],
    };
    var nested_header = {
        'owner':
                [
                    [
                        {
                            title: '所有者权益变动表_',
                            colspan: '13',
                        },
                    ],
                    [
                        {
                            title: '编制单位：',
                            colspan: '13',
                        },
                    ],
                    [
                        {
                            title: '本年金额',
                            colspan: '6',
                        },{
                            title: '-',
                            colspan: '6',
                        },
                    ],
                ],
        'assets_liability':
            [
                [
                    {
                        title: '资产负债表_',
                        colspan: '6',
                    },
                ],
                [
                    {
                        title: '编制单位：',
                        colspan: '6',
                    },
                ],
                [
                    {
                        title: '所属期起：',
                        colspan: '2',
                    },{
                        title: '所属期止：',
                        colspan: '2',
                    },{
                        title: '单位：元',
                        colspan: '2',
                    },
                ]
            ],
        'profit':
            [
                [
                    {
                        title: '利润表_',
                        colspan: '3',
                    },
                ],
                [
                    {
                        title: '编制单位：',
                        colspan: '3',
                    },
                ],
            ],
        'cash_flow':
            [
                [
                    {
                        title: '现金流量表_',
                        colspan: '3',
                    },
                ],
                [
                    {
                        title: '编制单位：',
                        colspan: '3',
                    },
                ],
            ],
    };

    // 财务报表展示（所有者权益，现金流量，利润表，资产负债）
    var ReportAction = AbstractAction.extend({

        events: {

        },

        custom_events: {
            report_download: '_reportDownload',
        },

        /**
         * Public methods
         */
        init: function (parent, action) {
            this.report_model = action.context.model || 'account.ledger.report';
            this.report_id = action.context.report_id || false;
            this.period_id = action.context.period_id || false;
            this.odoo_context = action.context;
            this.report_options = action.options || false;
            this.ignore_session = action.ignore_session;
            return this._super.apply(this, arguments);
        },

        start: function () {
            // 渲染表格
            this._render_grid();
        },

        /**
         * Private methods
         */
        _render_grid: function () {
            var self = this;

            // 请求表格数据
            this._rpc({
                model: self.report_model,
                method: 'get_report_data',
                args: [self.report_id, self.period_id],
                context: self.odoo_context,
            }).then(function (result) {
                if (result) {
                    //#############################################################################
                    // 渲染表格
                    self.report_type = result['report_type'];
                    self.report_data = result['report_data'];
                    self.company_name = result['company'];
                    if (self.report_type){
                        var nested_header_rt = JSON.parse(JSON.stringify(nested_header[self.report_type]));

                        nested_header_rt[1][0]['title'] = '编制单位：' + self.company_name;
                        if (result.period_name) {
                            nested_header_rt[0][0]['title'] = nested_header_rt[0][0]['title'] + result.period_name;
                        }
                        if (result.period_start && result.period_end && nested_header_rt.length > 2) {
                            nested_header_rt[2][0]['title'] = nested_header_rt[2][0]['title'] + result.period_start;
                            nested_header_rt[2][1]['title'] = nested_header_rt[2][1]['title'] + result.period_end;
                        }

                        // 对于所有者权益变动表，表单靠左才能不留空隙
                        if (self.report_type !== 'owner') {
                            self.$el.addClass('container');
                            self.$el.find('.o_content').remove();
                        }
                        self.$el.find('.o_action_manager').remove();
                        self.$el.find('#spreadsheet').remove();
                        self.$el.append('<div id="spreadsheet"></div>');
                        if (self.report_type) {

                            var table = jexcel(self.$el.find('#spreadsheet')[0], {
                                editable: false,
                                data: result['report_data'],
                                columns: columns[result['report_type']],
                                nestedHeaders: nested_header_rt,
                                text: text,
                                minDimensions: [3, 1],
                                toolbar:
                                    [
                                        {
                                            type: 'i',
                                            content: 'undo',
                                            onclick: function () {
                                                table.undo();
                                            }
                                        },
                                        {
                                            type: 'i',
                                            content: 'redo',
                                            onclick: function () {
                                                table.redo();
                                            }
                                        },
                                        {
                                            type: 'select',
                                            tooltip: '切换字体',
                                            k: 'font-family',
                                            v: ['Arial', 'Verdana']
                                        },
                                        {
                                            type: 'select',
                                            tooltip: '字体大小',
                                            k: 'font-size',
                                            v: ['9px', '10px', '11px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '40px', '48px', '64px', '80px',]
                                        },
                                        {
                                            type: 'i',
                                            content: 'format_align_left',
                                            tooltip: '左对齐',
                                            k: 'text-align',
                                            v: 'left'
                                        },
                                        {
                                            type: 'i',
                                            content: 'format_align_center',
                                            tooltip: '居中对齐',
                                            k: 'text-align',
                                            v: 'center'
                                        },
                                        {
                                            type: 'i',
                                            content: 'format_align_right',
                                            tooltip: '右对齐',
                                            k: 'text-align',
                                            v: 'right'
                                        },
                                        {
                                            type: 'i',
                                            content: 'format_bold',
                                            tooltip: '加粗字体',
                                            k: 'font-weight',
                                            v: 'bold'
                                        },
                                        {
                                            type: 'color',
                                            content: 'format_color_text',
                                            tooltip: '设置字体颜色',
                                            k: 'color'
                                        },
                                        {
                                            type: 'color',
                                            content: 'format_color_fill',
                                            tooltip: '设置背景颜色',
                                            k: 'background-color'
                                        },
                                        {
                                            type: 'i',
                                            content: 'create',
                                            tooltip: '编辑模板',
                                            onclick: function () {
                                                self.do_action({
                                                    "type": "ir.actions.act_window",
                                                    "name": "报表编辑",
                                                    "res_model": "account.ledger.report",
                                                    "views": [[false, "form"]],
                                                    "res_id": self.report_id,
                                                    "context": {},
                                                    "target": "current",
                                                });
                                            }
                                        },
                                        {
                                            type: 'i',
                                            content: 'save_alt',
                                            tooltip: '下载',
                                            onclick: function () {
                                                var myDate = new Date();
                                                self.trigger_up('report_download', {
                                                    report_name: self.company_name + '_' + nested_header_rt[0][0]['title'] + '_' + myDate.toLocaleDateString()
                                                });
                                            }
                                        },
                                    ],
                            });
                        }
                        self.$el.parent().removeClass();
                    }
                }

            });
        },

        _reportDownload: function(e) {

            //base64转码
            var base64 = function (s) {
                return window.btoa(unescape(encodeURIComponent(s)));
            };

            //替换table数据和worksheet名字
            var format = function (s, c) {
                return s.replace(/{(\w+)}/g,
                    function (m, p) {
                        return c[p];
                    });
            };

            var uri = 'data:application/vnd.ms-excel;base64,';
            var template =
                '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">' +
                '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name>' +
                '<x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>' +
                '<body><table>{table}</table></body></html>';

            var table = $('#spreadsheet table.jexcel');
            var ctx = {worksheet: e.data.report_name || 'Sheet1', table: table[0].innerHTML};
            var alink = document.createElement('a');
            alink.href = uri + base64(format(template, ctx));
            alink.download = e.data.report_name;
            alink.style.display = 'none';
            document.body.appendChild(alink);
            alink.click();
            alink.parentNode.removeChild(alink);
        },
    });

    core.action_registry.add('ledger_report', ReportAction);
    return {
    ReportAction: ReportAction,
}
    // return ReportAction;
});
