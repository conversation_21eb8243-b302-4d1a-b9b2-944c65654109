<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountLedgerReportViewForm" model="ir.ui.view">
            <field name="name">报表表单视图</field>
            <field name="model">account.ledger.report</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <header>
<!--                        <button type="object" name="export_json">导出</button>-->
                        <button name="view_report" string="预览" class="oe_highlight oe_read_only" type="object"/>
                    </header>

                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="is_template == True"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="report_type"
                                       readonly="is_template == True"/>
                                <field name="cash_flow_open" string="现金流量期初(开关)"
                                       invisible="report_type != 'cash_flow'"/>
                                <field name="cancel_out"/>
                                <field name="is_template" invisible="1"/>
                                <field name="id" invisible="1"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                       readonly="is_template == True"
                                       groups="base.group_multi_company"/>
                                <field name="report" invisible="1"/>
                                <field name="file_name" invisible="1"/>
                                <label for="download_it" class="o_form_label"/>
                                <group>
                                    <div style="width:120px">
                                        <button name="download_it" string="导出模板" class="oe_highlight" type="object"/>
                                    </div>
                                    <button name="upload_in" string="导入" class="oe_highlight" type="object"/>
                                </group>
                            </group>
                        </group>
                        <group string="说明">

                        </group>
                        <field name="note" nolabel="1"/>
                        <notebook>
                            <page string="项目" invisible="report_type == 'assets_liability'">
                                <field name="general_item_ids" context="{'default_col_in': 'general'}"
                                       readonly="is_template == True">
                                    <tree editable="bottom" limit="100">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="qc_amount" string="期初金额"
                                               invisible="parent.report_type != 'cash_flow'"/>
                                        <field name="code"/>

                                        <field name="value_1" domain="[('report_id', '=', parent.id)]" string="本年累计"
                                               column_invisible="parent.report_type == 'owner'"
                                               context="{'default_name': code + '_bnlj', 'default_report_id':parent.id}"/>
                                        <field name="value_2" domain="[('report_id', '=', parent.id)]" string="本期金额"
                                              column_invisible="parent.report_type == 'owner'"
                                               context="{'default_name': code + '_bqje', 'default_report_id':parent.id}"/>

                                        <field name="value_13" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_sszb', 'default_report_id':parent.id}"/>
                                        <field name="value_14" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_yxg', 'default_report_id':parent.id}"/>
                                        <field name="value_3" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_yxz', 'default_report_id':parent.id}"/>
                                        <field name="value_4" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_qt1', 'default_report_id':parent.id}"/>
                                        <field name="value_5" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_zbgj', 'default_report_id':parent.id}"/>
                                        <field name="value_6" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_kcg', 'default_report_id':parent.id}"/>
                                        <field name="value_7" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_qtzhsy', 'default_report_id':parent.id}"/>
                                        <field name="value_8" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_yygj', 'default_report_id':parent.id}"/>
                                        <field name="value_9" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_wfplr', 'default_report_id':parent.id}"/>
                                        <field name="value_10" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_qt2', 'default_report_id':parent.id}"/>
                                        <field name="value_11" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_ssgdly', 'default_report_id':parent.id}"/>
                                        <field name="value_12" domain="[('report_id', '=', parent.id)]"
                                               column_invisible="parent.report_type != 'owner'"
                                               context="{'default_name': code + '_syzqyhj', 'default_report_id':parent.id}"/>

                                        <field name="col_in" invisible="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="资产" invisible="report_type != 'assets_liability'">
                                <field name="assets_item_ids" context="{'default_col_in': 'assets'}"
                                       readonly="is_template == True">
                                    <tree editable="bottom" limit="100">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="value_1" domain="[('report_id', '=', parent.id)]" string="期末余额"
                                               context="{'default_name': code + '_qmye', 'default_report_id':parent.id}"/>
                                        <field name="value_2" domain="[('report_id', '=', parent.id)]" string="年初余额"
                                               context="{'default_name': code + '_ncye', 'default_report_id':parent.id}"/>
                                        <field name="col_in" invisible="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="负债和所有者权益" invisible="report_type != 'assets_liability'">
                                <field name="liability_item_ids" context="{'default_col_in': 'liability'}"
                                       readonly="is_template == True">
                                    <tree editable="bottom" limit="100">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="value_1" domain="[('report_id', '=', parent.id)]" string="期末余额"
                                               context="{'default_name': code + '_qmye', 'default_report_id':parent.id}"/>
                                        <field name="value_2" domain="[('report_id', '=', parent.id)]" string="年初余额"
                                               context="{'default_name': code + '_ncye', 'default_report_id':parent.id}"/>
                                        <field name="col_in" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="科目重分类"  invisible="cancel_out == False">
                                <field name="cancel_out_ids">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="deb_account_id"/>
                                        <field name="cre_account_id"/>
                                        <field name="partner_co"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountLedgerReportViewList" model="ir.ui.view">
            <field name="name">报表列表视图</field>
            <field name="model">account.ledger.report</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!--搜索视图-->
        <record id="AccountLedgerReportActionSearch" model="ir.ui.view">
            <field name="name">报表搜索视图</field>
            <field name="model">account.ledger.report</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="名称"/>
                    <filter name="assets_liability" string="资产负债表" domain="[('report_type', '=', 'assets_liability')]"/>
                    <filter name="profit" string="利润表" domain="[('report_type', '=', 'profit')]"/>
                    <filter name="cash_flow" string="现金流量表" domain="[('report_type', '=', 'cash_flow')]"/>
                    <separator/>
                    <group>
                        <filter name="report_type" string="报表类型" context="{'group_by': 'report_type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountLedgerReportActionList" model="ir.actions.act_window">
            <field name="name">报表配置</field>
            <field name="res_model">account.ledger.report</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="AccountLedgerReportViewList"/>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示。
                </p>
            </field>
        </record>
    </data>
</odoo>