<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountPeriodViewForm" model="ir.ui.view">
            <field name="name">会计期间表单视图</field>
            <field name="model">fr.account.period</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="fiscalyear_id"/>
                                <field name="date_start"/>
                                <field name="state"/>
                            </group>
                            <group>
                                <field name="num"/>
                                <field name="date_end"/>
                                <field name="company_id"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!--定时任务-->
        <record id="AccountPeriodStateAutoFresh" model="ir.cron" >
            <field name="name">自动更新当前会计期间</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <field name="type">ir.actions.server</field>
            <field name="model_id" ref="model_fr_account_period"/>
            <field name="state">code</field>
            <field name="code">env['fr.account.period'].refresh_current_period()</field>
        </record>
    </data>
</odoo>
