<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountLedgerValueViewForm" model="ir.ui.view">
            <field name="name">报表取值表单视图</field>
            <field name="model">account.ledger.value</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="report_id" readonly="1"/>
                                <field name="from_type"/>
                            </group>
                            <group>
                                <field name="report_type" readonly="1"/>
                                <field name="company_id" readonly="1"
                                       groups="base.group_multi_company"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="取值明细"  invisible="from_type != 'ledger_line'">
                                <field name="ledger_line_ids">
                                    <tree editable="bottom">
                                        <field name="value_sign"/>
                                        <field name="account_id" options="{'no_create': True, 'no_edit': True}"/>
                                        <field name="value_type" widget="many2one"
                                               domain="[('value_type', '=', from_type)]"
                                               options="{'no_create': True, 'no_edit': True, 'no_open': True, 'limit': 10}"/>
                                        <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                               groups="base.group_multi_company"/>
                                        <field name="from_type" invisible="1"/>
                                        <field name="value_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="取值明细" invisible="from_type != 'cash_flow'">
                                <field name="cash_flow_ids">
                                    <tree editable="bottom">
                                        <field name="value_sign"/>
                                        <field name="cash_flow_id"/>
                                        <field name="value_type" widget="many2one"
                                               domain="[('value_type', '=', from_type)]"
                                               options="{'no_create': True, 'no_edit': True, 'no_open': True}"/>
                                        <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                               groups="base.group_multi_company"/>
                                        <field name="from_type" invisible="1"/>
                                        <field name="value_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="取值明细"  invisible="from_type != 'other_value'">
                                <field name="other_value_ids">
                                    <tree editable="bottom">
                                        <field name="value_sign"/>
                                        <field name="value_from_id"
                                               domain="[('report_id', '=', parent.report_id), ('id', '!=', value_id)]"/>
                                        <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                               groups="base.group_multi_company"/>
                                        <field name="from_type" invisible="1"/>
                                        <field name="value_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountLedgerValueViewList" model="ir.ui.view">
            <field name="name">报表取值列表视图</field>
            <field name="model">account.ledger.value</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>