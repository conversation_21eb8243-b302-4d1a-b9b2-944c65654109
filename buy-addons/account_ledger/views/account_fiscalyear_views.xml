<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountFiscalyearViewForm" model="ir.ui.view">
            <field name="name">FR会计年度表单视图</field>
            <field name="model">fr.account.fiscalyear</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="activate_fiscalyear" string="激活" class="oe_highlight" type="object"
                                invisible="state !='draft'"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>会计年度
                                <field name="name" string="会计年度"
                                       readonly="state != 'draft'"
                                       class="oe_inline"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="date_start" string="开始时间"
                                       eadonly="state != 'draft'"/>
                                <field name="init_period" string="初始会计期间"
                                       eadonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="date_end" string="结束时间" eadonly="state != 'draft'"/>
                                <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                       groups="base.group_multi_company" readonly="1"/>
                            </group>

                        </group>
                        <field name="period_ids" string="会计期间" force_save="1">
                            <tree default_order="date_start" decoration-info="state == 'ongoing'"
                                  decoration-muted="state == 'unuse'" editable="bottom">
                                <field name="name" string="会计期间"/>
                                <field name="date_start" string="开始日期"/>
                                <field name="date_end" string="结束日期"/>
                                <field name="state" string="期间状态"/>
                                <field name="num" invisible="1"/>
                            </tree>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountFiscalyearViewList" model="ir.ui.view">
            <field name="name">FR会计年度列表视图</field>
            <field name="model">fr.account.fiscalyear</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'activated'" class="ps_background_hidden" default_order="name desc">
                    <field name="name" string="会计年度"/>
                    <field name="date_start" string="开始日期"/>
                    <field name="date_end" string="结束日期"/>
                    <field name="state" string="状态"/>
                    <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                           groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountFiscalyearActionList" model="ir.actions.act_window">
            <field name="name">会计日历</field>
            <field name="res_model">fr.account.fiscalyear</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    尚未建立会计日历，点击创建会计年度。
                </p>
            </field>
        </record>
    </data>
</odoo>
