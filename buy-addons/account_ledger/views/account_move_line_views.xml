<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountMoveLineViewForm" model="ir.ui.view">
            <field name="name">明细账表单视图</field>
            <field name="model">account.move.line</field>
            <field name="inherit_id" ref="account.view_move_line_form"/>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <xpath expr="//form[1]" position="replace">
                    <form string="Journal Item" create="false">
                        <sheet>
                            <field name="company_id" invisible="1"/>
                            <field name="parent_state" invisible="1"/>
                            <field name="product_uom_category_id" invisible="1"/>
                            <group>
                                <group>
                                    <field name="name" string="摘要"/>
                                    <field name="account_id" domain="[('company_id', '=', company_id)]"
                                           readonly="parent_state != 'draft'"/>
                                    <field name="fr_cash_flow_id" domain="[('company_id', '=', company_id)]"
                                           readonly="parent_state != 'draft'"/>
                                </group>

                                <group>
                                    <field name="journal_id" readonly="1"/>
                                    <field name="partner_id"
                                           domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"/>
                                </group>
                            </group>

                            <notebook colspan="4">
                                <page string="信息">
                                    <group>
                                        <group string="金额">
                                            <field name="debit" readonly="parent_state != 'draft'"
                                                   widget="minus_monetary"/>
                                            <field name="credit" readonly="parent_state != 'draft'"
                                                   widget="minus_monetary"/>
                                            <field name="amount_currency" groups="base.group_multi_currency"/>
                                            <field name="currency_id" invisible="1" groups="base.group_multi_currency"/>
                                        </group>

                                        <group string="产品数量">
                                            <field name="product_id"/>
                                            <field name="product_uom_id"/>
                                            <field name="quantity"/>
                                        </group>

                                        <!--                                        <group string="分析">-->

                                        <!--                                            <field name="analytic_account_id" groups="analytic.group_analytic_accounting" readonly="parent_state != 'draft'"/>-->
                                        <!--                                            <field name="analytic_tag_ids" groups="analytic.group_analytic_tags"-->
                                        <!--                                                   widget="many2many_tags" readonly="parent_state != 'draft'"/>-->
                                        <!--                                        </group>-->

                                        <group string="会计单据">
                                            <field name="move_id" readonly="1" string="记账凭证"/>
                                            <field name="statement_id" readonly="1"
                                                   invisible="statement_id == False"/>
                                            <!--                                            <field name="invoice_id" readonly="1" attrs="{'invisible': [('invoice_id','=',False)]}"/>-->
                                        </group>

                                        <group string="日期">
                                            <field name="date"/>
                                            <field name="date_maturity"/>
                                            <field name="blocked"/>
                                        </group>

                                        <group string="核销匹配"
                                               invisible="full_reconcile_id == False and  matched_debit_ids == [] and matched_credit_ids == []">
                                            <label for="full_reconcile_id"/>
                                            <div>
                                                <field name="full_reconcile_id"/>
                                                <field name="matched_debit_ids" invisible="1"/>
                                                <field name="matched_credit_ids" invisible="1"/>
                                                <button name="open_reconcile_view" class="oe_link" type="object"
                                                        string="-&gt; View partially reconciled entries"
                                                        invisible="full_reconcile_id != False or (matched_debit_ids == [] and matched_credit_ids == [])">
                                                </button>
                                            </div>
                                        </group>

                                        <group string="税"
                                               invisible="tax_line_id == False and tax_ids == []">
                                            <field name="tax_line_id" readonly="1"
                                                   invisible="tax_line_id == False"/>
                                            <field name="tax_ids" widget="many2many_tags" readonly="1"
                                                   invisible="tax_ids == []"/>
                                        </group>
                                    </group>
                                    <field name="narration" colspan="4" nolabel="1" placeholder="添加备注..."/>
                                </page>

                                <page string="分析行" groups="analytic.group_analytic_accounting">
                                    <field name="date" invisible="1"/>
                                    <field name="analytic_line_ids"
                                           context="{'default_general_account_id':account_id, 'default_name': name, 'default_date':date, 'amount': (debit or 0.0)-(credit or 0.0)}"/>
                                </page>
                            </notebook>
                        </sheet>
                    </form>
                </xpath>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountMoveLineViewList" model="ir.ui.view">
            <field name="name">明细账列表视图</field>
            <field name="model">account.move.line</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="priority" eval="16"/>
            <field name="arch" type="xml">
                <xpath expr="//tree[1]" position="replace">
                    <tree create="false" edit="false" delete="false" import="false"
                          decoration-info="fr_state != 'posted'" default_order="date,id">
                        <field name="date" string="凭证日期"/>
                        <field name="move_id" string="凭证编号" widget="many2one"/>
                        <field name="journal_id" string="日记账"/>
                        <field name="name" string="摘要"/>
                        <field name="account_id" string="会计科目"/>
                        <field name="partner_id" widget="partner_ledger"/>
                        <field name="fr_cash_flow_id"/>
                        <field name="tax_tag_ids"/>
                        <!--                        <field name="analytic_account_id"/>-->
                        <!--                        <field name="analytic_tag_ids"/>-->
                        <field name="debit" sum="贷方总计" widget="minus_monetary"/>
                        <field name="credit" sum="贷方总计" widget="minus_monetary"/>
                        <field name="balance" column_invisible="1"/>
                        <field name="tax_ids" widget="many2many_tags" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="company_currency_id" invisible="1" groups="base.group_multi_currency"/>
                        <field name="fr_state" invisible="1"/>
                    </tree>
                </xpath>
            </field>
        </record>

        <record id="AccountMoveLineViewListDetail" model="ir.ui.view">
            <field name="name">明细账列表视图</field>
            <field name="model">account.move.line</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree js_class="detail_list" create="false" edit="false" delete="false" import="false"
                      decoration-info="fr_state != 'posted'" default_order="date,id">
                    <field name="date" string="凭证日期"/>
                    <field name="move_id" string="凭证编号" widget="many2one"/>
                    <field name="journal_id" string="日记账"/>
                    <field name="name" string="摘要"/>
                    <field name="account_id" string="会计科目"/>
                    <field name="partner_id" widget="partner_ledger"/>
                    <field name="debit" sum="贷方总计" widget="minus_monetary"/>
                    <field name="credit" sum="贷方总计" widget="minus_monetary"/>
                    <field name="fr_amount_incurred" string="发生额"/>
                    <field name="fr_balance_end" string="余额"/>
                    <field name="fr_direction_end" string="方向"/>
                    <!--                    <field name="analytic_account_id" invisible="1"/>-->
                    <!--                    <field name="analytic_tag_ids" invisible="1"/>-->
                    <field name="tax_ids" widget="many2many_tags" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="company_currency_id" invisible="1" groups="base.group_multi_currency"/>
                    <field name="fr_state" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="AccountMoveLineViewListPivot" model="ir.ui.view">
            <field name="name">明细账列表透视视图</field>
            <field name="model">account.move.line</field>
            <field name="inherit_id" ref="account.view_move_line_pivot"/>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <xpath expr="//pivot[1]" position="replace">
                    <pivot>
                        <field name="account_id" type="row"/>
                        <field name="fr_period_id" type="col"/>
                        <field name="debit" type="measure"/>
                        <field name="credit" type="measure"/>
                        <field name="balance" type="measure"/>
                    </pivot>
                </xpath>
            </field>
        </record>

        <!--搜索视图-->
        <record id="AccountMoveLineViewSearch" model="ir.ui.view">
            <field name="name">明细账列表搜索视图</field>
            <field name="model">account.move.line</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search>
                    <field name="move_id" string="凭证编号"/>
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('ref','ilike',self)]"
                           string="凭证摘要"/>
                    <field name="date" string="凭证日期"/>
                    <field name="account_id" string="会计科目"
                           filter_domain="['|', ('account_id.name','ilike',self), ('account_id.code','ilike',self)]"/>
                    <field name="partner_id" string="业务伙伴"/>
                    <field name="journal_id" string="日记账"/>
                    <field name="debit" string="借方金额"/>
                    <field name="credit" string="贷方金额"/>
                    <!--                    <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>-->
                    <!--                    <field name="analytic_tag_ids" groups="analytic.group_analytic_tags" />-->
                    <field name="tax_line_id"/>
                    <field name="tax_ids"/>
                    <separator/>
                    <filter name="current_period" string="当前期间" domain="[('fr_period_state','=','ongoing')]"/>
                    <filter name="unsolved_period" string="未结账期间" domain="[('fr_period_state','=','open')]"/>
                    <filter name="solved_period" string="已结账期间" domain="[('fr_period_state','=','close')]"/>
                    <separator/>
                    <filter name="posted" string="已过帐" domain="[('move_id.state','=','posted')]" help="已过帐的日记账项目"/>
                    <filter name="unposted" string="未过账" domain="[('move_id.state','!=','posted')]" help="未过帐的日记账项目"/>
                    <separator/>
                    <!--                    <filter string="应收" domain="[('account_id.internal_type', '=', 'receivable')]" help="来自应收科目" name="receivable"/>-->
                    <!--                    <filter string="应付" domain="[('account_id.internal_type', '=', 'payable')]" help="来自应付科目" name="payable"/>-->
                    <separator/>
                    <filter string="未核销的"
                            domain="[('full_reconcile_id', '=', False), ('balance','!=', 0), ('account_id.reconcile','=',True)]"
                            help="来自应收应付科目未核销的日记账项目" name="unreconciled"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="会计科目" name="account" context="{'group_by':'account_id'}"/>
                        <filter string="会计期间" name="period" context="{'group_by':'fr_period_id'}"/>
                        <filter string="业务伙伴" name="partner" context="{'group_by':'partner_id'}"/>
                        <filter string="日记账" name="journal" context="{'group_by':'journal_id'}"/>
                        <filter string="日期" name="date" domain="[]" context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountMoveLineActionList" model="ir.actions.act_window">
            <field name="name">明细账</field>
            <field name="res_model">account.move.line</field>
            <field name="view_mode">tree,form,pivot</field>
            <field name="view_id" ref="AccountMoveLineViewListDetail"/>
            <field name="domain">[]</field>
            <field name="context">{'search_default_posted': 1, 'view_no_maturity': True, 'search_default_account': 1}</field>
            <field name="target">current</field>
            <field name="limit">100</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示。
                </p>
            </field>
        </record>
    </data>
</odoo>
