<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountPaymentViewForm" model="ir.ui.view">
            <field name="name">收付款单表单视图</field>
            <field name="model">account.payment</field>
            <field name="priority" eval="10"/>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form[1]/sheet[1]/group[1]/group[2]/field[@name='journal_id']" position="after">
                    <field name="cash_bool" invisible="1"/>
                    <field name="cash_flow_id"
                           readonly="state != 'draft'"
                           required="cash_bool == True"
                           string="现金流量项目"
                           options="{'no_create': True, 'no_edit': True, 'no_open': True}"/>
                    <field name="account_id_payment" string="业务伙伴科目"/>
                </xpath>
            </field>
        </record>

        <!--        <record id="view_account_payment_invoice_form" model="ir.ui.view">-->
        <!--            <field name="name">登记付款单表单视图</field>-->
        <!--            <field name="model">account.payment</field>-->
        <!--            <field name="priority" eval="10"/>-->
        <!--            <field name="inherit_id" ref="account.view_account_payment_invoice_form"/>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <xpath expr="//form[1]/sheet[1]/group[1]/group[1]/field[@name='journal_id']" position="after">-->
        <!--                    <field name="cash_flow_id" attrs="{'readonly': [('state', '!=', 'draft')]}"-->
        <!--                           options="{'no_create': True, 'no_edit': True, 'no_open': True}"/>-->

        <!--                </xpath>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--        <record id="view_bank_statement_form" model="ir.ui.view">-->
        <!--            <field name="name">对账单表单视图</field>-->
        <!--            <field name="model">account.bank.statement</field>-->
        <!--            <field name="priority" eval="10"/>-->
        <!--            <field name="inherit_id" ref="account.view_bank_statement_form"/>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <xpath expr="//form[1]/sheet[1]/notebook[1]/page[1]/field[1]/tree[1]/field[@name='partner_id']" position="after">-->
        <!--                    <field name="cash_flow_id" options="{'no_create': True, 'no_edit': True, 'no_open': True}"/>-->
        <!--                </xpath>-->
        <!--            </field>-->
        <!--        </record>-->

        <!--窗口动作-->
        <record id="AccountPaymentTransferActionList" model="ir.actions.act_window">
            <field name="name">内部转账</field>
            <field name="res_model">account.payment</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{'default_payment_type': 'transfer', 'search_default_transfers_filter': 1}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无相关记录。
                </p>
            </field>
        </record>
    </data>
</odoo>