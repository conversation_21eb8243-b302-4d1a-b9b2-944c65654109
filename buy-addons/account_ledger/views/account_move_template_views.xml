<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountMoveTemplateViewForm" model="ir.ui.view">
            <field name="name">凭证模板表单视图</field>
            <field name="model">account.move.template</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="请输入模板名称" required="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="journal_id" options="{'no_create': True, 'no_edit': True}"/>
                                <field name="account_type" widget="radio"
                                       options="{'no_create': True, 'no_edit': True}"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                       groups="base.group_multi_company"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="模板明细">
                                <field name="line_ids">
                                    <tree editable="bottom" string="凭证类型">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name" string="摘要" required="1"/>
                                        <field name="account_id" string="科目" required="1"
                                               options="{'no_create': True, 'no_edit': True}"/>
                                        <field name="direction" string="默认方向"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountMoveTemplateViewList" model="ir.ui.view">
            <field name="name">凭证模板列表视图</field>
            <field name="model">account.move.template</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="journal_id"/>
                    <field name="account_type"/>
                </tree>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountMoveTemplateActionList" model="ir.actions.act_window">
            <field name="name">凭证模板</field>
            <field name="res_model">account.move.template</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    点击创建一个凭证模板。
                </p>
            </field>
            <field name="context">
                {
                    "tree_view_ref": "account_ledger.AccountMoveTemplateViewList",
                    "form_view_ref": "account_ledger.AccountMoveTemplateViewForm",
                }
            </field>
        </record>
    </data>
</odoo>
