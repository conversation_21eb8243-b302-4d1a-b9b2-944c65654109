<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_analytic_tag_action" model="ir.actions.act_window">
            <field name="name">Analytic Tags</field>
            <field name="res_model">account.analytic.tag</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Add a new tag
              </p>
            </field>
        </record>
<!--        <record id="analytic.account_analytic_tag_form_view" model="ir.ui.view">-->
<!--            <field name="name">account.analytic.tag.form</field>-->
<!--            <field name="model">account.analytic.tag</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <form string="Analytic Tags">-->
<!--                    <sheet>-->
<!--                        <div class="oe_button_box" name="button_box">-->
<!--                            <button class="oe_stat_button" type="object" name="toggle_active" icon="fa-archive">-->
<!--                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>-->
<!--                            </button>-->
<!--                        </div>-->
<!--                        <group>-->
<!--                            <group>-->
<!--                                <field name="name"/>-->
<!--                                <field name="active_analytic_distribution" groups="analytic.group_analytic_accounting"/>-->
<!--                                <field name="coding"/>-->
<!--                            </group>-->
<!--                            <group>-->
<!--                                <field name="category_id"/>-->
<!--                                <field name="company_id" groups="base.group_multi_company"/>-->
<!--                            </group>-->
<!--                        </group>-->
<!--                        <group>-->
<!--                            <field name="analytic_distribution_ids" nolabel="1" widget="one2many_list"-->
<!--                                   attrs="{'invisible': [('active_analytic_distribution', '=', False)]}" groups="analytic.group_analytic_accounting">-->
<!--                                <tree string="Analytic Distribution" editable="bottom">-->
<!--                                    <field name="account_id"/>-->
<!--                                    <field name="percentage"/>-->
<!--                                </tree>-->
<!--                            </field>-->
<!--                        </group>-->
<!--                    </sheet>-->
<!--                </form>-->
<!--            </field>-->
<!--        </record>-->

<!--        <record id="analytic.account_analytic_tag_tree_view" model="ir.ui.view">-->
<!--            <field name="name">account.analytic.tag.tree</field>-->
<!--            <field name="model">account.analytic.tag</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <tree string="Analytic Tags">-->
<!--                    <field name="name"/>-->
<!--                    <field name="category_id"/>-->
<!--                    <field name="coding"/>-->
<!--                    <field name="company_id" groups="base.group_multi_company"/>-->
<!--                </tree>-->
<!--            </field>-->
<!--        </record>-->

        <record id="account_analytic_tag_search_view" model="ir.ui.view">
            <field name="name">account.analytic.tag.search</field>
            <field name="model">account.analytic.tag</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="分析标签"
                           filter_domain="['|', ('name','ilike',self), ('code','=like',str(self)+'%')]"/>
                    <group expand="0" string="分组">
                        <filter string="分析类别" name="analytic_category" context="{'group_by':'category_id'}"/>
                        <filter string="编码" name="coding" context="{'group_by':'coding'}"/>
                    </group>
                </search>
            </field>
        </record>

<!--        <record id="analytic.account_analytic_tag_action" model="ir.actions.act_window">-->
<!--            <field name="name">Analytic Tags</field>-->
<!--            <field name="res_model">account.analytic.tag</field>-->
<!--            <field name="view_mode">tree,form</field>-->
<!--            <field name="help" type="html">-->
<!--              <p class="o_view_nocontent_smiling_face">-->
<!--                Add a new tag-->
<!--              </p>-->
<!--            </field>-->
<!--        </record>-->

<!--&lt;!&ndash;        分析账户继承修改添加编码&ndash;&gt;-->
<!--        <record id="account_analytic_account_ledger_form_inherit" model="ir.ui.view">-->
<!--            <field name="name">account.analytic.account.form.inherit</field>-->
<!--            <field name="model">account.analytic.account</field>-->
<!--            <field name="inherit_id" ref="analytic.view_account_analytic_account_form"/>-->
<!--            <field name="arch" type="xml">-->
<!--                <xpath expr="//field[@name='partner_id']" position="after">-->
<!--                    <field name="coding"/>-->
<!--                </xpath>-->
<!--            </field>-->
<!--        </record>-->


<!--        <record id="view_account_analytic_account_search_ledger" model="ir.ui.view">-->
<!--            <field name="name">account.analytic.account.search</field>-->
<!--            <field name="model">account.analytic.account</field>-->
<!--            <field name="inherit_id" ref="analytic.view_account_analytic_account_search"/>-->
<!--            <field name="arch" type="xml">-->
<!--                <xpath expr="//filter[@name='associatedpartner']" position="after">-->
<!--                    <filter string="编码" name="coding" domain="[]" context="{'group_by':'coding'}"/>-->
<!--                </xpath>-->
<!--            </field>-->
<!--        </record>-->


    </data>
</odoo>