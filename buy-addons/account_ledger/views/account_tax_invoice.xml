<odoo>
    <data>
        <record id="account_tax_invoice_tree" model="ir.ui.view">
            <field name="name">views.account.tax.invoice.tree</field>
            <field name="model">account.tax.invoice</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-danger="state == 'cancel'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="invoice_type"/>
                    <field name="move_id"/>
                    <field name="purchaser_id"/>
                    <field name="seller_id"/>
                    <field name="currency_id" invisible="1" groups="base.group_multi_currency"/>
                    <field name="amount_total"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="account_tax_invoice_form" model="ir.ui.view">
            <field name="name">税务发票表单视图</field>
            <field name="model">account.tax.invoice</field>
            <field name="priority" eval="16"/>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="confirm" string="确认开票" type="object" class="oe_highlight oe_read_only" invisible="state !='draft'"/>
                        <button name="deprecated" string="作废" type="object" class="oe_highlight" invisible="state !='done'"
                                confirm="确认废弃该发票？"/>
                        <button name="reset_to_draft" string="重置为草稿" type="object" invisible="state !='deprecated'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,done"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>税票号码:
                                <field name="name" class="oe_inline"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="date"  readonly="state != 'draft'"/>
                                <field name="invoice_type" readonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="move_id" string="关联单据" readonly="state != 'draft'"/>
                                <field name="currency_id" invisible="1" groups="base.group_multi_currency"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="purchaser_id" readonly="state != 'draft'"/>
                                <field name="purchaser_vat" string="税号"
                                       readonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="seller_id" readonly="state != 'draft'"/>
                                <field name="seller_vat" string="税号"
                                       readonly="state != 'draft'"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="发票行">
                                <field name="line_ids" readonly="state != 'draft'">
                                    <form>
                                        <group>
                                            <group>
                                                <field name="product_id"/>
                                                <field name="specification"/>
                                            </group>
                                            <group>
                                                <field name="account_id"/>
                                                <field name="uom_id"/>
                                                <field name="currency_id" invisible="1"
                                                       groups="base.group_multi_currency"/>
                                            </group>
                                        </group>
                                        <group>
                                            <group>
                                                <field name="quantity"/>
                                                <field name="tax_id"/>
                                                <field name="amount"/>
                                            </group>
                                            <group>
                                                <field name="price_unit"/>
                                                <field name="amount_tax"/>
                                                <field name="amount_total"/>
                                            </group>
                                        </group>
                                    </form>
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle" optional="show"/>
                                        <field name="product_id" optional="show"/>
                                        <field name="account_id" optional="show"/>
                                        <field name="specification" optional="show"/>
                                        <field name="uom_id" optional="show"/>
                                        <field name="quantity" optional="show"/>
                                        <field name="price_unit" optional="show"/>
                                        <field name="amount" optional="show"/>
                                        <field name="tax_id" optional="show"/>
                                        <field name="amount_tax" optional="show"/>
                                        <field name="amount_total" optional="show"/>
                                        <field name="currency_id" invisible="1" groups="base.group_multi_currency"
                                               optional="show"/>
                                    </tree>
                                </field>

                                <group class="oe_subtotal_footer oe_right">
                                    <field name="amount"/>
                                    <field name="amount_tax"/>
                                    <field name="amount_total" class="oe_subtotal_footer_separator"/>
                                </group>

                                <group string="备注">
                                    <field name="note" nolabel="1" placeholder="在此添加备注信息..."/>
                                </group>

                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                        <field name="activity_ids" widget="mail_activity"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="account_tax_invoice_action" model="ir.actions.act_window">
            <field name="name">税务发票</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.tax.invoice</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="account_tax_invoice_tree"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    创建
                </p>
            </field>
        </record>
    </data>
</odoo>