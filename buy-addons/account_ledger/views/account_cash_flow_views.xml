<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="AccountCashFlowViewForm" model="ir.ui.view">
            <field name="name">现金流量项目表单视图</field>
            <field name="model">account.cash.flow</field>
            <field name="priority" eval="16"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                       groups="base.group_multi_company"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountCashFlowViewTree" model="ir.ui.view">
            <field name="name">现金流量项目列表视图</field>
            <field name="model">account.cash.flow</field>
            <field name="priority" eval="16"/>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountCashFlowActionList" model="ir.actions.act_window">
            <field name="name">现金流量项目</field>
            <field name="res_model">account.cash.flow</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="AccountCashFlowViewTree"/>
            <field name="context">{}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示，点击创建。
                </p>
            </field>
        </record>
    </data>
</odoo>