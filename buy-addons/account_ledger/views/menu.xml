<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--基础配置-->
        <!--                      <menuitem id="AccountBaseSetting" parent="account_accountant.menu_accounting" name="配置" sequence="30"/>-->
        <menuitem id="BaseSettingMainMenu" parent="account.menu_finance_configuration" name="主配置" sequence="10"/>
        <menuitem id="NewAccountFiscalyearList" parent="BaseSettingMainMenu" name="会计日历" sequence="21"
                  action="AccountFiscalyearActionList"/>
        <menuitem id="NewAccountAccountList" parent="BaseSettingMainMenu" name="科目表" sequence="22"
                  action="account_ledger.AccountAccountActionList"/>
        <menuitem id="NewAccountJournalList" parent="BaseSettingMainMenu" name="日记账" sequence="23"
                  action="account.action_account_journal_form"/>
        <menuitem id="NewAccountTaxList" parent="BaseSettingMainMenu" name="税率" sequence="24"
                  action="account.action_tax_form"/>
        <menuitem id="NewReportActionListMenu" parent="BaseSettingMainMenu" name="报表配置" sequence="25"
                  action="AccountLedgerReportActionList"/>
        <menuitem id="NewAccountTypeMenu" parent="BaseSettingMainMenu" name="账户类型" sequence="27"
                  action="action_account_type_form"/>

        <menuitem id="AccountBaseSettingAnalyticTwo" parent="account.menu_finance_configuration" name="分析配置"
                  sequence="11"/>

        <menuitem id="account.account_analytic_tag_menuTwo" parent="AccountBaseSettingAnalyticTwo"
                  action="account_analytic_tag_action" name="分析标签"
                  sequence="11"/>

        <menuitem id="account_analytic_category_menuTwo" parent="AccountBaseSettingAnalyticTwo"
                  action="account_analytic_category_action" name="分析类别"
                  sequence="12"/>

        <menuitem id="account.account_analytic_def_accountTwo" parent="AccountBaseSettingAnalyticTwo"
                  action="action_account_analytic_account_form" name="分析账户"
                  sequence="15"/>

        <!--                      <menuitem id="account.account_analytic_group_menuTwo" parent="AccountBaseSettingAnalyticTwo"-->
        <!--                                action="analytic.account_analytic_group_action" name="分析账户组"-->
        <!--                                sequence="20"/>-->


        <menuitem id="AccountBaseSettingOther" parent="account.menu_finance_configuration" name="其他配置" sequence="12"/>

        <menuitem id="AccountMenuAccountCashFlowActionList" name="现金流量项目" sequence="19"
                  parent="AccountBaseSettingOther" action="AccountCashFlowActionList"/>
        <menuitem id="AccountInquiryFinance" parent="account_accountant.menu_accounting" name="账簿" sequence="12"/>
        <menuitem id="AccountLedgerFinance" parent="AccountInquiryFinance" name="科目" sequence="10"/>
        <menuitem id="AccountLedgerActionListMenuFinance" parent="AccountLedgerFinance" name="总账"
                  action="LedgerInquiryWizardActionForm" sequence="5"/>
        <menuitem id="AccountMoveLineActionListMenuFinance" parent="AccountLedgerFinance" name="明细账"
                  action="DetailInquiryWizardActionForm" sequence="10"/>
        <menuitem id="AccountLedgerLineActionListMenuFinance" parent="AccountLedgerFinance" name="余额表"
                  action="BalanceInquiryWizardActionForm" sequence="15"/>
        <menuitem id="AccountPartnerNew" parent="AccountInquiryFinance" name="业务伙伴" sequence="30"/>
<!--        <menuitem id="account_reports.menu_action_account_report_partner_ledger" name="业务伙伴分类账"-->
<!--                  action="account_reports.action_account_report_partner_ledger" parent="AccountPartnerNew"-->
<!--                  groups="account.group_account_user"/>-->
<!--        <menuitem id="account_reports.menu_action_account_report_aged_receivable" name="账龄应收"-->
<!--                  action="account_reports.action_account_report_ar" parent="AccountPartnerNew"/>-->
<!--        <menuitem id="account_reports.menu_action_account_report_aged_payable" name="应付应付"-->
<!--                  action="account_reports.action_account_report_ap" parent="AccountPartnerNew"/>-->
        <menuitem id="AccountLedgerOtherNew" parent="AccountInquiryFinance" name="其他" sequence="40"/>
<!--        <menuitem id="account_reports.menu_action_account_report_general_ledger" name="总分类账"-->
<!--                  action="account_reports.action_account_report_general_ledger" parent="AccountLedgerOtherNew"-->
<!--                  sequence="20"/>-->
<!--        <menuitem id="account_reports.menu_action_account_report_coa" name="试算表"-->
<!--                  action="account_reports.action_account_report_coa" parent="AccountLedgerOtherNew"-->
<!--                  groups="account.group_account_user" sequence="30"/>-->
        <!--                      <menuitem id="account_reports.menu_action_account_report_cj" name="合并日记账"-->
        <!--                                action="account_reports.action_account_report_cj" parent="AccountLedgerOtherNew"-->
        <!--                                groups="account.group_account_user" sequence="40"/>-->
        <menuitem id="account_reports.menu_action_account_report_gt" name="税金报告" sequence="50"
                  action="account_reports.action_account_report_gt" parent="AccountLedgerOtherNew"/>
        <!--                      <menuitem id="account_reports.menu_print_journal" name="日记账审计" parent="AccountLedgerOtherNew"-->
        <!--                                action="account_reports.action_account_print_journal_menu" sequence="55"/>-->
        <menuitem id="AccountReportFinance" parent="account_accountant.menu_accounting" name="报表" sequence="13"/>
        <!--报表-->
        <menuitem id="ReportAssetsLiabilityActionListMenuFinance" parent="AccountReportFinance" name="资产负债表"
                  sequence="19"
                  action="ALReportInquiryWizardActionForm"/>
        <menuitem id="ReportProfitActionListMenuFinance" parent="AccountReportFinance" name="利润表" sequence="20"
                  action="PRReportInquiryWizardActionForm"/>
        <menuitem id="ReportOwnerEquityctionListMenuFinance" parent="AccountReportFinance" name="现金流量表" sequence="21"
                  action="CLReportInquiryWizardActionForm"/>
        <menuitem id="ReportCashFLowActionListMenuFinance" parent="AccountReportFinance" name="所有者权益变动表" sequence="30"
                  action="OEReportInquiryWizardActionForm"/>
        <menuitem id="AccountActionFinance" parent="account_accountant.menu_accounting" name="动作" sequence="18"/>
        <menuitem id="PeriodClosureFinance" parent="AccountActionFinance" name="期末月结" sequence="1"
                  action="CarryForwardWizardActionForm"/>
        <!--                            <menuitem id="account_asset.menu_asset_depreciation_confirmation_wizard"-->
        <!--                                parent="AccountActionFinance" name="生成资产分录" sequence="11"-->
        <!--                                action="account_asset.action_asset_depreciation_confirmation_wizard"-->
        <!--                                groups="account.group_account_manager"/>-->
        <menuitem id="AccountAccountInitNew" parent="AccountActionFinance" name="初始化科目余额" sequence="20"
                  action="AccountAccountActionInit"/>
        <menuitem id="NewAccountMove" parent="account_accountant.menu_accounting" name="凭证" sequence="11"/>
        <menuitem id="NewAccountMoveActionListDraftMenu" parent="NewAccountMove" name="草稿" sequence="10"
                  action="account_ledger.NewAccountMoveActionListDraft"/>
        <menuitem id="NewAccountMoveActionListApprovedMenu" parent="NewAccountMove" name="凭证审核" sequence="12"
                  action="AccountMoveActionListApproved"/>
        <menuitem id="NewAccountMoveActionListPostedMenu" parent="NewAccountMove" name="凭证查询" sequence="13"
                  action="AccountMoveActionListPosted"/>
        <menuitem id="NewAccountMoveLineDetailActionListMenu" parent="NewAccountMove" name="凭证明细" sequence="14"
                  action="account.action_account_moves_all_a"/>
        <menuitem id="NewAccountMoveTemplateActionListMenu" parent="NewAccountMove" name="凭证模板" sequence="21"
                  action="AccountMoveTemplateActionList"/>
        <odoo>
            <data>


            </data>
        </odoo>


    </data>
</odoo>