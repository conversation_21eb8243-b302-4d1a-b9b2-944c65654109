<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--        重写科目表视图-->
        <record id="account_ledger_view_account_form" model="ir.ui.view">
            <field name="name">财务总账科目表视图</field>
            <field name="model">account.account</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Account">
                    <header>
                        <button name="create_child_account" string="新建下级科目" type="object"
                                class="oe_highlight"/>
                        <button name="wizard_migrate_move_lines" string="迁移日记账项目" type="object"
                                class="oe_highlight" invisible="state != 'on_use'"/>
                        <!--                        <button name="count_account_level" string="科目分级" type="object"-->
                        <!--                                class="oe_highlight oe_read_only"/>-->
                        <button name="update_all_default_pay_rec" string="更新联系人会计分录" type="object"
                                class="oe_highlight"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <div class="alert alert-danger" role="alert"
                         invisible="fr_move_lines_count == 0 or fr_as_leaf == True">
                        该科目不是末级科目，但存在日记账项目关联该科目，可能会导致数据错误，请及时迁移记账项目至下级科目。
                    </div>

                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="account.action_move_line_select"
                                    string="会计分录" type="action" icon="fa-bars"/>
                        </div>

                        <div class="oe_title">
                            <h1 class="oe_read_only">
                                <!--                                 12-->
                                <field name="fr_path_name" class="oe_inline"/>
                                <field name="name" placeholder="科目名称" class="oe_inline"/>
                                <!--                                 14-->
                                <!--<field name="code" placeholder="code"/> - <field name="name" placeholder="name"/>
                                <field name="company_id" invisible="1"/> -->
                            </h1>

                            <div class="oe_inline oe_edit_only">
                                <label for="code"/>
                                <field name="code" placeholder="code"/>
                                <label for="name"/>
                                <field name="name" placeholder="name"/>
                            </div>
                        </div>


                        <group string="基本信息">
                            <group>
                                <field name="code" required="True" string="代码"/>
                                <field name="user_type_id" />
                                <field name="account_type" string="科目类型" required="1"/>
                                <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                                       groups="base.group_multi_company"
                                       readonly="state != 'uninitialized'"/>
                                <field name="internal_type" invisible="1"/>
                            </group>
                            <group>
                                <field name="fr_as_leaf"/>
                                <field name="fr_account_level"/>
                                <field name="fr_parent_account_id" domain="[('fr_as_leaf', '=', False)]"
                                       options="{'no_create': True, 'no_edit': True}"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="deprecated" invisible="1"/>
                                <field name="fr_initialized" invisible="1"/>
                                <field name="fr_child_count" invisible="1"/>
                                <field name="fr_move_lines_count" invisible="1"/>
                            </group>
                            <group>
                                <field name="fr_current_balance"/>
                            </group>
                            <group>
                                <field name="fr_current_direction"/>
                            </group>
                        </group>

                        <group string="配置选项">
                            <group>
                                <field name="fr_short_code"/>
                                <field name="fr_category_ids" widget="many2many_tags"/>
                                <field name="fr_default_rec"
                                       invisible="internal_type != 'receivable' or fr_as_leaf == False"/>
                                <field name="fr_default_pay"
                                       invisible="internal_type != 'payable' or fr_as_leaf == False"/>
                            </group>
                            <group>
                                <label for="reconcile"/>
                                <div>
                                    <field name="reconcile"/>
                                    <button name="action_open_reconcile" class="oe_link" type="object"
                                            invisible="reconcile == False"
                                            string=" -&gt; 核销"/>
                                </div>
                                <field name="partner_bool"/>
                                <field name="cash_bool" string="现金流量"/>
                                <!--                                取消单独选择现金流量项目-->
                                <!--                                <field name="fr_cash_flow_id" string="请选择" attrs="{'invisible': [('cash_bool', '=', False)],'required': [('cash_bool', '=', True)]}"/>-->
                                <field name="analysis_bool"/>
                                <field name="label_bool"/>
<!--                                <field name="minus_bool"/>-->

                                <field name="tax_ids" widget="many2many_tags"
                                       domain="[('company_id','=',company_id)]"
                                       groups="base.group_multi_company"
                                />
                                <field name="tag_ids" widget="many2many_tags" invisible="1"
                                       domain="[('applicability', '!=', 'taxes')]"
                                       context="{'default_applicability': 'accounts'}"/>
                            </group>
                        </group>

                        <group string="下级科目" col="16">
                            <field name="fr_child_ids" nolabel="1" colspan="15">
                                <tree create="false">
                                    <field name="code"/>
                                    <field name="fr_full_name" string="名称"/>
                                    <field name="user_type_id"/>
                                    <field name="fr_account_level"/>
                                    <field name="fr_as_leaf"/>
                                    <field name="fr_current_balance"/>
                                    <field name="fr_current_direction"/>
                                    <field name="state"/>
                                    <field name="currency_id" invisible="1"/>
                                </tree>
                            </field>
                        </group>

                        <group>
                            <field name="note" nolabel="1" placeholder="添加备注信息..."/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountAccountViewList" model="ir.ui.view">
            <field name="name">会计科目列表视图</field>
            <field name="model">account.account</field>
            <field name="priority" eval="1"/>
            <field name="arch" type="xml">
                <tree default_order="code">
                    <field name="code"/>
                    <field name="fr_full_name" string="名称"/>
                    <field name="user_type_id"/>
                    <field name="fr_account_level"/>
                    <field name="fr_as_leaf"/>
                    <field name="fr_current_balance"/>
                    <field name="fr_current_direction"/>
                    <field name="state"/>
                    <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                           groups="base.group_multi_company"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <!--        初始化科目余额列表视图-->
        <record id="AccountAccountInitViewList" model="ir.ui.view">
            <field name="name">初始化科目余额列表视图</field>
            <field name="model">account.account</field>
            <field name="arch" type="xml">
                <tree default_order="code" editable="bottom" create="false" delete="false"
                      decoration-primary="state == 'uninitialized'">
                    <field name="code" readonly="1"/>
                    <field name="fr_full_name" readonly="1" string="名称"/>
                    <field name="user_type_id" readonly="1" options="{'no_open': True}"/>
                    <field name="fr_account_level" readonly="1"/>
                    <field name="fr_as_leaf" readonly="1"/>
                    <field name="fr_init_balance"
                           readonly="state != 'uninitialized' or fr_as_leaf == False"/>
                    <field name="fr_init_direction"
                           readonly="state != 'uninitialized' or fr_as_leaf == False"/>
                    <field name="fr_initialized" readonly="1"/>
                    <field name="state" readonly="1"/>
                    <field name="company_id" options="{'no_create': True, 'no_edit': True}"
                           groups="base.group_multi_company" readonly="1"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <!--搜索视图-->
        <record id="AccountAccountViewSearch" model="ir.ui.view">
            <field name="name">会计科目搜索视图</field>
            <field name="model">account.account</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search string="Accounts">
                    <field name="name" string="会计科目"
                           filter_domain="['|', ('fr_full_name','ilike',self), ('code','ilike',self)]"/>
                    <field name="fr_short_code" string="助记码"/>
                    <separator/>
                    <filter string="未初始化" name="not_init" domain="[('state', '=', 'uninitialized')]"/>
                    <filter string="使用中" name="on_use" domain="[('state', '=', 'on_use')]"/>
                    <filter string="已封存" name="initialized" domain="[('state', '=', 'deprecated')]"/>
                    <separator/>
                    <filter string="末级科目" name="leaf" domain="[('fr_as_leaf', '=', True)]"/>
                    <filter string="非末级科目" name="not_leaf" domain="[('fr_as_leaf', '=', False)]"/>
                    <separator/>
                    <filter string="应收科目" name="receivable_acc" domain="[('internal_type','=','receivable')]"/>
                    <filter string="应付科目" name="payable_acc" domain="[('internal_type','=','payable')]"/>
                    <separator/>
                    <filter string="所有者权益" name="equity_acc" domain="[('internal_group','=', 'equity')]"/>
                    <filter string="资产" name="assets_acc" domain="[('internal_group','=', 'asset')]"/>
                    <filter string="负债" name="liability_acc" domain="[('internal_group','=', 'liability')]"/>
                    <filter string="收入" name="income_acc" domain="[('internal_group','=', 'income')]"/>
                    <filter string="费用" name="expenses_acc" domain="[('internal_group','=', 'expense')]"/>
                    <filter string="利润" name="profit_acc" domain="[('internal_group','=', 'profit')]"/>
                    <separator/>
                    <group expand="0" string="分组">
                        <filter string="科目类型" name="user_type" context="{'group_by':'user_type_id'}"/>
                        <filter string="科目等级" name="account_level" context="{'group_by':'fr_account_level'}"/>
                        <filter string="会计要素" name="internal_group" context="{'group_by':'internal_group'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountAccountActionList" model="ir.actions.act_window">
            <field name="name">科目表</field>
            <field name="res_model">account.account</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{'search_default_on_use': 1, 'tree_view_ref': 'account_ledger.AccountAccountViewList',
                'form_view_ref': 'account_ledger.account_ledger_view_account_form', }
            </field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    尚未建立会计科目，点击创建会计科目。
                </p>
            </field>
        </record>

        <record id="AccountAccountActionInit" model="ir.actions.act_window">
            <field name="name">初始化科目余额</field>
            <field name="res_model">account.account</field>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="AccountAccountInitViewList"/>
            <field name="domain">[('fr_as_leaf', '=', True)]</field>
            <field name="context">{'search_default_not_init': 1}</field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示。
                </p>
            </field>
        </record>

        <!--服务器动作-->
        <record id="AccountAccountActionInitMulti" model="ir.actions.server">
            <field name="name">初始化科目</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.initialize_account()
            </field>
        </record>

        <record id="AccountAccountActionInitMultiCancel" model="ir.actions.server">
            <field name="name">取消初始化科目</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.initialize_account_cancel()
            </field>
        </record>

        <record id="AccountAccountActionInitMultiClassification" model="ir.actions.server">
            <field name="name">科目分级</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.count_account_level()
            </field>
        </record>

        <record id="AccountAccountActionInitMultiReplay" model="ir.actions.server">
            <field name="name">科目重置</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.remove_account_chart()
            </field>
        </record>
        <record id="AccountAccountActionDeprecatedMulti" model="ir.actions.server">
            <field name="name">封存</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.deprecated_account()
            </field>
        </record>

        <record id="AccountAccountActionDeprecatedMultiCancel" model="ir.actions.server">
            <field name="name">取消封存</field>
            <field name="model_id" ref="model_account_account"/>
            <field name="binding_model_id" ref="model_account_account"/>
            <field name="state">code</field>
            <field name="code">
                records.deprecated_account_cancel()
            </field>
        </record>
    </data>
</odoo>
