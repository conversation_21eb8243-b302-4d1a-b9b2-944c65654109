<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="NewAccountMoveViewForm" model="ir.ui.view">
            <field name="name">记账凭证表单视图</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry" duplicate="false">
                    <header>
                        <button name="clean_line_fr_cash_flow_id" string="清除6月现金流量" class="oe_highlight"
                                type="object" invisible="1"/>

                        <button name="clean_line_fr_cash_flow" string="清除非银行现金类-现金流量" class="oe_highlight"
                                type="object" invisible="1"/>

                        <button name="changer_move_delete" string="取消,应收应付" class="oe_highlight"
                                type="object" invisible="1"/>

                        <button name="voucher_number_fill" string="凭证补号" class="oe_highlight" type="object"
                        />
                        <!--confirm="确认填补空号?"-->
                        <button name="voucher_approved" string="凭证审核" class="oe_highlight"
                                invisible="state != 'draft'"
                                type="object" confirm="确认审核凭证？"/>
                        <button name="voucher_approved_posted" string="审核并过账" class="oe_highlight"
                                invisible="state != 'draft'"
                                type="object"/>
                        <widget name="attach_document" string="附件上传" action="message_post"
                                invisible="state != 'draft' or fr_attachcount &gt; 0"
                                highlight="1"/>
                        <widget name="attach_document" string="附件上传" action="message_post"
                                invisible="state != 'draft' or fr_attachcount == 0"/>
                        <button name="voucher_posted" string="凭证过账" class="btn-primary" type="object"
                                invisible="state != 'approved'"/>
                        <button name="%(MoveTemplateSaveWizardAction)d" string="存为模板" class="oe_highlight"
                                type="action"/>
                        <button name="voucher_approved_cancel" string="撤销审核" type="object"
                                invisible="state != 'approved'"/>
                        <button name="voucher_posted_cancel" string="撤销过账" type="object" confirm="确认撤销过账凭证？"
                                invisible="state != 'posted'"/>
                        <button name="voucher_posted_invalid" string="凭证作废" type="object" confirm="确认作废该凭证？"
                                invisible="state not in  ['approved','draft','posted']"
                                groups="account_ledger.group_certificates_invalid"/>
                        <button name="voucher_posted_draft" string="取消作废" class="oe_read_only" type="object"
                                confirm="确认取消作废该凭证，将重置为草稿？" invisible="state != 'invalid'"/>
                        <button name="action_add_quick" string="快捷添加标签/账户" type="object" invisible="1"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <field name="tax_type_domain" invisible="1"/>
                        <field name="id" invisible="1"/>
                        <div style="display: flex; justify-content: space-between; align-items:center;">
                            <div style="display: flex; justify-content: space-between; align-items:center;">
                                <div>
                                    <span>凭证编号</span>
                                    <field name="num" readonly="1"/>
                                </div>
                                <div style="margin-left: 20px;">
                                    <span>凭证日期</span>
                                    <field name="date"/>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items:center;">
                                <h1 style="text-align: center">
                                    记账凭证
                                </h1>
                                <span style="margin-left: 20px;">
                                    <field name="fr_period_id"/>
                                </span>
                            </div>
                            <div>
                                <span>附单据：</span>
                                <field name="fr_attachcount"/>
                                <span>张</span>
                            </div>
                        </div>

                        <group>
                            <!--                        <group>-->
                            <field name="date" invisible="1"/>
                            <!--                            <field name="ref" attrs="{'required': [('id', '==', False)]}"/>-->
                            <!--                        </group>-->
                            <group>
                                <field name="journal_id" options="{'no_open': True, 'no_create': True}" invisible="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="amount" invisible="1"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="show_name_warning" invisible="1"/>
                            </group>
                        </group>
                        <field name="suitable_journal_ids" invisible="1"/>
                        <field name="company_currency_id" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="posted_before" invisible="1"/>
                        <notebook>
                            <page string="日记账项目">
                                <field name="line_ids" widget="one2many_list"
                                       readonly="state != 'draft'"
                                       context="{'line_ids': line_ids, 'journal_id': journal_id, 'default_partner_id': commercial_partner_id, 'default_currency_id': currency_id or company_currency_id}">
                                    <tree editable="bottom" string="日记账项目" default_order="debit desc"
                                          class="account_table">
                                        <field name="move_id" column_invisible="1"/>
                                        <field name="sequence" widget="handle"/>
                                        <field name="name" string="摘要" optional="show"/>
                                        <field name="account_id" options="{'no_create': True}"
                                               domain="[('company_id', '=', parent.company_id)]"/>
                                        <field name="amount_currency" groups="base.group_multi_currency"
                                               invisible="currency_id == False" optional="hide"/>
                                        <field name="company_currency_id" options="{'no_create': True}"
                                               optional="hide"/>
                                        <field name="currency_id" column_invisible="True"/>
                                        <field name="debit" sum="借方总计"/>
                                        <field name="credit" sum="贷方总计"/>
                                        <field name="partner_bool" string="业务伙伴必填" column_invisible="1"/>
                                        <field name="cash_bool" string="现金流量必填" column_invisible="1"/>
                                        <field name="analysis_bool" string="分析账户必填" column_invisible="1"/>
                                        <field name="label_bool" string="分析标签必填" column_invisible="1"/>
                                        <field name="partner_id" widget="partner_ledger" options="{'no_create': True}"
                                               domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                               optional="hide"
                                               readonly="partner_bool != True" required="partner_bool == True"
                                        />
                                        <!--取消现金流量必填-->
                                        <!--                                        <field name="fr_cash_flow_id" optional="hide"-->
                                        <!--                                               options="{'no_create': True, 'no_edit': True, 'no_open': True}"-->
                                        <!--                                               attrs="{'readonly': [('cash_bool', '!=', True)]}"-->
                                        <!--                                        />-->
                                        <field name="fr_cash_flow_id" optional="hide" string="现金流量"
                                               options="{'no_create': True, 'no_edit': True, 'no_open': True}"
                                               readonly="cash_bool != True" required="cash_bool == True"
                                        />


                                        <field name="product_id" optional="hide"/>
                                        <field name="quantity" optional="hide"/>
                                        <!--                                        <field name="analytic_account_id" groups="analytic.group_analytic_accounting" optional="hide"-->
                                        <!--                                               attrs="{'readonly': [('analysis_bool', '!=', True)],'required':[('analysis_bool', '=', True)]}"/>-->
                                        <!--                                        <field name="analytic_tag_ids" groups="analytic.group_analytic_tags"-->
                                        <!--                                               widget="many2many_tags" optional="hide"-->
                                        <!--                                               attrs="{'readonly': [('label_bool', '!=', True)],'required':[('label_bool', '=', True)]}"-->
                                        <!--                                        />-->
                                        <field name="tax_ids" string="使用的税" widget="many2many_tags" optional="hide"
                                               domain="[('type_tax_use', '=?', parent.tax_type_domain)]"
                                               context="{'append_type_to_tax_name': not parent.tax_type_domain}"
                                               options="{'no_create': True}"/>
                                        <field name="date_maturity" required="0" column_invisible="1"/>
                                        <field name="tax_line_id" column_invisible="1"/>
                                        <!--                                        <field name="recompute_tax_line" invisible="1" readonly="1"/>-->
                                        <field name="tax_line_grouping_key" column_invisible="1" readonly="1"/>
                                        <field name="company_id" column_invisible="1"/>
                                        <field name="company_currency_id" column_invisible="1"
                                               groups="base.group_multi_currency"/>
                                        <field name="fr_in_debit" column_invisible="1"/>
                                        <field name="id" column_invisible="1"/>
                                    </tree>
                                </field>

                                <!--创建审核过账-->
                                <group col="3">
                                    <group colspan="1">
                                        <field name="fr_create_uid" string="制单人" readonly="1"/>
                                        <field name="fr_create_date" string="制单日期" readonly="1" force_save="1"
                                               widget="date" invisible="1"/>
                                        <field name="fr_create_today" string="制单日期" readonly="1" force_save="1"/>
                                    </group>
                                    <group colspan="1">
                                        <field name="fr_approved_uid" string="审核人" readonly="1"/>
                                        <field name="fr_approved_date" string="审核日期" readonly="1" widget="date"/>
                                    </group>
                                    <group colspan="1">
                                        <field name="fr_posted_uid" string="过账人" readonly="1"/>
                                        <field name="fr_posted_date" string="过账日期" readonly="1" widget="date"/>
                                    </group>
                                </group>
                                <div style="height: 50px">

                                </div>
                                <group col="3">
                                    <group colspan="1">
                                        <field name="num" string="凭证编号" readonly="1"/>
                                        <field name="date" string="凭证日期"
                                               readonly="state != 'draft'"/>
                                        <field name="ref" string="内部参考"
                                               readonly="state != 'draft'"/>
                                        <field name="partner_id" options="{'no_create': True}"
                                               readonly="state != 'draft'" string="业务伙伴"/>
                                    </group>
                                    <group colspan="1">
                                        <field name="name" string="系统编号" readonly="1"/>
                                        <field name="journal_id" string="日记账"
                                               options="{'no_open': True, 'no_create_edit': True}"
                                               context="{'date':date, 'journal_id':journal_id }"
                                               readonly="state != 'draft'"/>
                                        <field name="fr_credentials" string="自有凭证号"/>

                                        <field name="fr_template_id" string="凭证模板"
                                               readonly="state != 'draft'"
                                               options="{'no_create_edit': True}" class="oe_edit_only"/>
                                    </group>
                                    <group colspan="1">
                                        <field name="fr_type" string="来源类型" readonly="1"/>
                                        <!--                                    <field name="partner_id" string="业务伙伴" readonly="1"/>-->
                                        <field name="company_id" groups="base.group_multi_company"/>
                                    </group>
                                </group>

                                <field name="narration" colspan="4" placeholder="Add an internal note..." nolabel="1"
                                       height="50"/>
                            </page>
                            <page string="其他信息">
                                <group>
                                    <field name="auto_reverse"
                                           readonly="reverse_entry_id != False"/>
                                    <field name="reverse_date"
                                           invisible="auto_reverse == False" required="auto_reverse == True"
                                           readonly="reverse_entry_id != False"/>
                                    <field name="reverse_entry_id" invisible="True"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="o_attachment_preview"/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                        <field name="activity_ids" widget="mail_activity"/>
                    </div>
                </form>
            </field>
        </record>

        <!--列表视图-->
        <record id="NewAccountMoveViewList" model="ir.ui.view">
            <field name="name">记账凭证列表视图</field>
            <field name="model">account.move</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-primary="state == 'approved'">
                    <field name="date" string="凭证日期"/>
                    <field name="num" string="凭证编号"/>
                    <field name="fr_credentials" string="自有凭证号"/>
                    <field name="name" string="系统编号"/>
                    <field name="journal_id" string="日记账"/>
                    <field name="partner_id" string="业务伙伴"/>
                    <field name="ref" string="内部参考"/>
                    <field name="fr_create_uid" string="制单人"/>
                    <field name="fr_approved_uid" string="审核人"/>
                    <field name="fr_posted_uid" string="过账人"/>
                    <field name="amount" string="金额"/>
                    <field name="state" string="状态"/>
                    <field name="currency_id" string="币种" invisible="1" groups="base.group_multi_currency"/>
                    <field name="company_id" string="公司" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!--搜索视图-->
        <record id="NewAccountMoveViewSearch" model="ir.ui.view">
            <field name="name">记账凭证搜索视图</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="凭证搜索">
                    <field name="num" filter_domain="[('num','ilike',self)]" string="凭证编号"/>
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('ref','ilike',self)]"
                           string="系统编号"/>
                    <field name="amount" string="金额"/>
                    <field name="fr_credentials" string="自有凭证号"/>
                    <field name="date" string="凭证日期"/>
                    <field name="partner_id" string="业务伙伴"/>
                    <field name="journal_id" string="日记账"/>
                    <field name="dummy_account_id" string="会计科目"/>
                    <separator/>
                    <filter name="current_period" string="当前期间" domain="[('fr_period_state','=','ongoing')]"/>
                    <filter name="unsolved_period" string="未结账期间" domain="[('fr_period_state','=','open')]"/>
                    <filter name="solved_period" string="已结账期间" domain="[('fr_period_state','=','close')]"/>
                    <separator/>
                    <filter string="草稿" name="draft" domain="[('state','=','draft')]"/>
                    <filter string="已审核" name="approved" domain="[('state','=','approved')]"/>
                    <filter string="已过账" name="posted" domain="[('state','=','posted')]"/>
                    <separator/>
                    <filter string="销售" name="sales" domain="[('journal_id.type','=','sale')]"
                            context="{'default_journal_type': 'sale'}"/>
                    <filter string="采购" name="purchases" domain="[('journal_id.type','=','purchase')]"
                            context="{'default_journal_type': 'purchase'}"/>
                    <filter string="银行" name="bankoperations" domain="[('journal_id.type','=','bank')]"
                            context="{'default_journal_type': 'bank'}"/>
                    <filter string="现金" name="cashoperations" domain="[('journal_id.type','=','cash')]"
                            context="{'default_journal_type': 'cash'}"/>
                    <filter string="杂项操作" domain="[('journal_id.type','=','general')]"
                            name="misc_filter" context="{'default_journal_type': 'general'}"/>
                    <filter string="损益结转" domain="[('journal_id.type','=','profit')]"
                            name="profit_filter" context="{'default_journal_type': 'profit'}"/>
                    <separator/>
                    <group>
                        <filter string="会计期间" name="period" context="{'group_by':'fr_period_id'}"/>
                        <filter string="日记账" name="journal" context="{'group_by':'journal_id'}"/>
                        <filter string="业务伙伴" name="partner" context="{'group_by':'partner_id'}"/>
                        <filter string="凭证日期" name="date" context="{'group_by':'date'}"/>
                        <filter string="状态" name="status" context="{'group_by':'state'}"/>
                    </group>
                </search>

            </field>
        </record>

        <!--        窗口动作-->
        <record id="NewAccountMoveActionListDraft" model="ir.actions.act_window">
            <field name="name">草稿凭证</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="account_ledger.NewAccountMoveViewList"/>
            <field name="search_view_id" ref="account_ledger.NewAccountMoveViewSearch"/>
            <!--            <field name="domain">[('move_type', '!=', 'out_invoice')]</field>-->
            <field name="context">{'default_fr_type': 'manual',
                'view_no_maturity': True,
                'search_default_draft': True,
                'default_journal_type': 'general',
                'allowed_to_be_neg': 'True',
                'search_default_current_period': True}
            </field>
            <!--            <field name="target">current</field>-->
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无草稿凭证。
                </p>
            </field>
        </record>


        <record id="account_move_form_viewNew" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="NewAccountMoveViewForm"/>
            <field name="act_window_id" ref="NewAccountMoveActionListDraft"/>
        </record>

        <record id="account_move_tree_viewNew" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="NewAccountMoveViewList"/>
            <field name="act_window_id" ref="NewAccountMoveActionListDraft"/>
        </record>

        <record id="AccountMoveActionListApproved" model="ir.actions.act_window">
            <field name="name">已审核凭证</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="account_ledger.NewAccountMoveViewList"/>
            <field name="search_view_id" ref="account_ledger.NewAccountMoveViewSearch"/>
            <field name="context">{'create': False,
                'view_no_maturity': True,
                'search_default_approved': True,
                'search_default_current_period': True,
                'form_view_ref': 'account_ledger.NewAccountMoveViewForm'
                }
            </field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无已审核凭证。
                </p>
            </field>
        </record>

        <record id="AccountMoveActionListPosted" model="ir.actions.act_window">
            <field name="name">已过账凭证</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="account_ledger.NewAccountMoveViewList"/>
            <field name="search_view_id" ref="account_ledger.NewAccountMoveViewSearch"/>
            <field name="context">{'create': False,
                'view_no_maturity': True,
                'search_default_posted': True,
                'search_default_current_period': True,
                'search_default_journal': True,
                'form_view_ref': 'account_ledger.NewAccountMoveViewForm'
                }
            </field>
            <field name="target">current</field>
            <field name="limit">80</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    暂无已过账凭证。
                </p>
            </field>
        </record>

        <record id="account.action_move_journal_line" model="ir.actions.act_window">
            <field name="name">记账凭证</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="account_ledger.NewAccountMoveViewList"/>
            <field name="search_view_id" ref="account_ledger.NewAccountMoveViewSearch"/>
            <field name="context">{
                'default_fr_type': 'manual',
                'view_no_maturity': True,
                'form_view_ref': 'account_ledger.NewAccountMoveViewForm'}
            </field>
            <field name="target">current</field>
            <field name="limit">200</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    点击创建，录入一张凭证吧。
                </p>
            </field>
        </record>

        <!--服务器动作-->
        <record id="AccountMoveActionApprove" model="ir.actions.server">
            <field name="name">凭证审核</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.voucher_approved()
            </field>
        </record>

        <record id="AccountMoveActionPost" model="ir.actions.server">
            <field name="name">凭证过账</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.voucher_posted()
            </field>
        </record>

        <record id="AccountMoveActionApprovedPost" model="ir.actions.server">
            <field name="name">审核并过账</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.voucher_approved_posted()
            </field>
        </record>

        <record id="AccountMoveActionApproveCancel" model="ir.actions.server">
            <field name="name">取消审核</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.voucher_approved_cancel()
            </field>
        </record>

        <record id="AccountMoveActionPostCancel" model="ir.actions.server">
            <field name="name">取消过账</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.voucher_posted_cancel()
            </field>
        </record>

        <record id="AccountMoveActionResetNum" model="ir.actions.server">
            <field name="name">凭证编号日期重排</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.reset_num()
            </field>
        </record>


        <record id="AccountMoveActionResetNumApproved" model="ir.actions.server">
            <field name="name">凭证编号审核日期重排</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.reset_num_approved()
            </field>
        </record>

        <record id="AccountMoveActionResetNumNext" model="ir.actions.server">
            <field name="name">凭证编号重排</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.reset_num_next()
            </field>
        </record>

        <record id="AccountMoveActionResetNameNext" model="ir.actions.server">
            <field name="name">系统编号重排</field>
            <field name="model_id" ref="model_account_move"/>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="state">code</field>
            <field name="code">
                records.reset_name_next()
            </field>
        </record>


        <record id="view_move_line_tree_grouped_extend" model="ir.ui.view">
            <field name="name">view.move.line.tree.inherit.account.accountant</field>
            <field name="model">account.move.line</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='move_id']" position="after">
                    <field name="matching_number" string="Matching" optional="show"/>
                    <field name="fr_cash_flow_id" string="现金流量项目" optional="show"/>
                    <field name="analytic_distribution" widget="analytic_distribution"
                           groups="analytic.group_analytic_accounting"
                           optional="show"
                           options="{'product_field': 'product_id', 'account_field': 'account_id', 'force_applicability': 'optional'}"
                    />
                </xpath>

                <field name="move_id" position="attributes">
                    <attribute name="widget">many2one</attribute>
                </field>

                <!--凭证明细行查询，新增凭证编号字段-->
                <xpath expr="//field[@name='move_id']" position="before">
                    <field name="move_id_num" string="凭证编号" optional="show"/>
                </xpath>

            </field>
        </record>

        <!--        凭证登记付款，添加现金流量项目-->
        <record id="view_account_reg_payment_form_move_extend" model="ir.ui.view">
            <field name="name">登记付款添加现金流量</field>
            <field name="model">account.payment.register</field>
            <field name="inherit_id" ref="account.view_account_payment_register_form"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//field[@name='journal_id']" position="after">
                        <field name="cash_bool" invisible="1"/>
                        <field name="fr_cash_flow_id" required="cash_bool == True"/>
                    </xpath>
                </data>
            </field>
        </record>

    </data>
</odoo>
