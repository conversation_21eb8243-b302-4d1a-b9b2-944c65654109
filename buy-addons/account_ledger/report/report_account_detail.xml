<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--报表动作-->
<!--        <report id="ReportAccountDetailAction" model="report.account.detail" string="明细账报表"-->
<!--                name="account_ledger.report_account_detail" report_type="qweb-html" attachment_use="True"-->
<!--                paperformat="account_ledger.account_ledger_paper_format" multi="True" />-->

        <record id="ReportAccountDetailAction" model="ir.actions.report">
            <field name="name">明细账报表</field>
            <field name="model">report.account.detail</field>
            <field name="report_type">qweb-html</field>
            <field name="report_name">account_ledger.report_account_detail</field>
            <field name="report_file">account_ledger.report_account_detail</field>
            <field name="paperformat_id" ref="account_ledger.account_ledger_paper_format"/>
            <field name="attachment"/>
            <field name="attachment_use">True</field>
            <field name="binding_model_id" ref="model_report_account_detail"/>
            <field name="binding_type">report</field>
        </record>

        <!--报表模板-->
        <template id="report_account_detail">
            <t t-call="web.html_container">
                <!--循环-->
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.basic_layout">
                        <div class="page">
                            <div class="text-center">
                                <p style="font-size: 32px"><strong><sapn t-field="doc.account_id.name"/>明细账</strong></p>
                            </div>

                            <div>
                                科目：<span t-field="doc.account_id"/>
                            </div>

                            <div t-if="doc.partner_id">
                                客户：<span t-field="doc.partner_id"/>
                            </div>

                            <table class="table table-sm table-bordered mb0" style="font-size:15px;line-height:120%">
                                <thead class="text-center">
                                    <tr>
                                        <th scope="col" width="8%">日期</th>
                                        <th scope="col" width="10%">凭证编号</th>
                                        <th scope="col" width="41%">摘要</th>
                                        <th scope="col" width="12%">借方</th>
                                        <th scope="col" width="12%">贷方</th>
                                        <th scope="col" width="5%">方向</th>
                                        <th scope="col" width="12%">余额</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <t t-foreach="doc.line_ids" t-as="o">
                                        <tr>
                                            <td class="text-center"><span t-field="o.date"/></td>
                                            <td class="text-left"><span t-field="o.move"/></td>
                                            <td class="text-left"><span t-field="o.name"/></td>
                                            <td class="text-right">
                                                <span t-field="o.debit" t-if="o.debit != 0"/>
                                            </td>
                                            <td class="text-right">
                                                <span t-field="o.credit" t-if="o.credit != 0"/>
                                            </td>
                                            <td class="text-center"><span t-field="o.direction"/></td>
                                            <td class="text-right">
                                                <span t-field="o.balance" t-if="o.balance != 0"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>

                            <div class="row" style="font-size:16px;line-height:120%">
                                <div class="col-4">
                                    核算单位：<span t-field="doc.company_id"/>
                                </div>
                                <div class="col-4 text-center">
                                    制表：<span t-field="doc.user_id"/>
                                </div>
                                <div class="col-4 text-right">
                                    打印日期：<span t-field="doc.date"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>
