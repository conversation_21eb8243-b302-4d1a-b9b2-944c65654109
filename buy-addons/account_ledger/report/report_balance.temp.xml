<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--报表动作-->
        <report id="AccountBalanceTempActionReport" model="report.account.balance" string="科目余额表" name="account_ledger.account_balance_temp"
            report_type="qweb-html" attachment_use="True" multi="False" file="account_ledger.report_balance.temp"/>

        <!--打印模板-->
        <template id="account_balance_temp">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.basic_layout">
                        <div class="page">
                            <div style="font-size:30px;text-align:center;width:40%;margin:auto">
                                <strong>发生额及余额表</strong>
                                <hr style="margin-top:4px;border-color: #141f1e"/>
                                <h6><span t-field="doc.displayName"/></h6>
                            </div>

                            <table class="table table-sm table-bordered mb0" style="font-size:15px;line-height:120%">
                                <thead class="text-center">
                                    <tr>
                                        <th scope="col" width="15%">会计科目</th>
                                        <th scope="col" width="15%">期初借方</th>
                                        <th scope="col" width="15%">期初贷方</th>
                                        <th scope="col" width="15%">借方发生</th>
                                        <th scope="col" width="15%">贷方发生</th>
                                        <th scope="col" width="15%">期末借方</th>
                                        <th scope="col" width="15%">期末贷方</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <t t-foreach="doc.line_ids" t-as="o">
                                        <tr>
                                            <td class="text-left">
                                                <span t-field="o.account_id" t-if="o.account_id"/>
                                            </td>
                                            <td class="text-right">
                                                <span t-field="o.debit_start" t-if="o.debit_start != 0"/>

                                            </td>
                                            <td class="text-right">
                                                <span t-field="o.credit_start" t-if="o.credit_start != 0"/>
                                            </td>

                                            <td class="text-right">
                                                <span t-field="o.debit_occur" t-if="o.debit_occur != 0"/>
                                            </td>

                                            <td class="text-right">
                                                <span t-field="o.credit_occur" t-if="o.credit_occur != 0"/>
                                            </td>

                                            <td class="text-right">
                                                <span t-field="o.debit_end" t-if="o.debit_end != 0"/>
                                            </td>

                                            <td class="text-right">
                                                <span t-field="o.credit_end" t-if="o.credit_end != 0"/>
                                            </td>

                                        </tr>
                                    </t>
                                </tbody>
                            </table>

                            <div class="row" style="font-size:16px;line-height:120%">
                                <div class="col-4">
                                    核算单位：<span t-field="doc.company_id"/>
                                </div>
                                <div class="col-4 text-center">
                                    制表：<span t-field="doc.user_id"/>
                                </div>
                                <div class="col-4 text-right">
                                    打印日期：<span t-field="doc.date"/>
                                </div>
                            </div>


                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>