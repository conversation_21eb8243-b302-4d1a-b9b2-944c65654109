# -*- coding: utf-8 -*-
# ========================================
# Author: zhanghaibin
# Email: <EMAIL>
# Date：2020/01/03
# ========================================
from odoo import models, fields, api

BALANCE_DIRECTION = [('debit', '借'), ('credit', '贷'), ('balance', '平')]


class ReportAccountLedger(models.TransientModel):
    _name = 'report.account.ledger'
    _description = '总账报表辅助模型'

    # 基础字段
    date = fields.Date(string='打印日期', default=fields.Date.today())

    # 关系字段
    account_id = fields.Many2one('account.account', string='会计科目')
    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度')
    line_ids = fields.One2many('report.account.ledger.line', 'ledger_id', string='总账条目')
    user_id = fields.Many2one('res.users', string='制表人', default=lambda self: self.env.user.id)

    # 关联字段
    company_id = fields.Many2one(related='account_id.company_id', string='公司', store=True)
    currency_id = fields.Many2one(related='account_id.currency_id', string='币种', store=True)


class ReportAccountLedgerLine(models.TransientModel):
    _name = 'report.account.ledger.line'
    _description = '总账报表行辅助模型'

    # 基础字段
    name = fields.Char(string='摘要')
    debit = fields.Monetary(string='借方')
    credit = fields.Monetary(string='贷方')
    direction = fields.Selection(BALANCE_DIRECTION, string='方向')
    balance = fields.Monetary(string='余额')

    # 关系字段
    ledger_id = fields.Many2one('report.account.ledger', required=True, ondelete='cascade')
    period_id = fields.Many2one('fr.account.period', string='会计期间')

    # 关联字段
    company_id = fields.Many2one(related='ledger_id.company_id', string='公司')
    currency_id = fields.Many2one(related='ledger_id.currency_id', string='币种')
