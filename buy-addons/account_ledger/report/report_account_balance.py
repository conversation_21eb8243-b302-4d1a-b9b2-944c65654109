# -*- coding: utf-8 -*-
import datetime

from odoo import models, fields, api


class ReportAccountBalance(models.TransientModel):
    _name = 'report.account.balance'
    _description = '余额表报表打印辅助模型'

    # 基础字段
    date = fields.Date(string='打印日期', default=fields.Date.today())

    # 关系字段
    line_ids = fields.One2many('report.account.balance.line', 'detail_id',  string='明细账条目')
    user_id = fields.Many2one('res.users', string='制表人', default=lambda self: self.env.user.id)
    company_id = fields.Many2one('res.company', string='公司')
    displayName = fields.Char(string="displayName")


class ReportAccountBalanceLine(models.TransientModel):
    _name = 'report.account.balance.line'
    _description = '余额表报表打印报表行辅助模型'

    # 基础字段
    account_id = fields.Many2one('account.account', string='会计科目')
    debit_start = fields.Monetary(string='期初借方')
    credit_start = fields.Monetary(string='期初贷方')
    debit_occur = fields.Monetary(string='借方发生')
    credit_occur = fields.Monetary(string='贷方发生')
    debit_end = fields.Monetary(string='期末借方')
    credit_end = fields.Monetary(string='期末贷方')

    # 关系字段
    detail_id = fields.Many2one('report.account.balance', required=True, ondelete='cascade')

    # 关联字段
    company_id = fields.Many2one('res.company', string='公司')
    currency_id = fields.Many2one('res.currency', string='币种')
