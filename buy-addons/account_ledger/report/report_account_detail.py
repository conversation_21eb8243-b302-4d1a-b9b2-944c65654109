# -*- coding: utf-8 -*-
from odoo import models, fields, api

BALANCE_DIRECTION = [('debit', '借'), ('credit', '贷'), ('balance', '平')]


class ReportAccountDetail(models.TransientModel):
    _name = 'report.account.detail'
    _description = '明细账报表辅助模型'

    # 基础字段
    date = fields.Date(string='打印日期', default=fields.Date.today())

    # 关系字段
    account_id = fields.Many2one('account.account', string='会计科目')
    partner_id = fields.Many2one('res.partner', string='客户')
    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度')
    line_ids = fields.One2many('report.account.detail.line', 'detail_id',  string='明细账条目')
    user_id = fields.Many2one('res.users', string='制表人', default=lambda self: self.env.user.id)

    # 关联字段
    company_id = fields.Many2one(related='account_id.company_id', string='公司', store=True)
    currency_id = fields.Many2one(related='account_id.currency_id', string='币种', store=True)


class ReportAccountDetailLine(models.TransientModel):
    _name = 'report.account.detail.line'
    _description = '明细账报表行辅助模型'

    # 基础字段
    date = fields.Char(string='日期')
    move = fields.Char(string='凭证')
    name = fields.Char(string='摘要')
    debit = fields.Monetary(string='借方')
    credit = fields.Monetary(string='贷方')
    direction = fields.Selection(BALANCE_DIRECTION, string='方向')
    balance = fields.Monetary(string='余额')

    # 关系字段
    detail_id = fields.Many2one('report.account.detail', required=True, ondelete='cascade')

    # 关联字段
    company_id = fields.Many2one(related='detail_id.company_id', string='公司')
    currency_id = fields.Many2one(related='detail_id.currency_id', string='币种')
