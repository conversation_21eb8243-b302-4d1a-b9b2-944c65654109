<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--报表动作-->
        <record id="AccountMoveActionReportBill" model="ir.actions.report">
            <field name="name">记账凭证</field>
            <field name="model">account.move</field>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="report_type">qweb-html</field>
            <field name="report_name">account_ledger.account_move_report</field>
            <field name="report_file">account_ledger.account_move_report</field>
            <field name="attachment_use">False</field>
        </record>

        <record id="AccountMoveActionReportPdfBill" model="ir.actions.report">
            <field name="name">记账凭证(pdf)</field>
            <field name="model">account.move</field>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">account_ledger.account_move_report_pdf</field>
            <field name="report_file">account_ledger.account_move_report_pdf</field>
            <field name="attachment_use">False</field>
        </record>

        <!--打印模板-->
        <template id="account_move_report">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="move">
                    <t t-call="web.basic_layout">
                        <div class="page">
                            <div style="font-size:30px;text-align:center;width:40%;margin:auto">
                                <strong>记账凭证
                                    <t t-if="move.state=='invalid'">
                                        <span>(作废)</span>
                                    </t>
                                </strong>
                                <hr style="margin-top:0px;margin-bottom:4px;border-color: #141f1e"/>
                                <hr style="margin-top:4px;border-color: #141f1e"/>
                            </div>

                            <div style="float:right">
                                附件
                                <span t-field="move.fr_attachcount"/>
                                张
                            </div>

                            <div class="row mb4 mt24">
                                <div class="col-4 text-left">单位：
                                    <span t-field="move.company_id"/>
                                </div>
                                <div class="col-4 text-center">日期：
                                    <span t-field="move.date"/>
                                </div>
                                <div class="col-4 text-right">编号：
                                    <span t-field="move.num"/>
                                </div>
                            </div>

                            <table class="table table-sm table-bordered mb4" style="text-align:center">
                                <thead>
                                    <tr>
                                        <th style="border: 1px solid black;" scope="col" width="40%">摘要</th>
                                        <th style="border: 1px solid black;" scope="col" width="30%">会计科目</th>
                                        <th style="border: 1px solid black;" scope="col" width="15%">借方</th>
                                        <th style="border: 1px solid black;" scope="col" width="15%">贷方</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <t t-foreach="move.line_ids" t-as="line">
                                        <tr>
                                            <td style="border: 1px solid black; text-align:left;">
                                                <span t-field="line.name"/>
                                                <t t-if="line.partner_id">
                                                    <span>/</span>
                                                    <span t-field="line.partner_id.name"/>
                                                </t>
                                                <t t-if="line.fr_cash_flow_id">
                                                    <span>/</span>
                                                    <span t-field="line.fr_cash_flow_id.name"/>
                                                </t>
                                                <!--                                                <t t-if="line.analytic_account_id">-->
                                                <!--                                                    <span>/</span><span t-field="line.analytic_account_id.name"/>-->
                                                <!--                                                </t>-->
                                                <!--                                                <t t-if="line.analytic_tag_ids">-->
                                                <!--                                                    <span>/</span><span t-esc="','.join(map(lambda x: x.name, line.analytic_tag_ids))"/>-->
                                                <!--                                                </t>-->
                                            </td>
                                            <td style="border: 1px solid black; text-align:left;">
                                                <span t-field="line.account_id"/>
                                            </td>
                                            <td style="border: 1px solid black;">
                                                <t t-if="line.debit">
                                                    <span t-esc="line.debit"/>
                                                </t>
                                            </td>
                                            <td style="border: 1px solid black;">
                                                <t t-if="line.credit">
                                                    <span t-esc="line.credit"/>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>

                                <tfoot>
                                    <tr>
                                        <th style="border: 1px solid black;" colspan="2">合计:
                                            <span t-field="move.fr_money_format"/>
                                        </th>
                                        <th style="border: 1px solid black;">
                                            <span t-esc="move.amount"/>
                                        </th>
                                        <th style="border: 1px solid black;">
                                            <span t-esc="move.amount"/>
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>

                            <div class="row mb4">
                                <div class="col-3 text-left">制单：
                                    <span t-field="move.fr_create_uid"/>
                                </div>
                                <div class="col-3 text-center">审核：
                                    <span t-field="move.fr_approved_uid"/>
                                </div>
                                <div class="col-3 text-center">记账：
                                    <span t-field="move.fr_posted_uid"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!--        pdf 打印模板-->
        <template id="account_move_report_pdf">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="move">
                    <t t-call="web.basic_layout">
                        <div class="page">
                            <div style="font-size:30px;text-align:center;width:40%;margin:auto">
                                <strong>记账凭证
                                    <t t-if="move.state=='invalid'">
                                        <span>(作废)</span>
                                    </t>
                                </strong>
                                <hr style="margin-top:0px;margin-bottom:4px;border-color: #141f1e"/>
                                <hr style="margin-top:4px;border-color: #141f1e"/>
                            </div>

                            <div style="float:right; font-size:9px;">
                                附件
                                <span t-field="move.fr_attachcount"/>
                                张
                            </div>

                            <div class="row mb4 mt24" style="font-size:9px;">
                                <div class="col-4 text-left">单位：
                                    <span t-field="move.company_id"/>
                                </div>
                                <div class="col-4 text-center">日期：
                                    <span t-field="move.date"/>
                                </div>
                                <div class="col-4 text-right">编号：
                                    <span t-field="move.num"/>
                                </div>
                            </div>

                            <table class="table table-sm table-bordered mb4" style="text-align:center; font-size:9px;">
                                <thead>
                                    <tr>
                                        <th style="border: 1px solid black;" scope="col" width="40%">摘要</th>
                                        <th style="border: 1px solid black;" scope="col" width="30%">会计科目</th>
                                        <th style="border: 1px solid black;" scope="col" width="15%">借方</th>
                                        <th style="border: 1px solid black;" scope="col" width="15%">贷方</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <t t-foreach="move.line_ids" t-as="line">
                                        <tr>
                                            <td style="border: 1px solid black; text-align:left; font-size:9px;">
                                                <span t-field="line.name"/>
                                                <t t-if="line.partner_id">
                                                    <span>/</span>
                                                    <span t-field="line.partner_id.name"/>
                                                </t>
                                                <t t-if="line.fr_cash_flow_id">
                                                    <span>/</span>
                                                    <span t-field="line.fr_cash_flow_id.name"/>
                                                </t>
                                                <!--                                                <t t-if="line.analytic_account_id">-->
                                                <!--                                                    <span>/</span><span t-field="line.analytic_account_id.name"/>-->
                                                <!--                                                </t>-->
                                                <!--                                                <t t-if="line.analytic_tag_ids">-->
                                                <!--                                                    <span>/</span><span t-esc="','.join(map(lambda x: x.name, line.analytic_tag_ids))"/>-->
                                                <!--                                                </t>-->
                                            </td>
                                            <td style="border: 1px solid black; text-align:left; font-size:9px;">
                                                <span t-field="line.account_id"/>
                                            </td>
                                            <td style="border: 1px solid black; font-size:9px;">
                                                <t t-if="line.debit">
                                                    <span t-esc="line.debit"/>
                                                </t>
                                            </td>
                                            <td style="border: 1px solid black; font-size:9px;">
                                                <t t-if="line.credit">
                                                    <span t-esc="line.credit"/>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>

                                <tfoot>
                                    <tr style="font-size:9px;">
                                        <th style="border: 1px solid black;" colspan="2">合计:
                                            <span t-field="move.fr_money_format"/>
                                        </th>
                                        <th style="border: 1px solid black;">
                                            <span t-esc="move.amount"/>
                                        </th>
                                        <th style="border: 1px solid black;">
                                            <span t-esc="move.amount"/>
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>

                            <div class="row mb4" style="font-size:9px;">
                                <div class="col-3 text-left">制单：
                                    <span t-field="move.fr_create_uid"/>
                                </div>
                                <div class="col-3 text-center">审核：
                                    <span t-field="move.fr_approved_uid"/>
                                </div>
                                <div class="col-3 text-center">记账：
                                    <span t-field="move.fr_posted_uid"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

    </data>
</odoo>