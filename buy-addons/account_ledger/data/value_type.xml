<?xml version="1.0" encoding="UTF-8" ?>
<data>

    <record id="value_type_balance_period_deb" model="account.ledger.value.type">
        <field name="sequence">1</field>
        <field name="code">balance_period_end_deb</field>
        <field name="name">期末借方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_balance_period_cre" model="account.ledger.value.type">
        <field name="sequence">2</field>
        <field name="code">balance_period_end_cre</field>
        <field name="name">期末贷方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_balance_period_start_deb" model="account.ledger.value.type">
        <field name="sequence">3</field>
        <field name="code">balance_period_start_deb</field>
        <field name="name">期初借方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_balance_period_start_cre" model="account.ledger.value.type">
        <field name="sequence">4</field>
        <field name="code">balance_period_start_cre</field>
        <field name="name">期初贷方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_balance_start_deb" model="account.ledger.value.type">
        <field name="sequence">5</field>
        <field name="code">balance_year_start_deb</field>
        <field name="name">年初借方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_balance_start_cre" model="account.ledger.value.type">
        <field name="sequence">6</field>
        <field name="code">balance_year_start_cre</field>
        <field name="name">年初贷方余额</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_period_deb" model="account.ledger.value.type">
        <field name="sequence">7</field>
        <field name="code">period_deb</field>
        <field name="name">本期借方发生</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_period_cre" model="account.ledger.value.type">
        <field name="sequence">8</field>
        <field name="code">period_cre</field>
        <field name="name">本期贷方发生</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_fiscalyear_deb" model="account.ledger.value.type">
        <field name="sequence">9</field>
        <field name="code">fiscalyear_deb</field>
        <field name="name">本年借方发生</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_fiscalyear_cre" model="account.ledger.value.type">
        <field name="sequence">10</field>
        <field name="code">fiscalyear_cre</field>
        <field name="name">本年贷方发生</field>
        <field name="value_type">ledger_line</field>
    </record>

    <record id="value_type_fiscalyear_deb_diff" model="account.ledger.value.type">
        <field name="sequence">11</field>
        <field name="code">fiscalyear_deb_diff</field>
        <field name="name">本年借方差额</field>
        <field name="value_type">cash_flow</field>
    </record>

    <record id="value_type_fiscalyear_cre_diff" model="account.ledger.value.type">
        <field name="sequence">12</field>
        <field name="code">fiscalyear_cre_diff</field>
        <field name="name">本年贷方差额</field>
        <field name="value_type">cash_flow</field>
    </record>

    <record id="value_type_period_deb_diff" model="account.ledger.value.type">
        <field name="sequence">13</field>
        <field name="code">period_deb_diff</field>
        <field name="name">本期借方差额</field>
        <field name="value_type">cash_flow</field>
    </record>

    <record id="value_type_period_cre_diff" model="account.ledger.value.type">
        <field name="sequence">14</field>
        <field name="code">period_cre_diff</field>
        <field name="name">本期贷方差额</field>
        <field name="value_type">cash_flow</field>
    </record>
</data>