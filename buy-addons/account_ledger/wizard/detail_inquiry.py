# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError, MissingError


class DetailInquiryWizard(models.TransientModel):
    """明细帐查询向导"""
    _name = 'detail.inquiry.wizard'
    _inherit = 'inquiry.wizard.mixin'
    _description = '明细帐查询向导'

    partner_ids = fields.Many2many('res.partner', string="业务伙伴")

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)

    @api.model
    def server_default_confirm(self):
        fiscalyear_from = self.env['fr.account.fiscalyear'].search([('company_id', '=', self.env.company.id),
                                                                    ('state', '=', 'activated')],
                                                                   limit=1, order='date_start')
        fiscalyear_to = self.env['fr.account.fiscalyear'].search([('company_id', '=', self.env.company.id),
                                                                  ('state', '=', 'activated')],
                                                                 limit=1, order='date_end DESC')

        period_from = self.env['fr.account.period'].search([('fiscalyear_id', '=', fiscalyear_from.id)],
                                                           limit=1, order='date_start')
        period_to = self.env['fr.account.period'].search([('fiscalyear_id', '=', fiscalyear_to.id)],
                                                         limit=1, order='date_end DESC')
        record = self.create({
            'fiscalyear_from': fiscalyear_from.id,
            'fiscalyear_to': fiscalyear_to.id,
            'period_from': period_from.id,
            'period_to': period_to.id,
        })
        return record.confirm()

    def confirm(self):
        self.ensure_one()

        # 获取总账窗口动作
        act_window = self.env['ir.actions.act_window']._for_xml_id('account_ledger.AccountMoveLineActionList')
        act_window['domain'] = eval(act_window['domain'])
        act_window['context'] = eval(act_window['context'])

        periods = self.env['fr.account.period'].search(
            [('date_start', '>=', self.period_from.date_start), ('date_start', '<=', self.period_to.date_start)])
        # 添加会计期间筛选
        if self.period_from and self.period_to:
            act_window['domain'] += [('fr_period_id', 'in', periods.ids)]
            act_window['display_name'] = f'明细账：{self.period_from.name} - {self.period_to.name}'

        # 添加会计科目筛选
        if len(self.account_ids) > 0:
            act_window['domain'] += [('account_id', 'in', self.account_ids.ids)]
        # 添加业务伙伴筛选
        if len(self.partner_ids) > 0:
            act_window['domain'] += [('partner_id', 'in', self.partner_ids.ids)]

        return act_window
