# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError, MissingError


class QuickAddWizard(models.TransientModel):
    """快捷添加分析标签分析账户"""
    _name = 'quick.add.wizard'
    _description = '快速添加分析账户分析标签'

    # 关系字段
    account_move_id = fields.Many2one('account.move', string='发票凭证主表')

    analytic_account_id = fields.Many2one('account.analytic.account', string='分析账户')
    analytic_tag_ids = fields.Many2many('account.analytic.tag', string='分析标签')

    def confirm(self):
        """
        确认添加  分析账户 分析标签
        :return:
        """
        self.ensure_one()
        for line in self.account_move_id.invoice_line_ids:
            if line.account_id.label_bool:
                line.update({
                    'analytic_tag_ids': [(6, 0, self.analytic_tag_ids.ids)],
                })
            if line.account_id.analysis_bool:
                line.update({
                    'analytic_account_id': self.analytic_account_id,
                })
        for line_tow in self.account_move_id.line_ids:
            if line_tow.account_id.label_bool:
                line_tow.update({
                    'analytic_tag_ids': [(6, 0, self.analytic_tag_ids.ids)],
                })
            if line_tow.account_id.analysis_bool:
                line_tow.update({
                    'analytic_account_id': self.analytic_account_id,
                })


