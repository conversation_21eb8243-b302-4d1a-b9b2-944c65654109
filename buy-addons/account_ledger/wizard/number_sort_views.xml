<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="NumberSortWizardViewForm" model="ir.ui.view">
            <field name="name">编号整理选择向导</field>
            <field name="model">number.sort.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group string="会计期间" invisible="1">
                            <group >
                                <field name="period_id" string='会计期间' />
                            </group>
                        </group>

                        <group string="选择整理方式">
                            <group>
                                <group>
                                <field name="number_rearrange" string='凭证号重排'/>
                                </group>
                                <group >
                                <field name="date_rearrange" string='凭证日期重排'/>
                                </group>
                            </group>
                            <group>

                                <group>
                                <field name="approve_rearrange" string='审核日期重排'/>
                                </group>
                            </group>

                        </group>


                    </sheet>
                    <footer>
                        <button name="reset_move_num_wizard" type="object" string="编号整理" class="btn-primary"/>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="NumberSortWizardActionForm" model="ir.actions.act_window">
            <field name="name">编号整理</field>
            <field name="res_model">number.sort.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="NumberSortWizardViewForm"/>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
