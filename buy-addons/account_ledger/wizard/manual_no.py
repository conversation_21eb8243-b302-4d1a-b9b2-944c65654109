# -*- coding: utf-8 -*-
from odoo import api, models, fields, _
from odoo.exceptions import UserError, MissingError
from odoo.exceptions import UserError

class ManualNo(models.TransientModel):
    """手动补号操作"""
    _name = 'move.manual.no'
    _description = '凭证手动补号'

    account_move_not_list = fields.Text(string='空缺编号', readonly=True)

    move_num = fields.Char(string='填补的编号', required=True)

    move_id = fields.Many2one('account.move', string='会计凭证')

    def confirm(self):
        """
        确认添加  确认修改 凭证手动补号
        :return:
        """
        self.ensure_one()
        move_not_list = eval(self.account_move_not_list)
        if self.move_num in move_not_list:
            self.move_id.num = self.move_num
        else:
            raise UserError(_("请填写正确的凭证编号"))





