# -*- coding: utf-8 -*-
from odoo import fields, models, api


class MoveTemplateSaveWizard(models.TransientModel):
    """凭证模板保存向导"""
    _name = 'move.template.save.wizard'
    _description = '凭证模板保存向导'

    def _default_move_id(self):
        return self.env['account.move'].browse(self._context.get('active_id'))

    name = fields.Char(string='模板名称')
    move_id = fields.Many2one('account.move', default=_default_move_id)


    def save_template(self):
        for rec in self:

            # 创建凭证模板行
            line_vals = []
            for line in rec.move_id.line_ids:
                if line.debit != 0:
                    direction = 'debit'
                elif line.credit != 0:
                    direction = 'credit'
                else:
                    direction = None

                line_vals.append((0, 0, {
                    'name': line.name,
                    'account_id': line.account_id.id,
                    'direction': direction,
                }))

            # 创建凭证模板
            vals_template = {
                'name': rec.name,
                'journal_id': rec.move_id.journal_id.id,
                'line_ids': line_vals,
            }

            # 创建凭证模板行
            move_tempalte = self.env['account.move.template'].create(vals_template)
