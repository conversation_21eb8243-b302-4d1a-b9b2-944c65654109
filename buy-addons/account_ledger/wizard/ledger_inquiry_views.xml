<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="LedgerInquiryWizardViewForm" model="ir.ui.view">
            <field name="name">总账查询向导表单视图</field>
            <field name="model">ledger.inquiry.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group string="公司">
                                <field name="company_id" string='公司' required="1"/>
                            </group>
                        </group>
                        <group string="查询期间：">
                            <div colspan="12">从：</div>
                            <group>
                                <field name="fiscalyear_from" string="会计年度" domain="[('company_id','=',company_id)]"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                />
                            </group>
                            <group>
                                <field name="period_from" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_from), ('state', '!=', 'unuse')]"/>
                            </group>
                            <div colspan="12">至：</div>
                            <group>
                                <field name="fiscalyear_to" string="会计年度" domain="[('company_id','=',company_id)]"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                />
                            </group>
                            <group>
                                <field name="period_to" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_to), ('state', '!=', 'unuse')]"/>
                            </group>
                        </group>

                        <group string="查询科目：">
                            <field name="account_ids" string="会计科目" widget="many2many_tags" domain="[('company_id','=',company_id)]"
                                   options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <div class="alert alert-info" role="alert">
                            提示：不选择会计科目则默认查询全部会计科目。
                        </div>
                    </sheet>
                    <footer>
                        <button name="confirm" type="object" class="btn-primary">
                            <i class="fa fa-search"/>
                            查询
                        </button>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="LedgerInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">总账查询：</field>
            <field name="res_model">ledger.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="LedgerInquiryWizardViewForm"/>
            <field name="target">new</field>
        </record>
        <record id="LedgerInquiryWizardActionServerForm" model="ir.actions.server">
            <field name="name">总账查询：</field>
            <field name="model_id" ref="account_ledger.model_ledger_inquiry_wizard"/>
            <field name="binding_model_id" ref="account_ledger.model_ledger_inquiry_wizard"/>
            <field name="state">code</field>
            <field name="code">
                  action = env['ledger.inquiry.wizard'].search([('id', '=', '-1')]).server_default_confirm()
            </field>
        </record>

        <!--列表视图-->
        <record id="AccountLedgerTempViewList" model="ir.ui.view">
            <field name="name">总账列表视图</field>
            <field name="model">account.ledger.temp</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree js_class="ledger_list" create="false" import="false" delete="false"
                      decoration-primary="name == '初始余额' or name == '上年结转'">
                    <field name="account_id" string="会计科目"/>
                    <field name="period_id" string="会计期间"
                           invisible="name in ['初始余额', '上年结转', '本年累计']"/>
                    <field name="name" string="摘要"/>
                    <field name="debit" string="借方"
                           invisible="name == '初始余额'"/>
                    <field name="credit" string="贷方"
                           invisible="name == '初始余额'"/>
                    <field name="balance" string="余额"
                           invisible="direction == False"/>
                    <field name="direction" string="方向"
                           invisible="direction == False"/>
                    <field name="currency_id" invisible="1" groups="base.group_multi_currency"/>
                </tree>
            </field>
        </record>

        <!--搜索视图-->
        <record id="AccountLedgerTempViewSearch" model="ir.ui.view">
            <field name="name">总账搜索视图</field>
            <field name="model">account.ledger.temp</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search>
                    <field name="account_id" string="会计科目"
                           filter_domain="['|', ('account_id.name','ilike',self), ('account_id.code','ilike',self)]"/>
                    <field name="fiscalyear_id" string="会计年度"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="会计科目" name="account" context="{'group_by':'account_id'}"/>
                        <filter string="会计年度" name="fiscalyear" context="{'group_by':'fiscalyear_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountLedgerTempActionList" model="ir.actions.act_window">
            <field name="name">总账</field>
            <field name="res_model">account.ledger.temp</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="AccountLedgerTempViewList"/>
            <field name="domain">[]</field>
            <field name="context">{'search_default_account': 1}</field>
            <field name="target">current</field>
            <field name="limit">1000</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示。
                </p>
            </field>
        </record>
    </data>
</odoo>
