# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError, MissingError

BALANCE_DIRECTION = [('debit', '借'), ('credit', '贷'), ('balance', '平')]


class CarryForwardWizard(models.TransientModel):
    """期末月结向导"""
    _name = 'carry.forward.wizard'
    _description = '期末月结向导'

    # 关系字段
    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度', required=True, ondelete='cascade')
    period_id = fields.Many2one('fr.account.period', string='会计期间', required=True, store=True, ondelete='cascade')
    account_id = fields.Many2one('account.account', string='损益结转科目',
                                 domain=[('fr_as_leaf', '=', True), ('state', '=', 'on_use')])
    journal_id = fields.Many2one('account.journal', string='损益结转日记账',
                                 domain=[('type', '=', 'general')])

    # 关联字段
    period_state = fields.Selection(related='period_id.state', string='期间状态')
    # unpost_move_ids = fields.One2many(related='period_id.unpost_move_ids', string='未过账凭证', readonly=True)

    # 计算字段
    num_continue = fields.Boolean(string='凭证编号是否连续', compute='_compute_num_and_carry_over', readonly=False)
    carry_over_state = fields.Boolean(string='是否需要损益结转', compute='_compute_num_and_carry_over', readonly=False)

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)

    ####################
    # 公用方法
    ####################
    # @api.multi
    def reset_move_num(self):
        """单实例方法：期间凭证编号整理

        :return: 更新后的当前视图
        """
        self.ensure_one()
        self.period_id.reset_move_num()

        return {
            'type': 'ir.actions.act_window',
            'name': '期末月结',
            'res_model': 'carry.forward.wizard',
            'views': [[False, "form"]],
            'res_id': self.id,
            'target': 'new'
        }



    def reset_move_num(self):
        """单实例方法：期间凭证编号整理
        :return: 更新后的当前视图
        """
        self.ensure_one()
        view = self.env.ref('account_ledger.NumberSortWizardViewForm')
        return {
            'type': 'ir.actions.act_window',
            'name': '编号整理',
            'res_model': 'number.sort.wizard',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'context': {
                'default_period_id': self.period_id.id
            },
            'target': 'new'
        }


    def carry_over(self):
        """单实例方法：结转期间对应的损益科目总账

        :return: 更新后的当前视图
        """
        self.ensure_one()
        if (not self.journal_id) or (not self.account_id):
            raise MissingError('请先配置损益结转科目和损益结转日记账！')
        self.period_id.execute_carry_over(self.account_id.id, self.journal_id.id)
        return {
            'type': 'ir.actions.act_window',
            'name': '期末月结',
            'res_model': 'carry.forward.wizard',
            'views': [[False, "form"]],
            'res_id': self.id,
            'target': 'new'
        }



    def carry_forward(self):
        """单实例方法：期末月结，关闭会计期间

        :return: 前端特效
        """
        self.ensure_one()
        self.period_id.carry_forward()
        view = self.env.ref('account_ledger.PromptWizardViewForm')
        return {
            'type': 'ir.actions.act_window',
            'name': '月结完成',
            'res_model': 'prompt.wizard',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'target': 'new'
        }


    def carry_forward_cancel(self):
        """单实例方法：取消期末月结，开启会计期间

        :return: None
        """
        self.ensure_one()
        self.period_id.carry_forward_cancel()

    ####################
    # 继承方法
    ####################
    @api.model
    def default_get(self, fields_list):
        defaults = super(CarryForwardWizard, self).default_get(fields_list)
        fiscalyear = self.env['fr.account.fiscalyear'].search([('state', '=', 'activated')], limit=1, order='name')
        account = self.env['account.account'].search([('name', 'ilike', '本年利润'), ('fr_as_leaf', '=', True)], limit=1)
        journal = self.env['account.journal'].search([('name', 'ilike', '杂项')], limit=1)

        defaults.update({
            'fiscalyear_id': fiscalyear.id,
            'account_id': account.id,
            'journal_id': journal.id
        })

        return defaults


    ####################
    # onchange方法
    ####################
    @api.onchange('fiscalyear_id')
    def _change_period_id(self):
        """自动获取首个未结账期间"""
        if self.fiscalyear_id:
            periods = self.fiscalyear_id.period_ids.filtered(lambda period: period.state in ['open', 'ongoing'])

            if periods:
                self.period_id = periods[0]
                self.num_continue = self.period_id.check_move_num()
                self.carry_over_state = self.period_id.check_carry_over()

    ####################
    # 计算方法
    ####################
    @api.depends('period_id')
    def _compute_num_and_carry_over(self):
        if self.period_id:
            self.num_continue = self.period_id.check_move_num()
            self.carry_over_state = self.period_id.check_carry_over()
