<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="ReportInquiryWizardViewForm" model="ir.ui.view">
            <field name="name">报表查询向导表单视图</field>
            <field name="model">report.inquiry.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
<!--                        <group string="查询期间：">-->
<!--                            <group>-->
<!--                                <field name="fiscalyear_id" string="会计年度" domain="[('company_id','=',company_id)]"-->
<!--                                       options="{'no_create_edit': True, 'no_open': True}"/>-->
<!--                                <field name="period_state" string='期间状态'/>-->
<!--                                <field name="company_id" string="公司" invisible="1"/>-->
<!--                            </group>-->
<!--                            <group>-->
<!--                                <field name="period_id" string='会计期间' widget="selection"-->
<!--                                       domain="[('fiscalyear_id', '=', fiscalyear_id),-->
<!--                                       ('state', '!=', 'unuse'),-->
<!--                                       ('company_id','=',company_id)]"/>-->
<!--                            </group>-->
<!--                        </group>-->
                        <group string="查询期间：">
                            <field name="company_id" string="公司" invisible="1"/>
                            <div colspan="12">从：</div>
                            <group>
                                <field name="fiscalyear_from" string="会计年度"  domain="[('company_id','=',company_id)]"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                />
                                <field name="period_from_state" string='期间状态'/>
<!--                                       domain="[('company_id','=',company_id)]" -->
<!--                                />-->
<!--                                <field name="company_id" string="公司" invisible="1"/>/>-->
                            </group>
                            <group>
                                <field name="period_from" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_from), ('state', '!=', 'unuse'), ('company_id','=',company_id)]"/>
                            </group>
                            <div colspan="12">至：</div>
                            <group>
                                <field name="fiscalyear_to" string="会计年度"  domain="[('company_id','=',company_id)]"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                />
                                <field name="period_to_state" string='期间状态'/>
<!--                                       domain="[('company_id','=',company_id)]" -->
<!--                                />-->
                            </group>
                            <group>
                                <field name="period_to" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_to), ('state', '!=', 'unuse'), ('company_id','=',company_id)]"/>
                            </group>
                        </group>
                        <group string="报表：">
                            <group>
                                <field name="report_type"/>
                            </group>
                            <group>
                                <field name="report_id" domain="[('report_type', '=', report_type), ('company_id','=',company_id)]"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="confirm" type="object" class="btn-primary">
                            <i class="fa fa-search"/> 查询
                        </button>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="ALReportInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">资产负债表查询</field>
            <field name="res_model">report.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ReportInquiryWizardViewForm"/>
            <field name="context">{'default_report_type': 'assets_liability'}</field>
            <field name="target">new</field>
        </record>

        <record id="PRReportInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">利润表查询</field>
            <field name="res_model">report.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ReportInquiryWizardViewForm"/>
            <field name="context">{'default_report_type': 'profit'}</field>
            <field name="target">new</field>
        </record>

        <record id="CLReportInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">现金流量表查询</field>
            <field name="res_model">report.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ReportInquiryWizardViewForm"/>
            <field name="context">{'default_report_type': 'cash_flow'}</field>
            <field name="target">new</field>
        </record>

        <record id="OEReportInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">所有者权益变动表查询</field>
            <field name="res_model">report.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="ReportInquiryWizardViewForm"/>
            <field name="context">{'default_report_type': 'owner'}</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
