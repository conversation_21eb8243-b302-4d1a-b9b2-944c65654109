<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="CarryForwardWizardViewForm" model="ir.ui.view">
            <field name="name">期末月结向导表单视图</field>
            <field name="model">carry.forward.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                期末月结
                            </h1>
                        </div>
                        <group string="选择会计期间">
                            <group>
                                <field name="fiscalyear_id" string="会计年度" required="1"
                                       options="{'no_create': True, 'no_edit': True}"
                                       domain="[('state', '!=', 'draft'), ('company_id','=',company_id)]"/>
                                <field name="period_state" string='期间状态'/>
                                <field name="company_id" string="公司" invisible="1"/>
                            </group>
                            <group>
                                <field name="period_id" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_id), ('state', '!=', 'unuse')]"/>
                            </group>
                        </group>

                        <!--                        <group string="未过账凭证">-->
                        <!--                            <field name="unpost_move_ids" nolabel="1">-->
                        <!--                                <tree decoration-danger="state != 'posted'">-->
                        <!--                                    <field name="name" string="凭证编号"/>-->
                        <!--                                    <field name="date" string="日期"/>-->
                        <!--                                    <field name="create_uid" string="创建人"/>-->
                        <!--                                    <field name="journal_id" string="日记账"/>-->
                        <!--                                    <field name="line_ids" string="凭证明细"/>-->
                        <!--                                    <field name="state" string="状态"/>-->
                        <!--                                </tree>-->
                        <!--                            </field>-->
                        <!--                        </group>-->

                        <group string="编号整理">
                            <field name="num_continue" invisible="1"/>
                        </group>
                        <div class="alert alert-success" role="alert"
                             invisible="num_continue == False">
                            该期间凭证编号连续，无需进行编号整理。
                        </div>
                        <div class="alert alert-danger" role="alert"
                             invisible="num_continue == True">
                            该期间凭证编号不连续，点击<strong>编号整理</strong>可整理该期间所有凭证的编号.
                        </div>

                        <group string="损益结转">
                            <group>
                                <field name="account_id"/>
                                <field name="carry_over_state" invisible="0"/>
                            </group>
                            <group>
                                <field name="journal_id"/>
                            </group>
                        </group>
                        <div class="alert alert-success" role="alert"
                             invisible="carry_over_state == False">
                            该期间所有损益类科目余额为零，无需进行损益结转。
                        </div>
                        <div class="alert alert-danger" role="alert"
                             invisible="carry_over_state == True">
                            该期间存在损益类科目余额不为零，点击<strong>损益结转</strong>可结转期间损益类科目余额。
                        </div>
                    </sheet>
                    <footer>
                        <button name="reset_move_num" type="object" string="编号整理" class="btn-primary"
                                invisible="num_continue == True"/>

                        <button name="carry_over" type="object" string="损益结转" class="btn-primary"
                                invisible="carry_over_state == True"
                        />
                        <button name="carry_forward" type="object" string="确认月结" class="btn-primary"
                                invisible="period_state == 'close'"
                        />
                        <button name="carry_forward_cancel" type="object" string="取消月结" class="btn-primary"
                                invisible="period_state != 'close'"
                        />
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="CarryForwardWizardActionForm" model="ir.actions.act_window">
            <field name="name">期末月结</field>
            <field name="res_model">carry.forward.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="CarryForwardWizardViewForm"/>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
