<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="DetailInquiryWizardViewForm" model="ir.ui.view">
            <field name="name">明细账查询向导表单视图</field>
            <field name="model">detail.inquiry.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group string="查询期间：">
                            <div colspan="12">从：</div>
                            <group>
                                <field name="fiscalyear_from" string="会计年度"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                       domain="[('company_id','=',company_id)]"/>
                                <field name="company_id" string="公司" invisible="1"/>
                            </group>
                            <group>
                                <field name="period_from" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_from), ('state', '!=', 'unuse')]"/>
                            </group>
                            <div colspan="12">至：</div>
                            <group>
                                <field name="fiscalyear_to" string="会计年度"
                                       options="{'no_create_edit': True, 'no_edit': True}"
                                       domain="[('company_id','=',company_id)]"/>
                            </group>
                            <group>
                                <field name="period_to" string='会计期间' widget="selection"
                                       domain="[('fiscalyear_id', '=', fiscalyear_to), ('state', '!=', 'unuse')]"/>
                            </group>
                        </group>

                        <group string="查询科目：">
                            <field name="account_ids" string="会计科目" widget="many2many_tags"
                                   options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <div class="alert alert-info" role="alert">
                            提示：不选择会计科目则默认查询全部会计科目。
                        </div>

                        <group string="查询业务伙伴：">
                            <field name="partner_ids" string="业务伙伴" widget="many2many_tags"
                                   options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <div class="alert alert-info" role="alert">
                            提示：不选择业务伙伴则默认查询全部业务伙伴。
                        </div>

                    </sheet>
                    <footer>
                        <button name="confirm" type="object" class="btn-primary">
                            <i class="fa fa-search"/>
                            查询
                        </button>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="DetailInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">明细账查询</field>
            <field name="res_model">detail.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="DetailInquiryWizardViewForm"/>
            <field name="target">new</field>
        </record>
        <record id="DetailInquiryWizardActionServerForm" model="ir.actions.server">
            <field name="name">明细账查询</field>
            <field name="model_id" ref="account_ledger.model_detail_inquiry_wizard"/>
            <field name="binding_model_id" ref="account_ledger.model_detail_inquiry_wizard"/>
            <field name="state">code</field>
            <field name="code">
                action = env['detail.inquiry.wizard'].search([('id', '=', '-1')]).server_default_confirm()
            </field>
        </record>
    </data>
</odoo>
