# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError, MissingError

from ..models.utils import get_balance_direction


class DetailPrintWizard(models.TransientModel):
    """明细帐打印向导"""
    _name = 'detail.print.wizard'
    _description = '明细帐打印向导'

    def _default_fiscalyear_id(self):
        fiscalyears = self.env['fr.account.fiscalyear'].search([('state', '=', 'activated')])
        if len(fiscalyears) > 1:
            return fiscalyears[-1]
        else:
            return fiscalyears

    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度', default=_default_fiscalyear_id, required=True)
    account_ids = fields.Many2many('account.account', string='会计科目')


    def confirm(self):
        # 参数校验
        self.ensure_one()
        if not self.account_ids:
            raise MissingError('缺少要打印明细账的科目！')

        # 生成临时明细账报表记录集
        vals_list = []
        for account in self.account_ids:
            move_lines = self.env['account.move.line'].search(
                [('account_id', '=', account.id), ('fr_fiscalyear_id', '=', self.fiscalyear_id.id)], order='date')

            # 应收应付科目打印
            if account.internal_type in ['receivable', 'payable']:
                for partner in move_lines.mapped('partner_id'):
                    partner_move_lines = move_lines.filtered(lambda line: line.partner_id == partner)
                    vals_list.append(self._get_report_account_detail_vals(partner_move_lines, account, partner))

            # 其他科目打印
            else:
                vals_list.append(self._get_report_account_detail_vals(move_lines, account))

        # 创建记录集

        report_recs = self.env['report.account.detail'].create(vals_list)
        # 获取报表
        act_report = self.env.ref('account_ledger.ReportAccountDetailAction')

        report = act_report.report_action(report_recs, config=False)

        return report

    @api.model
    def _get_report_account_detail_vals(self, move_lines, account, partner=None):
        # 处理有无partner参数
        if partner:
            partner_id = partner.id
            move_lines = move_lines.filtered(lambda line: line.partner_id == partner)
            previous_lines = self.env['account.move.line'].search(
                [('date', '<', self.fiscalyear_id.date_start), ('account_id', '=', account.id),
                 ('partner_id', '=', partner.id)])

        else:
            partner_id = None
            previous_lines = self.env['account.move.line'].search(
                [('date', '<', self.fiscalyear_id.date_start), ('account_id', '=', account.id)])

        line_vals_list = []

        # 计算初始余额和方向
        balance_debit = sum(previous_lines.mapped('balance'))
        balance, direction = get_balance_direction(balance_debit)
        line_vals_list.append((0, 0, {
            'name': '上年结转',
            'direction': direction,
            'balance': balance,
        }))

        # 按会计期间添加明细
        debit_fiscalyear = credit_fiscalyear = 0
        for period in self.fiscalyear_id.period_ids:
            period_move_lines = move_lines.filtered(lambda line: line.fr_period_id == period)
            if not period_move_lines:
                continue

            debit_period = credit_period = 0
            # 添加期间明细
            for move_line in period_move_lines:
                # 计算余额和方向
                balance_debit += move_line.balance
                balance, direction = get_balance_direction(balance_debit)

                # 添加明细行
                line_vals_list.append((0, 0, {
                    'date': move_line.date.strftime('%m月%d日'),
                    'move': move_line.move_id.num,
                    'name': move_line.name,
                    'debit': move_line.debit,
                    'credit': move_line.credit,
                    'direction': direction,
                    'balance': balance,
                }))

                # 更新本期合计
                debit_period += move_line.debit
                credit_period += move_line.credit

                # 更新本年累计
                debit_fiscalyear += move_line.debit
                credit_fiscalyear += move_line.credit

            # 添加本期合计
            line_vals_list.append((0, 0, {
                'name': '本期合计',
                'debit': debit_period,
                'credit': credit_period,
                'direction': direction,
                'balance': balance,
            }))

            # 添加本年累计
            line_vals_list.append((0, 0, {
                'name': '本年累计',
                'debit': debit_fiscalyear,
                'credit': credit_fiscalyear,
                'direction': direction,
                'balance': balance,
            }))

        return {
            'account_id': account.id,
            'fiscalyear_id': self.fiscalyear_id.id,
            'partner_id': partner_id,
            'line_ids': line_vals_list,
        }
