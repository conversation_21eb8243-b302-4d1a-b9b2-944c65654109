#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from odoo import api, models, fields
from odoo.exceptions import ValidationError


class ReportInquiryWizard(models.TransientModel):
    """报表查询向导"""
    _name = 'report.inquiry.wizard'
    _description = '报表查询向导'

    def _default_fiscalyear_id(self):
        fiscalyears = self.env['fr.account.fiscalyear'].search([
            ('state', '=', 'activated'),
            ('company_id', '=', self.env.company.id),
            ('name', '=', str(datetime.date.today().year)),
        ])
        if len(fiscalyears) > 1:
            return fiscalyears[-1]
        else:
            return fiscalyears

    def _default_fiscalyear_domain(self):
        return [('company_id', '=', self.company_id.id)]

    # 基础字段
    report_type = fields.Selection([
        ('assets_liability', '资产负债表'),
        ('profit', '利润表'),
        ('cash_flow', '现金流量表'),
        ('owner', '所有者权益变动表'),
    ], string='报表类型', required=True)

    # 关系字段
    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度', default=_default_fiscalyear_id)
    period_id = fields.Many2one('fr.account.period', string='会计期间')
    report_id = fields.Many2one('account.ledger.report', string='报表模板', required=True, ondelete='cascade')

    fiscalyear_from = fields.Many2one('fr.account.fiscalyear', string='开始年度',
                                      default=_default_fiscalyear_id, required=True,
                                      ondelete='cascade')
    fiscalyear_to = fields.Many2one('fr.account.fiscalyear', string='结束年度',
                                    default=_default_fiscalyear_id, required=True,
                                    ondelete='cascade')
    period_from = fields.Many2one('fr.account.period', string='开始期间', required=True, ondelete='cascade')
    period_to = fields.Many2one('fr.account.period', string='结束期间', required=True, ondelete='cascade')

    period_from_state = fields.Selection(related='period_from.state', string='期间状态')
    period_to_state = fields.Selection(related='period_to.state', string='期间状态')

    # 关联字段
    period_state = fields.Selection(related='period_id.state', string='期间状态')

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)

    def confirm(self):
        """单实例方法：结转期间对应的损益科目总账

        :return: 更新后的当前视图
        """
        self.ensure_one()
        # periods = self.env['fr.account.period'].search(
        #     [('date_start', '>=', self.period_from.date_start),
        #      ('date_start', '<=', self.period_to.date_start),
        #      ('company_id', '=', self.env.company.id)])
        return {
            'type': 'ir.actions.client',
            'tag': 'ledger_report',
            'target': 'current',
            # 'name': self.report_id.name + '：' + self.period_id.name,
            'context': {
                'report_id': self.report_id.id,
                'period_from_id': self.period_from.id,
                'period_to_id': self.period_to.id
            }
        }

    @api.onchange('fiscalyear_from')
    def _change_period_from(self):
        """自动获取开始期间"""
        if self.fiscalyear_from:
            period = self.env['fr.account.period'].search(
                [('fiscalyear_id', '=', self.fiscalyear_from.id), ('state', '!=', 'unuse')], limit=1,
                offset=datetime.date.today().month - 1)
            self.period_from = period

    @api.onchange('fiscalyear_to')
    def _change_period_to(self):
        """自动获取结束期间"""
        if self.fiscalyear_to:
            period = self.env['fr.account.period'].search(
                [('fiscalyear_id', '=', self.fiscalyear_to.id), ('state', '!=', 'unuse')], limit=1,
                offset=datetime.date.today().month - 1)
            self.period_to = period

    @api.onchange('report_type')
    def _change_report_id(self):
        """自动获取报表"""
        if self.report_type:
            default_report_id = self.env.context.get('default_report_id', [])
            report = self.env['account.ledger.report'].browse(default_report_id)
            if report.report_type == self.report_type:
                self.report_id = report
            else:
                self.report_id = self.env['account.ledger.report'].search(
                    [('report_type', '=', self.report_type)], order='id desc', limit=1)

    @api.onchange('fiscalyear_id')
    def _change_period_id(self):
        """自动获取首个未结账期间"""
        if self.fiscalyear_id:
            periods = self.fiscalyear_id.period_ids.filtered(lambda period: period.state != 'unuse')
            if periods:
                # if len(periods) == 12:
                #     self.period_id = periods[datetime.date.today().month]
                self.period_id = periods[0]
