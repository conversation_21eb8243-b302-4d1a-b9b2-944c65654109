# -*- coding: utf-8 -*-
from odoo import api, models, fields
from odoo.exceptions import UserError, MissingError


class NumberSortWizard(models.TransientModel):
    """编号整理"""
    _name = 'number.sort.wizard'
    _description = '编号整理向导'

    number_rearrange = fields.<PERSON><PERSON>an(string='凭证号重排')
    date_rearrange = fields.<PERSON><PERSON><PERSON>(string='凭证日期重排')
    approve_rearrange = fields.<PERSON><PERSON><PERSON>(string='审核日期重排')
    period_id = fields.Many2one('fr.account.period', string='会计期间', ondelete='cascade')

    @api.onchange('number_rearrange')
    def onchange_select_number_rearrange(self):
        if self.number_rearrange:
            self.date_rearrange = False
            self.approve_rearrange = False

    @api.onchange('date_rearrange')
    def onchange_select_date_rearrange(self):
        if self.date_rearrange:
            self.number_rearrange = False
            self.approve_rearrange = False

    @api.onchange('approve_rearrange')
    def onchange_select_approve_rearrange(self):
        if self.approve_rearrange:
            self.number_rearrange = False
            self.date_rearrange = False


    def reset_move_num_wizard(self):
        """单实例方法：期间凭证编号整
        :return: 更新后的当前视图
        """
        self.ensure_one()
        if self.date_rearrange:
            # 【凭证编号重排】
            self.period_id.reset_move_num_next()
        if self.number_rearrange:
            self.period_id.reset_move_num()
        if self.approve_rearrange:
            self.period_id.reset_move_num_approved()
        view = self.env.ref('account_ledger.CarryForwardWizardViewForm')
        return {
            'type': 'ir.actions.act_window',
            'name': '期末月结',
            'res_model': 'carry.forward.wizard',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'context': {
                'default_period_id': self.period_id.id
            },
            'target': 'new'
        }

