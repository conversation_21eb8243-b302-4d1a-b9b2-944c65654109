<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--表单视图-->
        <record id="BalanceInquiryWizardViewForm" model="ir.ui.view">
            <field name="name">科目余额表查询向导表单视图</field>
            <field name="model">balance.inquiry.wizard</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group string="公司">
                                  <field name="company_id" string='公司' required="1"/>
                            </group>

                        </group>
                        <group string="查询期间：">
                            <div colspan="12" class="">从：</div>
                            <group class="mt0 mb0">
                                <field name="fiscalyear_from" string="会计年度" invisible="not company_id"
                                       options="{'no_create_edit': True, 'no_edit': True}" domain="[('company_id','=',company_id)]" />
                                <field name="company_id" string="公司" required="1" invisible="1"/>
                            </group>
                            <group class="mt0 mb0">
                                <field name="period_from" string='会计期间' widget="selection" invisible="not company_id"
                                       domain="[('fiscalyear_id', '=', fiscalyear_from), ('state', '!=', 'unuse')]"/>
                            </group>
                            <div colspan="12">至：</div>
                            <group class="mt0 mb0">
                                <field name="fiscalyear_to" string="会计年度" invisible="not company_id"
                                       options="{'no_create_edit': True, 'no_edit': True}" domain="[('company_id','=',company_id)]"/>
                            </group>
                            <group class="mt0 mb0">
                                <field name="period_to" string='会计期间' widget="selection" invisible="not company_id"
                                       domain="[('fiscalyear_id', '=', fiscalyear_to), ('state', '!=', 'unuse')]"/>
                            </group>
                        </group>

                        <group string="查询科目：" class="mt0" invisible="not company_id">
                            <group class="mt0">
                                <field name="account_from" options="{'no_create': True, 'no_edit': True}"/>
                            </group>
                            <group class="mt0">
                                <field name="account_to" options="{'no_create': True, 'no_edit': True}"/>
                            </group>

                            <group class="mt0">
                                <field name="level_from" options="{'no_create': True, 'no_edit': True}"/>
                            </group>
                            <group class="mt0">
                                <field name="level_to" options="{'no_create': True, 'no_edit': True}"/>
                            </group>

                            <group class="mt0">
                                <field name="internal_group"/>
                            </group>

                            <group class="mt0">
                                <field name="user_type_id" widget="selection"/>
                            </group>

                            <group class="mt0">
                                <field name="level_is" string="科目等级查询(非连续)"/>
                            </group>

                            <group class="mt0">

                            </group>


                            <group class="mt0">
                                <field name="level_from_one" string="总分类科目"
                                       options="{'no_create': True, 'no_edit': True}"
                                       readonly="1" force_save="1"
                                       invisible="level_is == False"
                                />
                            </group>

                            <group class="mt0">
                                <field name="level_to_all" string="其他科目等级"
                                       options="{'no_create': True, 'no_edit': True}"
                                       invisible="level_is == False" required="level_is == True"
                                />
                            </group>

                        </group>
                        <group string="选项：" col="6">
                            <group colspan="2" class="mt0">
                                <field name="include_unpost"/>
                            </group>
                            <group colspan="2" class="mt0">
                                <field name="hidden_no_occur"/>
                            </group>
                            <group colspan="2" class="mt0">
                                <field name="hidden_no_balance"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="confirm" type="object" class="btn-primary">
                            <i class="fa fa-search"/> 查询
                        </button>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <!--窗口动作-->
        <record id="BalanceInquiryWizardActionForm" model="ir.actions.act_window">
            <field name="name">科目余额表查询</field>
            <field name="res_model">balance.inquiry.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="BalanceInquiryWizardViewForm"/>
            <field name="target">new</field>
        </record>

        <!--列表视图-->
        <record id="AccountBalanceTempViewList" model="ir.ui.view">
            <field name="name">科目余额表查询结果列表视图</field>
            <field name="model">account.balance.temp</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree js_class="balance_list" create="false" import="false" delete="false">
                    <field name="account_id"/>
<!--                    <field name="partner_id"/>-->
<!--                    <field name="analytic_account_id"/>-->
<!--                    <field name="analytic_tag_ids"/>-->


                    <field name="debit_start"/>
                    <field name="credit_start"/>
                    <field name="debit_occur"/>
                    <field name="credit_occur"/>
                    <field name="debit_end"/>
                    <field name="credit_end"/>
                    <field name="internal_group" invisible="1"/>
                    <field name="account_level" invisible="1"/>
                    <field name="date_start" invisible="1"/>
                    <field name="date_end" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>
<!--        余额表-->
        <record id="AccountBalanceTempViewListYeb" model="ir.ui.view">
            <field name="name">科目余额表查询结果列表视图</field>
            <field name="model">account.balance.temp</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree js_class="balance_list_yeb" create="false" import="false" delete="false">
                    <field name="account_id"/>
                    <field name="debit_start"/>
                    <field name="credit_start"/>
                    <field name="debit_occur"/>
                    <field name="credit_occur"/>
                    <field name="debit_end"/>
                    <field name="credit_end"/>
                    <field name="internal_group" invisible="1"/>
                    <field name="account_level" invisible="1"/>
                    <field name="date_start" invisible="1"/>
                    <field name="date_end" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>


        <!--搜索视图-->
        <record id="AccountBalanceTempViewSearch" model="ir.ui.view">
            <field name="name">科目余额表查询结果搜索视图</field>
            <field name="model">account.balance.temp</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search string="Accounts">
                    <field name="account_id" string="会计科目" filter_domain="['|', ('account_id.name','ilike',self), ('account_id.code','ilike',self)]"/>
                    <filter name="top_level" string="仅一级科目" domain="[('account_level', '=', 1)]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter name="internal_group" string="会计要素" context="{'group_by': 'internal_group'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--窗口动作-->
        <record id="AccountBalanceTempViewActionList" model="ir.actions.act_window">
            <field name="name">科目余额表</field>
            <field name="res_model">account.balance.temp</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="AccountBalanceTempViewList"/>
            <field name="domain">[]</field>
            <field name="context">{'search_default_internal_group': 1}</field>
            <field name="target">current</field>
            <field name="limit">1000</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    无结果显示。
                </p>
            </field>
        </record>

<!--  客户余额表信息      -->
        <record id="PartnerBalanceTree" model="ir.ui.view">
           <field name="name">客户余额表查询结果</field>
            <field name="model">fr.partner.balance</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree js_class="balance_list_yeb" create="false" edit="false" delete="false" import="false"
                      default_order="id">
                    <field name="account_id" string="会计科目"/>
                    <field name="partner_id" widget="partner_ledger" string="业务伙伴"/>
                    <field name="beginning" sum="期初总计" widget="minus_monetary"/>
                    <field name="debit" sum="贷方总计" widget="minus_monetary"/>
                    <field name="credit" sum="贷方总计" widget="minus_monetary"/>
                    <field name="balance" sum="余额总计" widget="minus_monetary"/>
                    <field name="company_id" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="account_balance_id" string='余额表id' invisible="1"/>
                    <field name="date_start" string='查询开始期间' invisible="1"/>
                    <field name="date_end" string='查询结束期间' invisible="1"/>
                </tree>
            </field>
        </record>
<!--   客户余额搜索视图-->
        <record id="PartnerBalanceViewSearch" model="ir.ui.view">
            <field name="name">客户余额表查询结果搜索视图</field>
            <field name="model">fr.partner.balance</field>
            <field name="arch" type="xml">
                <search>
                    <field name="partner_id" string="业务伙伴"/>
                    <field name="account_id" string="会计科目"/>
                    <group>
                        <filter string="业务伙伴" name="partner_id_group" context="{'group_by': 'partner_id'}"/>
                    </group>
                </search>
            </field>
        </record>

    </data>
</odoo>
