<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="group_accounting_module_hidden" model="res.groups">
            <field name="name">会计模块隐藏群组</field>
        </record>

        <record id="group_certificates_invalid" model="res.groups">
            <field name="name">会计凭证作废按钮-可见群组</field>
        </record>

        <record id="account.menu_action_account_form" model="ir.ui.menu">
            <field name="groups_id" eval="[(6,0,[ref('account_ledger.group_accounting_module_hidden')])]"/>
        </record>
        <record id="account.menu_action_tax_form" model="ir.ui.menu">
            <field name="groups_id" eval="[(6,0,[ref('account_ledger.group_accounting_module_hidden')])]"/>
        </record>
        <record id="account.menu_action_account_journal_form" model="ir.ui.menu">
            <field name="groups_id" eval="[(6,0,[ref('account_ledger.group_accounting_module_hidden')])]"/>
        </record>
        <record id="account.menu_action_move_journal_line_form" model="ir.ui.menu">
            <field name="groups_id" eval="[(6,0,[ref('account_ledger.group_accounting_module_hidden')])]"/>
        </record>

        <record id="fiscalyear_comp_rule" model="ir.rule">
            <field name="name">会计日历多公司权限</field>
            <field name="model_id" ref="model_fr_account_fiscalyear"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="account_fiscal_year_comp_rule" model="ir.rule">
            <field name="name">会计年度多公司权限</field>
            <field name="model_id" ref="account_accountant.model_account_fiscal_year"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>

        </record>


        <!-- <record id="move_template_comp_rule" model="ir.rule">
            <field name="name">凭证模板多公司权限</field>
            <field name="model_id" ref="model_account_move_template"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
        </record> -->

        <record id="ledger_report_rule" model="ir.rule">
            <field name="name">财务报表多公司权限</field>
            <field name="model_id" ref="model_account_ledger_report"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>

        <record id="ledger_value_rule" model="ir.rule">
            <field name="name">财务报表取值多公司权限</field>
            <field name="model_id" ref="model_account_ledger_value"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]
            </field>
        </record>

        <record id="analytic_category_comp_rule" model="ir.rule">
            <field name="name">分析类别多公司权限</field>
            <field name="model_id" ref="model_account_analytic_category"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]
            </field>
        </record>

        <record id="cash_flow_comp_rule" model="ir.rule">
            <field name="name">现金流量项目多公司权限</field>
            <field name="model_id" ref="account_ledger.model_account_cash_flow"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>
