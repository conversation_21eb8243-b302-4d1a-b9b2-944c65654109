# -*- coding: utf-8 -*-
{
    'name': "财务总账",

    'summary': """
        财务总账模块""",

    'description': """
        odoo会计中国化扩展
    """,

    'author': "openerp.hk Team",
    'website': "http://cdn.openerp.hk",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/13.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': '财务管理/财务管理',
    'version': '********',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account', 'account_accountant', 'account_asset', 'account_followup'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'security/security.xml',
        'views/account_move_voucher_template.xml',
        'views/account_account_type_views.xml',
        'views/account_account_views.xml',
        'views/account_bank_statement.xml',
        'views/account_cash_flow_views.xml',
        'views/account_fiscalyear_views.xml',
        'views/account_payment.xml',
        'views/account_tax_invoice.xml',
        'views/account_ledger_report_views.xml',
        'views/account_analytic_category_views.xml',
        'views/account_analytic_tag_views.xml',
        'wizard/move_template_save_views.xml',
        'views/account_payment_views.xml',
        'views/account_ledger_value_views.xml',
        'views/account_move_template_views.xml',
        'views/account_period_views.xml',
        'views/account_move_line_views.xml',
        'views/account_move_views.xml',
        'views/inherit_account_move_view.xml',
        'views/inherit_account_journal_views.xml',
        # 'views/ir_sequence_extend.xml',
        'report/report_paper_format.xml',
        'report/report_account_detail.xml',
        'report/report_account_move.xml',
        'report/report_account_ledger.xml',

        'report/report_balance_temp.xml',
        'wizard/report_inquiry.xml',
        'wizard/number_sort_views.xml',
        'wizard/carry_forward_views.xml',
        'wizard/balance_inquiry_views.xml',
        'wizard/detail_print_views.xml',
        'wizard/ledger_inquiry_views.xml',
        'wizard/ledger_print_views.xml',
        'wizard/detail_inquiry_views.xml',
        'wizard/move_template_save_views.xml',
        'wizard/prompt.xml',
        'wizard/quick_add.xml',
        'wizard/manual_no.xml',
        'views/manual_no_list.xml',
        # 'views/templates.xml',
        'views/menu.xml',
        'data/account_account_type.xml',
        'data/account_analytic_category.xml',
        'data/account_move_line_column.xml',
        'data/assets_liability_report.xml',
        'data/cash_flow_report.xml',
        'data/profit_report.xml',
        'data/value_type.xml',

    ],
    'assets': {
        'web.assets_backend': [
            'account_ledger/static/src/css/libs/jexcel.css',
            "account_ledger/static/src/css/libs/jsuites.css",
            "account_ledger/static/src/css/fonts.css",
            "account_ledger/static/src/css/jexcel_patch.css",
            "account_ledger/static/src/css/account_table.css",
            "account_ledger/static/src/js/libs/*.js",

            # 字段 显示组件
            "account_ledger/static/src/js/fields/minus_monetary.js",
            "account_ledger/static/src/js/fields/minus_monetary.xml",
            "account_ledger/static/src/js/fields/partner_ledger.js",
            # 报表
            "account_ledger/static/src/js/reports/account_report_odoo17.js",
            "account_ledger/static/src/js/reports/account_report_odoo17.xml",
            "account_ledger/static/src/js/views/list_balance_17.js",
            "account_ledger/static/src/js/views/list_button_report_17.xml",
            "account_ledger/static/src/js/views/list_balance_yeb_17.js",
            "account_ledger/static/src/js/views/list_detail_17.js",
            "account_ledger/static/src/js/views/list_ledger_17.js",
            "account_ledger/static/src/js/views/custom_table.js",
            # 'account_ledger/static/views/**/*',
        ]
    },
    'application': True,
    'installable': True,
    'auto_install': False,
    'sequence': 1,
    'license': 'LGPL-3',
}
