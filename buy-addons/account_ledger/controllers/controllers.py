# -*- coding: utf-8 -*-
# from odoo import http


# class AccountLedger(http.Controller):
#     @http.route('/account_ledger/account_ledger/', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/account_ledger/account_ledger/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('account_ledger.listing', {
#             'root': '/account_ledger/account_ledger',
#             'objects': http.request.env['account_ledger.account_ledger'].search([]),
#         })

#     @http.route('/account_ledger/account_ledger/objects/<model("account_ledger.account_ledger"):obj>/', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('account_ledger.object', {
#             'object': obj
#         })
