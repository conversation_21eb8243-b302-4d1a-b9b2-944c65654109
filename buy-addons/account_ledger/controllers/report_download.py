import json
from io import BytesIO

from odoo import http
from odoo.http import Controller, route, request


def get_value_data(value):
    return {
        "name": value.name,
        "from_type": value.from_type,
        "report_type": value.report_type,
    }


def handle_value(value):
    if not value.from_type:
        return None
    result = []
    if value.from_type == 'ledger_line':
        for line in value.ledger_line_ids:
            result.append({
                "value_sign": line.value_sign,
                "account_code": line.account_id.code,
                "value_type": line.value_type.name
            })
        value_data = get_value_data(value)
        value_data['ledger_line_ids'] = result
        return value_data
    elif value.from_type == 'cash_flow':
        for line in value.cash_flow_ids:
            result.append({
                "value_sign": line.value_sign,
                "cash_flow_name": line.cash_flow_id.name,
                "value_type": line.value_type.name
            })
        value_data = get_value_data(value)
        value_data['cash_flow_ids'] = result
        return value_data
    else:
        for line in value.other_value_ids:
            result.append({
                "value_sign": line.value_sign,
                "value_from_name": line.value_from_id.name,
            })
        value_data = get_value_data(value)
        value_data['other_value_ids'] = result
        return value_data


def handle_cash_flow(record):
    general_item_ids = []
    for general_item in record.general_item_ids:
        value_1_data = None
        value_2_data = None

        if general_item.value_1:
            value_1_result = []
            for line in general_item.value_1.cash_flow_ids:
                value_1_result.append({
                    "value_sign": line.value_sign,
                    "account_code": line.account_id.code,
                    "value_type": line.value_type.name,
                    'cash_flow_name': line.cash_flow_id.name
                })

            value_1_data = get_value_data(general_item.value_1)
            value_1_data['ledger_line_ids'] = value_1_result

        if general_item.value_2:
            value_2_result = []
            for line in general_item.value_2.cash_flow_ids:
                value_2_result.append({
                    "value_sign": line.value_sign,
                    "account_code": line.account_id.code,
                    "value_type": line.value_type.name,
                    'cash_flow_name': line.cash_flow_id.name
                })
            value_2_data = {
                "name": general_item.value_2.name,
                "from_type": general_item.value_2.from_type,
                "report_type": general_item.value_2.report_type,
                "ledger_line_ids": value_2_result
            }

        general_item_ids.append({
            "name": general_item.name,
            "code": general_item.code,
            "value_1": value_1_data,
            "value_2": value_2_data
        })

    return {
        "general_item_ids": general_item_ids
    }


def handle_profit(record):
    general_item_ids = []
    for general_item in record.general_item_ids:
        value_1_data = None
        value_2_data = None

        if general_item.value_1:
            value_1_data = handle_value(general_item.value_1)

        if general_item.value_2:
            value_2_data = handle_value(general_item.value_2)

        general_item_ids.append({
            "name": general_item.name,
            "code": general_item.code,
            "value_1": value_1_data,
            "value_2": value_2_data
        })

    return {
        "general_item_ids": general_item_ids
    }


def handle_assets_liability(record):
    assets_item_ids = []
    liability_item_ids = []
    for assets_item in record.assets_item_ids:
        value_1_data = None
        value_2_data = None

        if assets_item.value_1:
            value_1_data = handle_value(assets_item.value_1)
        if assets_item.value_2:
            value_2_data = handle_value(assets_item.value_2)

        assets_item_ids.append({
            "name": assets_item.name,
            "code": assets_item.code,
            "value_1": value_1_data,
            "value_2": value_2_data
        })
    for liability_item in record.liability_item_ids:
        value_1_data = None
        value_2_data = None

        if liability_item.value_1:
            value_1_data = handle_value(liability_item.value_1)
        if liability_item.value_2:
            value_2_data = handle_value(liability_item.value_2)

        liability_item_ids.append({
            "name": liability_item.name,
            "code": liability_item.code,
            "value_1": value_1_data,
            "value_2": value_2_data
        })

    return {
        "assets_item_ids": assets_item_ids,
        "liability_item_ids": liability_item_ids
    }


def handle_owner(record):
    result = []
    for general_item in record.general_item_ids:
        value_3_data = None
        value_4_data = None
        value_5_data = None
        value_6_data = None
        value_7_data = None
        value_8_data = None
        value_9_data = None
        value_10_data = None
        value_11_data = None
        value_12_data = None
        value_13_data = None
        value_14_data = None
        if general_item.value_3:
            value_3_data = handle_value(general_item.value_3)
        if general_item.value_4:
            value_4_data = handle_value(general_item.value_4)
        if general_item.value_5:
            value_5_data = handle_value(general_item.value_5)
        if general_item.value_6:
            value_6_data = handle_value(general_item.value_6)
        if general_item.value_7:
            value_7_data = handle_value(general_item.value_7)
        if general_item.value_8:
            value_8_data = handle_value(general_item.value_8)
        if general_item.value_9:
            value_9_data = handle_value(general_item.value_9)
        if general_item.value_10:
            value_10_data = handle_value(general_item.value_10)
        if general_item.value_11:
            value_11_data = handle_value(general_item.value_11)
        if general_item.value_12:
            value_12_data = handle_value(general_item.value_12)
        if general_item.value_13:
            value_13_data = handle_value(general_item.value_13)
        if general_item.value_14:
            value_14_data = handle_value(general_item.value_14)

        result.append({
            "name": general_item.name,
            "code": general_item.code,
            "value_3": value_3_data,
            "value_4": value_4_data,
            "value_5": value_5_data,
            "value_6": value_6_data,
            "value_7": value_7_data,
            "value_8": value_8_data,
            "value_9": value_9_data,
            "value_10": value_10_data,
            "value_11": value_11_data,
            "value_12": value_12_data,
            "value_13": value_13_data,
            "value_14": value_14_data,
        })
    return {
        "general_item_ids": result
    }


class ReportJSONDownload(Controller):
    @route('/web/api/report/json', csrf=False, methods=['GET'], auth="public")
    def download_json(self, **params):
        result = []

        db_data = request.env['account.ledger.report'].search(
            [['name', 'in', ['利润表', '现金流量表', '资产负债表', '所有者权益变动表']]])

        for record in db_data:
            if record.report_type == 'assets_liability':
                data = handle_assets_liability(record)
                item_data_result = {
                    'name': record.name,
                    'report_type': record.report_type,
                    'cancel_out': record.cancel_out
                }
                item_data_result.update(data)
                result.append(item_data_result)
            elif record.report_type == 'profit':
                data = handle_profit(record)
                item_data_result = {
                    'name': record.name,
                    'report_type': record.report_type,
                    'cancel_out': record.cancel_out
                }
                item_data_result.update(data)
                result.append(item_data_result)

            elif record.report_type == 'cash_flow':
                data = handle_cash_flow(record)
                item_data_result = {
                    'name': record.name,
                    'report_type': record.report_type,
                    'cancel_out': record.cancel_out
                }
                item_data_result.update(data)
                result.append(item_data_result)

            elif record.report_type == 'owner':
                data = handle_owner(record)
                item_data_result = {
                    'name': record.name,
                    'report_type': record.report_type,
                    'cancel_out': record.cancel_out
                }
                item_data_result.update(data)
                result.append(item_data_result)

        json_str = json.dumps(result, ensure_ascii=False)

        if params.get("format") == 'json':
            return request.make_response(json_str, headers={
                "Content-Type": "application/json"
            })

        name = params.get("name", "报表导出")

        stream = BytesIO(json_str.encode())
        filename = "%s.json" % name

        return http.send_file(stream, filename=filename, mimetype='application/octet-stream')
