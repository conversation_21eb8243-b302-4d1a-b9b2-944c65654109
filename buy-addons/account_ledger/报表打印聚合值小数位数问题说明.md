### 此方法问题

        def _write_group_header(self, row, column, label, group, group_depth=0):
                aggregates = group.aggregated_values

        label = '%s%s (%s)' % ('    ' * group_depth, label, group.count)
        self.write(row, column, label, self.header_bold_style)
        for field in self.fields[1:]: # No aggregates allowed in the first column because of the group title
            column += 1
            aggregated_value = aggregates.get(field['name'])
            # Non-stored float fields may not be displayed properly because of float representation
            # => we force 2 digits
            # if not field.get('store') and isinstance(aggregated_value, float):
            # 上一行为原生，此处修改，是因为，在余额表查询时候，发现下载的excel是多位小数，这里需要修改如下。
            if isinstance(aggregated_value, float):
                aggregated_value = float_repr(aggregated_value, 2)
            self.write(row, column, str(aggregated_value if aggregated_value is not None else ''), self.header_bold_style)
        return row + 1, 0