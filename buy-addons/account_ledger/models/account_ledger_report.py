# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal

from odoo import api, fields, models, http
from odoo.exceptions import ValidationError, UserError, MissingError


class AccountLedgerReport(models.Model):
    _name = 'account.ledger.report'
    _description = '财务报表模板'

    # 基础字段
    name = fields.Char(string='报表名称')
    report_type = fields.Selection([
        ('assets_liability', '资产负债表'),
        ('profit', '利润表'),
        ('cash_flow', '现金流量表'),
        ('owner', '所有者权益变动表'),
    ], string='报表类型', required=True)
    # 该字段用于，设置现金流量报表配置，期初值，开关是否在现金流量报表中添加期初
    cash_flow_open = fields.Boolean(string='现金流量期初', default=False)

    note = fields.Text(string='说明')
    is_template = fields.Boolean(string='作为模板', default=False, copy=False)
    cancel_out = fields.Boolean(string='启用重分类')

    # 关系字段
    cancel_out_ids = fields.One2many('account.cancel.out', 'report_id', string='科目重分类', copy=True)
    general_item_ids = fields.One2many('account.ledger.report.item', 'report_id', string='项目',
                                       domain=[('col_in', '=', 'general')])
    assets_item_ids = fields.One2many('account.ledger.report.item', 'report_id', string='资产',
                                      domain=[('col_in', '=', 'assets')])
    liability_item_ids = fields.One2many('account.ledger.report.item', 'report_id', string='负债',
                                         domain=[('col_in', '=', 'liability')])

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)

    # ===================
    # 公用方法
    # ===================

    def get_report_data(self, period_from_id, period_to_id):
        """单实例方法：获取报表数据

        :param period_id: int, 会计期间ID
        :return : 报表数据
        """
        # 参数校验
        if not self:
            return []

        self.ensure_one()

        # period = self.env['fr.account.period'].browse(period_id)
        period_from = self.env['fr.account.period'].browse(period_from_id)
        period_to = self.env['fr.account.period'].browse(period_to_id)
        self.company_id = self.env.company.id
        # for period in periods:
        #     if period.company_id != self.env.company:
        #         raise ValidationError('会计期间和报表所属公司不匹配！')
        # if period.company_id != self.env.company:
        #     raise ValidationError('会计期间和报表所属公司不匹配！')
        # 资产负债表,数据获取
        if self.report_type == 'assets_liability':
            report_data = self._get_assets_liability_data(period_from, period_to)
        # 利润表- 现金流量查询
        elif self.report_type == 'profit' or self.report_type == 'cash_flow':
            report_data = self._get_profit_cash_flow_data(period_from, period_to)
        elif self.report_type == 'owner':
            report_data = self._get_owner_equity_report_data(period_from, period_to)
        else:
            raise ValidationError('报表类型错误！')
        return {
            'company': self.company_id.name,
            'report_type': self.report_type,
            'period_name': f"{period_from.name}至{period_to.name}",
            'period_start': fields.Date.to_string(period_from.date_start),
            'period_end': fields.Date.to_string(period_to.date_end),
            'report_data': report_data,
        }

    def get_co_accounts(self):
        self.ensure_one()

        accounts = self.env['account.account']
        if self.cancel_out is True:
            accounts = self.cancel_out_ids.mapped('deb_account_id') + self.cancel_out_ids.mapped('cre_account_id')

        return accounts

    # ===================
    # 继承方法
    # ===================

    @api.returns('self', lambda value: value.id)
    def copy(self, default=None):

        if self.name[-4:] == '（预设）':
            new_name = self.name[:-4]
        else:
            new_name = self.name

        report_copy = super(AccountLedgerReport, self).copy(
            {'name': new_name, 'company_id': self.env.user.company_id.id})

        # 同时复制明细行
        for item in self.general_item_ids + self.assets_item_ids + self.liability_item_ids:
            item.copy({'report_id': report_copy.id, 'value_1': None, 'value_2': None})

        return report_copy

    # ===================
    # 视图方法
    # ===================

    def view_report(self):
        """单实例方法：预览报表"""
        self.ensure_one()

        return {
            "type": "ir.actions.act_window",
            "name": "报表查询",
            "res_model": "report.inquiry.wizard",
            "views": [[False, "form"]],
            'context': {'default_report_type': self.report_type, 'default_report_id': self.id},
            "target": "new",
        }

    def _get_owner_equity_report_data(self, period_from, period_to):
        """获取所有者权益变动数据"""
        report_data = []
        data_cache = self._prepare_data_cache(period_from, period_to)

        for item in self.general_item_ids.sorted('sequence'):
            report_data.append(item.get_row_data(data_cache))

        return report_data

    # ===================
    # 私有方法
    # ===================
    def _get_assets_liability_data(self, period_from, period_to):
        """单实例方法：获取报表数据

        :param period: 会计期间
        :return grid_data: 报表数据
        """
        report_data = []
        data_cache = self._prepare_data_cache(period_from, period_to)

        # 资产列数据
        rows_assets = []
        for item in self.assets_item_ids.sorted('sequence'):
            rows_assets.append(item.get_row_data(data_cache))

        # 负债及所有者权益列数据
        rows_liability = []
        for item in self.liability_item_ids.sorted('sequence'):
            rows_liability.append(item.get_row_data(data_cache))

        # 拼接两列数据
        max_length = max([len(rows_assets), len(rows_liability)])
        min_length = min([len(rows_assets), len(rows_liability)])
        for i in range(max_length):
            if i < min_length:
                row = rows_assets[i] + rows_liability[i]
            elif i < len(rows_assets):
                row = rows_assets[i] + [None] * 3
            else:
                row = [None] * 3 + rows_liability[i]
            report_data.append(row)

        return report_data

    def _get_profit_cash_flow_data(self, period_from, period_to):
        """单实例方法：获取报表数据

        :param period: 会计期间
        :return grid_data: 报表数据
        """
        report_data = []
        data_cache = self._prepare_data_cache(period_from, period_to)

        for item in self.general_item_ids.sorted('sequence'):
            date_info = item.get_row_data(data_cache)
            report_data.append(date_info)
        return report_data

    def _prepare_data_cache(self, period_from, period_to):
        """准备缓存数据 FIXME:性能优化"""
        data_cache = dict()

        date_qm = period_to.date_end
        date_qc = period_from.date_start - timedelta(days=1)
        date_nc = period_from.fiscalyear_id.date_start - timedelta(days=1)

        if self.report_type == 'assets_liability':
            # 加载期末余额，期初余额和年初余额
            data_cache['balance_data_qm'] = self._fetch_balance_data(self.company_id.id, date_qm)
            data_cache['balance_data_qc'] = self._fetch_balance_data(self.company_id.id, date_qc)
            data_cache['balance_data_nc'] = self._fetch_balance_data(self.company_id.id, date_nc)

            # 加载重分类科目余额
            if self.cancel_out is True:
                data_cache['balance_data_qm_co'] = self._fetch_balance_data_co(self.company_id.id, date_qm)
                data_cache['balance_data_qc_co'] = self._fetch_balance_data_co(self.company_id.id, date_qc)
                data_cache['balance_data_nc_co'] = self._fetch_balance_data_co(self.company_id.id, date_nc)
                data_cache['co_accounts'] = self.get_co_accounts()

        elif self.report_type == 'profit':
            # 加载本期发生和本年发生
            data_cache['occur_data_bn'] = self._fetch_occur_data(self.company_id.id,
                                                                 period_from.fiscalyear_id.date_start,
                                                                 period_to.date_end)
            data_cache['occur_data_bq'] = self._fetch_occur_data(self.company_id.id, period_from.date_start,
                                                                 period_to.date_end)

        elif self.report_type == 'cash_flow':
            # 加载期末余额，期初余额和年初余额
            data_cache['balance_data_qm'] = self._fetch_balance_data(self.company_id.id, date_qm)
            data_cache['balance_data_qc'] = self._fetch_balance_data(self.company_id.id, date_qc)
            data_cache['balance_data_nc'] = self._fetch_balance_data(self.company_id.id, date_nc)

            # 加载现金流量项目本期和本年借贷差额
            data_cache['cash_flow_data_bn'] = self._fetch_cash_flow_data(self.company_id.id,
                                                                         period_from.fiscalyear_id.date_start,
                                                                         period_to.date_end)
            data_cache['cash_flow_data_bq'] = self._fetch_cash_flow_data(self.company_id.id, period_from.date_start,
                                                                         period_to.date_end)

        elif self.report_type == 'owner':
            # 虽然不知道有什么用，但先加着
            data_cache['balance_data_qm'] = self._fetch_balance_data(self.company_id.id, date_qm)
            data_cache['balance_data_qc'] = self._fetch_balance_data(self.company_id.id, date_qc)
            data_cache['balance_data_nc'] = self._fetch_balance_data(self.company_id.id, date_nc)

        # 初始化缓存值
        data_cache['value_cache'] = {}

        return data_cache

    def _fetch_balance_data(self, company_id, date_end):
        """科目余额数据读取"""
        self.env.cr.execute(f"""
            SELECT
                account_id,
                sum( balance ) AS balance
            FROM
                account_move_line
            WHERE
                company_id = {company_id} and date <= '{date_end}' and fr_state = 'posted'
            GROUP BY
                account_id
        """)
        balance_data = defaultdict(float)
        for res in self.env.cr.fetchall():
            balance_data[res[0]] = res[1]
        return balance_data

    def _fetch_balance_data_co(self, company_id, date_end):
        """重分类余额读取"""
        balance_data = defaultdict(float)
        for cancel_out in self.cancel_out_ids:
            # 业务伙伴借方科目余额
            deb_account = cancel_out.deb_account_id
            deb_descendent_ids = deb_account.get_descendant_ids(leaf=True)
            partner_balance_deb = self._fetch_partner_balance_data(company_id, date_end, deb_descendent_ids)

            # 业务伙伴贷方科目余额
            cre_account = cancel_out.cre_account_id
            cre_descendent_ids = cre_account.get_descendant_ids(leaf=True)
            partner_balance_cre = self._fetch_partner_balance_data(company_id, date_end, cre_descendent_ids)

            # 相同业务伙伴借贷方余额相抵
            partner_balance_deb_co = defaultdict(float)
            partner_balance_cre_co = defaultdict(float)
            for partner_id in set(list(partner_balance_deb.keys()) + list(partner_balance_cre.keys())):

                # 余额重分类，借方为负记入贷方，贷方为负记入借方，示例如下：
                # [借：1, 贷：-1] --> [借：1, 贷：-1]
                # [借：-1, 贷：-1] --> [借：0, 贷：-2]
                # [借：1, 贷：1] --> [借：2, 贷：0]
                # [借：-1, 贷：1] --> [借：1, 贷：-1]
                if partner_balance_deb[partner_id] > 0:
                    partner_balance_deb_co[partner_id] += partner_balance_deb[partner_id]
                else:
                    partner_balance_cre_co[partner_id] += partner_balance_deb[partner_id]

                if partner_balance_cre[partner_id] > 0:
                    partner_balance_deb_co[partner_id] += partner_balance_cre[partner_id]
                else:
                    partner_balance_cre_co[partner_id] += partner_balance_cre[partner_id]

                # 借贷相抵，借贷抵消后重新分配借贷余额，示例如下：
                # [借：2, 贷：-1] --> [借：1, 贷：0]
                # [借：1, 贷：-2] --> [借：0, 贷：-1]
                if cancel_out.partner_co is True:
                    balance = partner_balance_deb_co[partner_id] - partner_balance_cre_co[partner_id]
                    if balance > 0:
                        partner_balance_deb_co[partner_id] = balance
                        partner_balance_cre_co[partner_id] = 0
                    elif balance < 0:
                        partner_balance_deb_co[partner_id] = 0
                        partner_balance_cre_co[partner_id] = balance

            balance_data[deb_account.id] = sum(list(partner_balance_deb_co.values()))
            balance_data[cre_account.id] = sum(list(partner_balance_cre_co.values()))

        return balance_data

    def _fetch_partner_balance_data(self, company_id, date_end, account_ids):
        """客户余额读取"""
        if len(account_ids) > 1:
            account_ids = tuple(account_ids)
        elif len(account_ids) == 1:
            account_ids = f'({account_ids[0]})'
        else:
            raise MissingError('缺少科目范围！')

        self.env.cr.execute(f"""
            SELECT
                partner_id,
                sum( balance ) AS balance
            FROM
                account_move_line
            WHERE
                company_id = {company_id} and date <= '{date_end}' and fr_state = 'posted'
                and account_id in {account_ids}
            GROUP BY
                partner_id
        """)

        balance_partner = defaultdict(float)
        for res in self.env.cr.fetchall():
            balance_partner[res[0]] = res[1]

        return balance_partner

    def _fetch_occur_data(self, company_id, date_start, date_end):
        """借贷发生额读取"""
        self.env.cr.execute(f"""
            SELECT
                account_id,
                sum( debit ) AS debit,
                sum( credit ) AS credit
            FROM
                account_move_line
            WHERE
                company_id = {company_id} and date <= '{date_end}' and date >= '{date_start}' 
                and fr_state = 'posted'
            GROUP BY
                account_id
        """)
        occur_data = defaultdict(lambda: (0, 0))
        for res in self.env.cr.fetchall():
            occur_data[res[0]] = (res[1], res[2])

        return occur_data

    def _fetch_cash_flow_data(self, company_id, date_start, date_end):
        """现金路流量借贷方差额读取"""
        self.env.cr.execute(f"""
            SELECT
                fr_cash_flow_id,
                sum( debit ) AS debit,
                sum( credit ) AS credit
            FROM
                account_move_line
            WHERE
                company_id = {company_id} and date >= '{date_start}' and date <= '{date_end}' and fr_state = 'posted'
            GROUP BY
                fr_cash_flow_id
        """)

        cash_flow_data = defaultdict(lambda: (0, 0))
        for res in self.env.cr.fetchall():
            cash_flow_data[res[0]] = (res[1] - res[2], res[2] - res[1])

        return cash_flow_data

    @staticmethod
    def _get_row_data(item, data_cache):
        value_1 = value_2 = None
        if item.value_1:
            value_1 = item.value_1.get_value(data_cache)
        if item.value_2:
            value_2 = item.value_2.get_value(data_cache)

        return [item.name, value_1, value_2]


class AccountLedgerReportItem(models.Model):
    _name = 'account.ledger.report.item'
    _description = '报表项目'

    # 基础字段
    sequence = fields.Integer(string='序列')
    name = fields.Char(string='名称')
    qc_amount = fields.Float(string="期初金额")
    code = fields.Char(string='代码')
    col_in = fields.Selection([('general', ''),
                               ('assets', ''),
                               ('liability', '')], default='general', string='列')

    # 关系字段
    report_id = fields.Many2one('account.ledger.report', string='报表', required=True, ondelete='cascade')
    value_1 = fields.Many2one('account.ledger.value', string='取值一')
    value_2 = fields.Many2one('account.ledger.value', string='取值二')

    value_13 = fields.Many2one('account.ledger.value', string='实收资本')
    value_14 = fields.Many2one('account.ledger.value', string='优先股')
    value_3 = fields.Many2one('account.ledger.value', string='永续债')
    value_4 = fields.Many2one('account.ledger.value', string='其他')
    value_5 = fields.Many2one('account.ledger.value', string='资本公积')
    value_6 = fields.Many2one('account.ledger.value', string='减：库存股')
    value_7 = fields.Many2one('account.ledger.value', string='其他综合收益')
    value_8 = fields.Many2one('account.ledger.value', string='盈余公积')
    value_9 = fields.Many2one('account.ledger.value', string='未分配利润')
    value_10 = fields.Many2one('account.ledger.value', string='其他')
    value_11 = fields.Many2one('account.ledger.value', string='少数股东权益')
    value_12 = fields.Many2one('account.ledger.value', string='所有者权益合计')

    # 关联字段
    company_id = fields.Many2one(related='report_id.company_id', string='公司')

    # ===================
    # 公用方法
    # ===================
    def get_row_data(self, data_cache):
        self.ensure_one()
        value_1 = value_2 = None
        if self.report_id.report_type == 'owner':
            result = [None] * 12
        else:
            result = [None] * 2

        if self.value_1:
            ###################新增，现金流量期初金额 添加。在现金流量报表配置中，添加金额。添加到对应的数据中
            # 21-11-5之前代码 #result[0] = self.value_1.get_value(data_cache)
            ###改造代码#################################################################################
            # 判断，报表是否开启现金流量期初功能
            if self.report_id.cash_flow_open:
                # 开启现金流量期初功能
                result[0] = self.value_1.get_value(data_cache) + self.qc_amount
            else:
                # 未开启现金流量期初功能
                result[0] = self.value_1.get_value(data_cache)

            ###########################################################################################
        if self.value_2:
            result[1] = self.value_2.get_value(data_cache)
        if self.value_13:
            result[0] = self.value_13.get_value(data_cache)
        if self.value_14:
            result[1] = self.value_14.get_value(data_cache)
        if self.value_3:
            result[2] = self.value_3.get_value(data_cache)
        if self.value_4:
            result[3] = self.value_4.get_value(data_cache)
        if self.value_5:
            result[4] = self.value_5.get_value(data_cache)
        if self.value_6:
            result[5] = self.value_6.get_value(data_cache)
        if self.value_7:
            result[6] = self.value_7.get_value(data_cache)
        if self.value_8:
            result[7] = self.value_8.get_value(data_cache)
        if self.value_9:
            result[8] = self.value_9.get_value(data_cache)
        if self.value_10:
            result[9] = self.value_10.get_value(data_cache)
        if self.value_11:
            result[10] = self.value_11.get_value(data_cache)
        if self.value_12:
            result[11] = self.value_12.get_value(data_cache)

        result.insert(0, self.name)
        return result


class AccountCancelOut(models.Model):
    _name = 'account.cancel.out'
    _description = '重分类科目'

    report_id = fields.Many2one('account.ledger.report', string='报表', required=True, ondelete='cascade')
    sequence = fields.Integer(string='序号')
    deb_account_id = fields.Many2one('account.account', string='借方科目', required=True)
    cre_account_id = fields.Many2one('account.account', string='贷方科目', required=True)
    partner_co = fields.Boolean(string='客户同名相抵')
