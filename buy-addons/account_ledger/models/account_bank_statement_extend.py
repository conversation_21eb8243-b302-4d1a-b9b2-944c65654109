# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class AccountBankStatement(models.Model):
    _inherit = "account.bank.statement"
    _description = "Bank Statement"

    # @api.model_create_multi
    # def create(self, values):
    #     res = super(AccountBankStatement, self).create(values)
    #
    #     print(self)
    #     # print(self.move_id)
    #     return res

    def button_post(self):
        res = super(AccountBankStatement, self).button_post()
        for order in self:
            move_id = order.line_ids.mapped('move_id')
            print(move_id)
            for line in order.line_ids:
                if line.account_id.partner_bool:
                    line.write({
                        'partner_id': order.partner_id.id,
                    })
                else:
                    line.write({
                        'partner_id': None,
                    })

                # 如果科目中没有选择 标签和账户，那么将标签和账户 设置为空
                if not line.account_id.analysis_bool:
                    line.write({
                        'analytic_account_id': None,
                    })
                if not line.account_id.label_bool:
                    line.write({
                        'analytic_tag_ids': None,
                    })
        # return res


