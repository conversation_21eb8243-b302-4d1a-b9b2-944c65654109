# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class AccountMoveTemplate(models.Model):
    """凭证模板：保存记账凭证作为模板"""
    _name = 'account.move.template'
    _description = '凭证模板'

    # 基础字段
    name = fields.Char(string='模板名称', required=True)

    # 关系字段
    journal_id = fields.Many2one('account.journal', string='日记账', required=False)
    line_ids = fields.One2many('account.move.template.line', 'template_id', string='模板明细行', required=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.user.company_id,
                                 required=True)
    account_receive_id = fields.Many2one("account.account", string='应收账款', required=False)
    account_payment_id = fields.Many2one("account.account", string='应付账款', required=False)
    account_type = fields.Selection([
        ("custom", '客户'),
        ("provider", '供应商'),
        ("employee", '员工'),
    ], string='类型', default="custom")

    # ===================
    # 继承方法
    # ===================

    @api.returns('self', lambda value: value.id)
    def copy(self, default=None):

        rec_copy = super(AccountMoveTemplate, self).copy(
            {'name': self.name + '（副本）', 'company_id': self.env.user.company_id.id})

        # 同时复制明细行
        for line in self.line_ids:
            line.copy({'template_id': rec_copy.id})

        return rec_copy

    # ===================
    # 约束方法
    # ===================
    @api.constrains('name')
    def _constrains_name(self):
        for rec in self:
            if self.search_count([('name', '=', rec.name), ('company_id', '=', rec.company_id.id)]) > 1:
                raise ValidationError('凭证模板已存在！')


class AccountMoveTemplateLine(models.Model):
    """凭证模板行：记账凭证模板的模板行"""
    _name = 'account.move.template.line'
    _description = '凭证模板行'
    _order = 'direction desc'

    # 基础字段
    sequence = fields.Integer(string='序列')
    name = fields.Char(string='摘要')
    direction = fields.Selection([('debit', '借方'), ('credit', '贷方')], string='默认方向')

    # 关系字段
    template_id = fields.Many2one('account.move.template', string='凭证模板', required=True, ondelete='cascade')
    account_id = fields.Many2one('account.account', string='科目')

    # ===================
    # 约束方法
    # ===================
    @api.constrains('account_id')
    def _constrains_account_id(self):
        for rec in self:
            if rec.account_id and rec.account_id.company_id != rec.template_id.company_id:
                raise ValidationError('科目所属公司与凭证模板所属公司不一致！')

            if rec.account_id and rec.account_id.fr_as_leaf is False:
                raise ValidationError('科目不能为非末级科目！')
