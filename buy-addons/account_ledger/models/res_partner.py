"""
客户联系人
合作伙伴模型扩展
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ResPartnerExtend(models.Model):
    """
    合作伙伴扩展
    """

    _inherit = "res.partner"

    @api.model
    def _get_default_account_pay(self):
        """
        创建客户。默认会计科目-应付
        :return:
        """
        # pay_account = self.env["account.account"].search(
        #     [("user_type_id.type", "=", "payable"), ("fr_default_pay", "=", True)], limit=1
        # )
        pay_account = self.env["account.account"].search(
            [("fr_default_pay", "=", True)], limit=1
        )
        return pay_account

    @api.model
    def _get_default_account_recv(self):
        """
        创建客户。默认会计科目-应收
        :return:
        """
        # recv_account = self.env["account.account"].search(
        #     [("user_type_id.type", "=", "receivable"), ("fr_default_rec", "=", True)], limit=1
        # )
        recv_account = self.env["account.account"].search(
            [("fr_default_rec", "=", True)], limit=1
        )
        return recv_account

    # 设置客户应收/应付账款科目的默认值

    property_account_payable_id = fields.Many2one('account.account', company_dependent=True,
                                                  default=_get_default_account_pay,
                                                  string="应付账款",
                                                  domain="[('deprecated', '=', False), ('company_id', '=', current_company_id)]",
                                                  required=True)
    property_account_receivable_id = fields.Many2one('account.account', company_dependent=True,
                                                     string="应收账款",
                                                     default=_get_default_account_recv,
                                                     domain="[('deprecated', '=', False), ('company_id', '=', current_company_id)]",
                                                     required=True)

    @api.model
    def create(self, vals_list):
        """
        继承联系人创建方法，使在多公司创建联系人不选择公司时时，保证该联系人在每个公司的会计分录字段都为默认值。
        :param vals_list:
        :return:
        """
        res = super(ResPartnerExtend, self).create(vals_list)
        # 如果没有选择公司，则多公司字段会计分录需要为每个公司都创建一个。
        if not vals_list.get('company_id'):
            company = self.env['res.company'].sudo().search([])
            # 获取字段id
            payable_fields_id = self.env['ir.model.fields'].sudo().search(
                [('name', '=', 'property_account_payable_id'), ('model', '=', 'res.partner')])
            receivable_fields_id = self.env['ir.model.fields'].sudo().search(
                [('name', '=', 'property_account_receivable_id'), ('model', '=', 'res.partner')])
            for com in company:
                # 是当前公司则跳过
                if com.id == self.env.company.id:
                    continue
                # 获取该公司会计分录默认值
                # account_default_pay = self.env['account.account'].sudo().search(
                #     [("user_type_id.type", "=", "payable"), ("fr_default_pay", "=", True), ('company_id', '=', com.id)])
                # account_default_rec = self.env['account.account'].sudo().search(
                #     [("user_type_id.type", "=", "receivable"), ("fr_default_rec", "=", True),
                #      ('company_id', '=', com.id)])
                account_default_pay = self.env['account.account'].sudo().search(
                    [("fr_default_pay", "=", True), ('company_id', '=', com.id)])
                account_default_rec = self.env['account.account'].sudo().search(
                    [("fr_default_rec", "=", True), ('company_id', '=', com.id)])
                # 如果有默认值，则为该联系人在该公司下创建记录（多公司字段保存在ir.property）
                if account_default_pay:
                    self.env['ir.property'].sudo().create({
                        'name': 'property_account_payable_id',
                        'res_id': 'res.partner,' + str(res.id),
                        'company_id': com.id,
                        'fields_id': payable_fields_id.id,
                        'value_reference': 'account.account,' + str(account_default_pay.id),
                        'type': 'many2one',
                    })
                if account_default_rec:
                    self.env['ir.property'].sudo().create({
                        'name': 'property_account_receivable_id',
                        'res_id': 'res.partner,' + str(res.id),
                        'company_id': com.id,
                        'fields_id': receivable_fields_id.id,
                        'value_reference': 'account.account,' + str(account_default_rec.id),
                        'type': 'many2one',
                    })
        return res

    def write(self, values):
        """
        重写联系人修改方法，使多公司下联系人公司修改为False时，保证该联系人在每个公司的会计分录字段都为默认值。
        :param values:
        :return:
        """
        res = super(ResPartnerExtend, self).write(values)
        # if values.get('company_id') is False and (self.is_company is True and self.parent_id is False):
        if values.get('company_id') is False:
            # 如果是公司并且有上级公司则不用设置
            for partner in self:
                if partner.is_company and partner.parent_id is True:
                    return res
                company = self.env['res.company'].sudo().search([])
                # 获取字段id
                payable_fields_id = self.env['ir.model.fields'].sudo().search(
                    [('name', '=', 'property_account_payable_id'), ('model', '=', 'res.partner')])
                receivable_fields_id = self.env['ir.model.fields'].sudo().search(
                    [('name', '=', 'property_account_receivable_id'), ('model', '=', 'res.partner')])
                for com in company:
                    # 是当前公司则跳过
                    if com.id == self.env.company.id:
                        continue
                    # 获取该公司会计分录默认值
                    # account_default_pay = self.env['account.account'].sudo().search(
                    #     [("user_type_id.type", "=", "payable"), ("fr_default_pay", "=", True), ('company_id', '=', com.id)])
                    # account_default_rec = self.env['account.account'].sudo().search(
                    #     [("user_type_id.type", "=", "receivable"), ("fr_default_rec", "=", True),
                    #      ('company_id', '=', com.id)])
                    account_default_pay = self.env['account.account'].sudo().search(
                        [("fr_default_pay", "=", True), ('company_id', '=', com.id)])
                    account_default_rec = self.env['account.account'].sudo().search(
                        [("fr_default_rec", "=", True), ('company_id', '=', com.id)])
                    property_payable_id = self.env['ir.property'].sudo().search(
                        [('company_id', '=', com.id), ('name', '=', 'property_account_payable_id'),
                         ('res_id', '=', 'res.partner,' + str(partner.id))])
                    property_receivable_id = self.env['ir.property'].sudo().search(
                        [('company_id', '=', com.id), ('name', '=', 'property_account_receivable_id'),
                         ('res_id', '=', 'res.partner,' + str(partner.id))])
                    # 如果该公司已经有该条记录，则只需修改对应的值，否则需要新建
                    if property_payable_id:
                        property_payable_id.write({
                            'value_reference': 'account.account,' + str(account_default_pay.id)
                        })
                    else:
                        self.env['ir.property'].sudo().create({
                            'name': 'property_account_payable_id',
                            'res_id': 'res.partner,' + str(partner.id),
                            'company_id': com.id,
                            'fields_id': payable_fields_id.id,
                            'value_reference': 'account.account,' + str(account_default_pay.id),
                            'type': 'many2one',
                        })
                    # 如果该公司已经有该条记录，则只需修改对应的值，否则需要新建
                    if property_receivable_id:
                        property_receivable_id.write({
                            'value_reference': 'account.account,' + str(account_default_rec.id)
                        })
                    else:
                        self.env['ir.property'].sudo().create({
                            'name': 'property_account_receivable_id',
                            'res_id': 'res.partner,' + str(partner.id),
                            'company_id': com.id,
                            'fields_id': receivable_fields_id.id,
                            'value_reference': 'account.account,' + str(account_default_rec.id),
                            'type': 'many2one',
                        })
        return res
