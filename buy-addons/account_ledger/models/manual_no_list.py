from odoo import models, fields, api, exceptions


class ManualNoList(models.TransientModel):
    _name = "manual.no.list"
    name = fields.Char("号码")

    def handle_manual(self):
        data = list(self)

        if len(data) != 1:
            raise exceptions.UserError("只能选择一个号码")

        am = self.env['account.move'].search([('id', '=', self._context.get("move_id"))], limit=1)

        am.num = self.name
        # 清空号码
        # self.search([]).unlink()
