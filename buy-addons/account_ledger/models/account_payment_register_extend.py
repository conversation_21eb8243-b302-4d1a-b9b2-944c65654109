# -*- coding: utf-8 -*-
# &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
# QQ:*********
# Author：'wangshuai'
# Date：2021/7/8
# &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&

from odoo import fields, models, api, _


class AccountPaymentRegisterExtend(models.TransientModel):
    _inherit = 'account.payment.register'

    fr_cash_flow_id = fields.Many2one('account.cash.flow', string='现金流量项目')
    cash_bool = fields.Boolean(related='journal_id.default_account_id.cash_bool', string="现金流量")

    def _create_payment_vals_from_wizard(self, batch_result):
        """此处在登记付款时，添加现金流量项目，带入到创建的付款单中"""
        register = super(AccountPaymentRegisterExtend, self)._create_payment_vals_from_wizard(batch_result)
        register['cash_flow_id'] = self.fr_cash_flow_id.id
        return register

    def _create_payment_vals_from_batch(self, batch_result):
        """此处在登记付款时，添加现金流量项目，带入到创建的付款单中"""
        from_batch = super(AccountPaymentRegisterExtend, self)._create_payment_vals_from_batch(batch_result)
        from_batch['cash_flow_id'] = self.fr_cash_flow_id.id
        return from_batch

    def _create_payments(self, batch_result=None):
        """ 重写此方法，实现，在登记付款时，通过获取当前现金流量项目，添加到 凭证明细行上"""
        self.ensure_one()

        batches = self._get_batches()
        first_batch_result = batches[0]
        edit_mode = self.can_edit_wizard and (len(first_batch_result['lines']) == 1 or self.group_payment)
        to_process = []

        if edit_mode:
            payment_vals = self._create_payment_vals_from_wizard(first_batch_result)
            to_process.append({
                'create_vals': payment_vals,
                'to_reconcile': first_batch_result['lines'],
                'batch': first_batch_result,
            })
        else:
            # Don't group payments: Create one batch per move.
            if not self.group_payment:
                new_batches = []
                for batch_result in batches:
                    for line in batch_result['lines']:
                        new_batches.append({
                            **batch_result,
                            'payment_values': {
                                **batch_result['payment_values'],
                                'payment_type': 'inbound' if line.balance > 0 else 'outbound'
                            },
                            'lines': line,
                        })
                batches = new_batches

            for batch_result in batches:
                to_process.append({
                    'create_vals': self._create_payment_vals_from_batch(batch_result),
                    'to_reconcile': batch_result['lines'],
                    'batch': batch_result,
                })

        payments = self._init_payments(to_process, edit_mode=edit_mode)
        ######此处新增循环，实现凭证明细行上，根据科目。进行对应现金流量的填写####
        if payments:
            for line_pay in payments.move_id.line_ids:
                if line_pay.account_id.cash_bool:
                    line_pay.fr_cash_flow_id = payments.cash_flow_id.id
        for process in to_process:
            # action_post()是个单例操作
            self.with_context(register=True)._post_payments([process], edit_mode=edit_mode)
        self._reconcile_payments(to_process, edit_mode=edit_mode)
        return payments
