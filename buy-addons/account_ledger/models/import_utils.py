
from datetime import timedelta
import base64
import csv
from io import BytesIO, StringIO
import xlrd
from odoo import api, fields, models, http
from odoo.exceptions import ValidationError, UserError, MissingError



class AccountLedgerR(models.Model):

    _inherit = 'account.ledger.report'

    file_name = fields.Char('文件名', size=64)
    report = fields.Binary('Prepared File', filters='.xls')

    def upload_in(self):
        if not self.report:
            raise UserError('请先上传文件')
        xls = self.report
        xls = base64.b64decode(xls)
        # try:
        #     book = xlrd.open_workbook(file_contents=xls, encoding_override='utf-8')
        # except Exception as e:
        #     raise UserError(u'文件无法解析，请确认文件类型是否为‘xls’')

        file = StringIO(initial_value=xls.decode())
        sheet0 = csv.reader(file, delimiter=',', quotechar='|')
        # sheet0 = book.sheet_by_index(0)
        self.general_item_ids.unlink()
        self.assets_item_ids.unlink()
        self.liability_item_ids.unlink()
        self.cancel_out_ids.unlink()
        datas = self._parse_excel(sheet0)
        try:
            self.write(datas)
        except Exception as e:
            raise e

    def _parse_excel(self, sheet):
        # row_num = sheet.nrows
        current_type = False
        data_dict = {}
        # data = []
        start = False
        row_dict = {}   # 每个明细行的值
        # for num in range(4, row_num):
        #     row_data = sheet.row_values(num)
        for row_data in sheet:
            if sheet.line_num in [1,2,3,4,5,6]:
                continue
            if row_data[0] != '':
                current_type = self._get_current_type(row_data[0])
                start = False
            elif row_data[0] == '' and row_data[1] == '' and row_data[2] == '' and row_data[3] != '':
                start = True
                continue

            if start:
                if row_data[1] != '' and row_data[2] != '' and row_data[3] != '':

                    result = data_dict.setdefault(current_type, [])
                    result.append((0, 0, row_dict))   # 完成上一轮，开始新一轮

                    for k, v in row_dict.items():
                        if isinstance(v, dict):
                            if None in v:
                                del v[None]
                            if v['from_type'] != 'other_value':
                                try:
                                    value_item_id = self.env['account.ledger.value'].create(v)
                                except Exception as e:
                                    raise e
                                row_dict[k] = value_item_id.id

                    row_dict = {'name': row_data[1], 'code': row_data[2]}   # 每一行报表明细
                    if row_data[1] == '二、营业利润（亏损以“-”号填列）':
                        print(2222)
                    if row_data[4] == '':  # 代表是空的！
                        continue
                    self._get_item(row_data, row_dict, True)
                else:
                    self._get_item(row_data, row_dict, False)

        result = data_dict.setdefault(current_type, [])
        result.append((0, 0, row_dict))

        for field, datas in  data_dict.items():
            for record in   datas:
                for k, v in   record[2].items():
                    if k.startswith('value_') and isinstance(v, dict):
                        if v['from_type'] == 'other_value' and v['other_value_ids']:
                            for x_rec in v['other_value_ids']:
                                x_rec[2]['value_from_id'] = self.env['account.ledger.value'].search([('name', '=', x_rec[2]['b_value_from_id']),
                                                                                      ('report_id', '=', self.id),
                                                                                      ('from_type', '=', 'other_value')],limit=1).id
                                print(f'xxxxxxxxxxx',x_rec[2]['value_from_id'])
                        record[2][k] = self.env['account.ledger.value'].create(v).id

        return data_dict


    def _get_item(self, row_value, row_dict, first):
        """从值列表里获取项目参数，项目从索引3开始，每5项一个"""
        s = self._field_generator()
        if first:
            start = 3
            while start+5 <= len(row_value):
                f = next(s)
                item_dict = row_dict.setdefault(f, {}) # 每个项对应的字段，比如实收资本的参数值
                item_dict['name'] = row_value[start]
                item_dict['report_id'] = self.id
                from_type = self._str_to_value('account.ledger.value', 'from_type', row_value[start+1])
                item_dict['from_type'] = from_type
                value_field = self._get_from_type(from_type)
                item_dict[value_field] = []
                start += 5
        else:
            start = 3
            while start+5 <= len(row_value):
                f = next(s)
                item_dict = row_dict[f]     # value_1 的字典
                value_field = self._get_from_type(item_dict['from_type'])
                item_line_list = item_dict[value_field]         # 每个项明细的参数字典列表
                value_sign = self._str_to_value('account.ledger.value.line', 'value_sign', row_value[start+2] )
                if value_field == 'ledger_line_ids':
                    if value_sign:
                        item_line_list.append((0, 0, {
                            'value_sign': value_sign,
                            'account_id': self.env['account.account'].search([('code', '=', row_value[start+3])],limit=1).id,
                            'value_type': self.env['account.ledger.value.type'].search([('name', '=', row_value[start+4])],limit=1).id,
                        }))
                elif value_field == 'cash_flow_ids':
                    if value_sign:
                        item_line_list.append((0, 0, {
                            'value_sign': value_sign,
                            'cash_flow_id': self.env['account.cash.flow'].search([('name', '=', row_value[start + 3])],limit=1).id,
                            'value_type': self.env['account.ledger.value.type'].search([('name', '=', row_value[start+4])],limit=1).id,
                        }))
                elif value_field == 'other_value_ids':
                    if value_sign:
                        item_line_list.append((0, 0, {
                            'value_sign': value_sign,
                            'b_value_from_id': row_value[start + 3]
                            # 'value_from_id': self.env['account.ledger.value'].search([('name', '=', row_value[start + 3]),
                            #                                                           ('report_id', '=', self.id),
                            #                                                           ('from_type', '=', 'other_value')],limit=1).id,
                        }))
                else:
                    raise UserError('xx')
                start += 5


    def _get_from_type(self, from_type):
        if from_type == 'ledger_line':
            return 'ledger_line_ids'
        elif from_type == 'cash_flow':
            return 'cash_flow_ids'
        elif from_type == 'other_value':
            return 'other_value_ids'

    def _field_generator(self):
        if self.report_type == 'owner':
            fields = ['value_13', 'value_14', 'value_3', 'value_4', 'value_5', 'value_6', 'value_7',
                      'value_8', 'value_9', 'value_10', 'value_11', 'value_12', ]
        else:
            fields = ['value_1', 'value_2']
        for f in fields:
            yield f

    def _str_to_value(self, model, field, value):
        for k, v in self.env[model]._fields[field].selection:
            if v == value:
                return k

    def _get_current_type(self, value):
        if self.report_type == 'assets_liability':
            if value == '资产':
                return 'assets_item_ids'
            elif value == '负债和所有者权益':
                return 'liability_item_ids'
            elif value == '科目重分类':
                return 'cancel_out_ids'
        elif self.report_type in ['profit', 'cash_flow', 'owner']:
            if value == '项目':
                return 'general_item_ids'


    
    def download_it(self):
        return {
            'type': 'ir.actions.act_url',
            'url': f'/ledger_template/download/{self.id}',
            'target': 'new',
        }

    def export_to_excel(self):
        """导出到excel"""
        file = StringIO()
        writer = csv.writer(file, delimiter=',', quotechar='|', quoting=csv.QUOTE_MINIMAL)
        datas = self._read_data()
        for data in datas:
            if data and isinstance(data[0], list):
                for line in data:
                    writer.writerow(line)
            else:
                writer.writerow(data)
        return file

    
    def import_from_excel(self):
        """从excel导入"""
        xls = self.report
        xls = base64.b64decode(xls)

    def _get_item_name(self, item_line, suffix, flag):

        if flag == 1 and item_line.value_1:
            return item_line.value_1.name
        elif flag == 2 and item_line.value_2:
            return item_line.value_2.name
        elif flag == 3 and item_line.value_3:
            return item_line.value_3.name
        elif flag == 4 and item_line.value_4:
            return item_line.value_4.name
        elif flag == 5 and item_line.value_5:
            return item_line.value_5.name
        elif flag == 6 and item_line.value_6:
            return item_line.value_6.name
        elif flag == 7 and item_line.value_7:
            return item_line.value_7.name
        elif flag == 8 and item_line.value_8:
            return item_line.value_8.name
        elif flag == 9 and item_line.value_9:
            return item_line.value_9.name
        elif flag == 10 and item_line.value_10:
            return item_line.value_10.name
        elif flag == 11 and item_line.value_11:
            return item_line.value_11.name
        elif flag == 12 and item_line.value_12:
            return item_line.value_12.name
        elif flag == 13 and item_line.value_13:
            return item_line.value_13.name
        elif flag == 14 and item_line.value_14:
            return item_line.value_14.name
        return f'{item_line.code}_{suffix}'

    def _get_item_source(self, item_line, flag):
        from_type = dict(self.env['account.ledger.value']._fields['from_type'].selection)
        if flag == 1 and item_line.value_1:
            return from_type[item_line.value_1.from_type]
        elif flag == 2 and item_line.value_2:
            return from_type[item_line.value_2.from_type]
        elif flag == 3 and item_line.value_3:
            return from_type[item_line.value_3.from_type]
        elif flag == 4 and item_line.value_4:
            return from_type[item_line.value_4.from_type]
        elif flag == 5 and item_line.value_5:
            return from_type[item_line.value_5.from_type]
        elif flag == 6 and item_line.value_6:
            return from_type[item_line.value_6.from_type]
        elif flag == 7 and item_line.value_7:
            return from_type[item_line.value_7.from_type]
        elif flag == 8 and item_line.value_8:
            return from_type[item_line.value_8.from_type]
        elif flag == 9 and item_line.value_9:
            return from_type[item_line.value_9.from_type]
        elif flag == 10 and item_line.value_10:
            return from_type[item_line.value_10.from_type]
        elif flag == 11 and item_line.value_11:
            return from_type[item_line.value_11.from_type]
        elif flag == 12 and item_line.value_12:
            return from_type[item_line.value_12.from_type]
        elif flag == 13 and item_line.value_13:
            return from_type[item_line.value_13.from_type]
        elif flag == 14 and item_line.value_14:
            return from_type[item_line.value_14.from_type]
        return ''

    def _read_data(self):
        """获取数据列表"""
        report_type = dict(self._fields['report_type'].selection)
        header = [
            ['表名称', self.name],
            ['报表类型', report_type[self.report_type], '公司', self.company_id.name],
            ['启用重分类', str(self.cancel_out)],
            ['说明', self.note],
        ]

        body = []

        if self.report_type == 'cash_flow':
            body.append(['项目'])
            body.append(['', '名称', '代码', '本年累计', '', '', '', '', '本期金额'])
            body.append(
                ['', '', '', '本年累计-名称', '本年累计-取值来源', '本年累计-取值明细', '', '', '本期金额-名称', '本期金额-取值来源', '本期金额-取值明细', '', ''])
            for item in self.general_item_ids:
                bnlj_name = self._get_item_name(item, 'bnlj', 1)
                bqje_name = self._get_item_name(item, 'bqje', 2)
                bnlj_source = self._get_item_source(item, 1)
                bqje_source = self._get_item_source(item, 2)
                body.append(
                    ['', item.name, item.code, bnlj_name, bnlj_source, '', '', '', bqje_name, bqje_source, '', '', ''])
                # for line in item.value_1.
                vv = self._read_item(item.value_1, item.value_2)
                body.extend(vv)

        elif self.report_type == 'assets_liability':
            body.append(['资产'])
            body.append(['', '名称', '代码', '期末余额', '', '', '', '', '年初余额'])
            body.append(
                ['', '', '', '期末余额-名称', '期末余额-取值来源', '期末余额-取值明细', '', '', '年初余额-名称', '年初余额-取值来源', '年初余额-取值明细', '', ''])
            for item in self.assets_item_ids:
                qmye_name = self._get_item_name(item, 'qmye', 1)
                ncye_name = self._get_item_name(item, 'ncye', 2)
                qmye_source = self._get_item_source(item, 1)
                ncye_source = self._get_item_source(item, 2)
                body.append(
                    ['', item.name, item.code, qmye_name, qmye_source, '', '', '', ncye_name, ncye_source, '', '', ''])
                vv = self._read_item(item.value_1, item.value_2)
                body.extend(vv)

            body.append(['负债和所有者权益'])
            body.append(['', '名称', '代码', '期末余额', '', '', '', '', '年初余额'])
            body.append(
                ['', '', '', '期末余额-名称', '期末余额-取值来源', '期末余额-取值明细', '', '', '年初余额-名称', '年初余额-取值来源', '年初余额-取值明细', '', ''])
            for item in self.liability_item_ids:
                qmye_name = self._get_item_name(item, 'qmye', 1)
                ncye_name = self._get_item_name(item, 'ncye', 2)
                qmye_source = self._get_item_source(item, 1)
                ncye_source = self._get_item_source(item, 2)
                body.append(
                    ['', item.name, item.code, qmye_name, qmye_source, '', '', '', ncye_name, ncye_source, '', '', ''])
                vv = self._read_item(item.value_1, item.value_2)
                body.extend(vv)

            body.append(['科目重分类'])
            body.append(['', '借方科目', '贷方科目', '客户同名相抵'])
            for item in self.cancel_out_ids:
                body.append(['', item.deb_account_id.code, item.cre_account_id.code, item.partner_co])

        elif self.report_type == 'profit':
            body.append(['项目'])
            body.append(['', '名称', '代码', '本年累计', '', '', '', '', '本期金额'])
            body.append(
                ['', '', '', '本年累计-名称', '本年累计-取值来源', '本年累计-取值明细', '', '', '本期金额-名称', '本期金额-取值来源', '本期金额-取值明细', '', ''])
            for item in self.general_item_ids:
                bnlj_name = self._get_item_name(item, 'bnlj', 1)
                bqje_name = self._get_item_name(item, 'bqje', 2)
                bnlj_source = self._get_item_source(item, 1)
                bqje_source = self._get_item_source(item, 2)
                body.append(
                    ['', item.name, item.code, bnlj_name, bnlj_source, '', '', '', bqje_name, bqje_source, '', '', ''])
                vv = self._read_item(item.value_1, item.value_2)
                body.extend(vv)

        elif self.report_type == 'owner':
            body.append(['项目'])
            body.append(['', '名称', '代码', '实收资本',
                         '', '', '', '', '优先股',
                         '', '', '', '', '永续债',
                         '', '', '', '', '其他',
                         '', '', '', '', '资本公积',
                         '', '', '', '', '减：库存股',
                         '', '', '', '', '其他综合收益',
                         '', '', '', '', '盈余公积',
                         '', '', '', '', '未分配利润',
                         '', '', '', '', '其他',
                         '', '', '', '', '少数股东权益',
                         '', '', '', '', '所有者权益合计', ])
            body.append(
                ['', '', '', '实收资本-名称', '实收资本-取值来源', '实收资本-取值明细', '', '',
                 '优先股-名称', '优先股-取值来源', '优先股-取值明细', '', '',
                 '永续债-名称', '永续债-取值来源', '永续债-取值明细', '', '',
                 '其他-名称', '其他-取值来源', '其他-取值明细', '', '',
                 '资本公积-名称', '资本公积-取值来源', '资本公积-取值明细', '', '',
                 '减：库存股-名称', '减：库存股-取值来源', '减：库存股-取值明细', '', '',
                 '其他综合收益-名称', '其他综合收益-取值来源', '其他综合收益-取值明细', '', '',
                 '盈余公积-名称', '盈余公积-取值来源', '盈余公积-取值明细', '', '',
                 '未分配利润-名称', '未分配利润-取值来源', '未分配利润-取值明细', '', '',
                 '其他-名称', '其他-取值来源', '其他-取值明细', '', '',
                 '少数股东权益-名称', '少数股东权益-取值来源', '少数股东权益-取值明细', '', '',
                 '所有者权益合计-名称', '所有者权益合计-取值来源', '所有者权益合计-取值明细', '', '', ])
            for item in self.general_item_ids:
                sszb_name = self._get_item_name(item, 'sszb', 13)
                yxg_name = self._get_item_name(item, 'yxg', 14)
                yxz_name = self._get_item_name(item, 'yxz', 3)
                qt1_name = self._get_item_name(item, 'qt1', 4)
                zbgj_name = self._get_item_name(item, 'zbgj', 5)
                kcg_name = self._get_item_name(item, 'kcg', 6)
                qtzhsy_name = self._get_item_name(item, 'qtzhsy', 7)
                yygj_name = self._get_item_name(item, 'yygj', 8)
                wfplr_name = self._get_item_name(item, 'wfplr', 9)
                qt2_name = self._get_item_name(item, 'qt2', 10)
                ssgdly_name = self._get_item_name(item, 'ssgdly', 11)
                syzqyhj_name = self._get_item_name(item, 'syzqyhj', 12)

                sszb_source = self._get_item_source(item, 13)
                yxg_source = self._get_item_source(item, 14)
                yxz_source = self._get_item_source(item, 3)
                qt1_source = self._get_item_source(item, 4)
                zbgj_source = self._get_item_source(item, 5)
                kcg_source = self._get_item_source(item, 6)
                qtzhsy_source = self._get_item_source(item, 7)
                yygj_source = self._get_item_source(item, 8)
                wfplr_source = self._get_item_source(item, 9)
                qt2_source = self._get_item_source(item, 10)
                ssgdly_source = self._get_item_source(item, 11)
                syzqyhj_source = self._get_item_source(item, 12)

                body.append(['', item.name, item.code,
                             sszb_name, sszb_source, '', '', '',
                             yxg_name, yxg_source, '', '', '',
                             yxz_name, yxz_source, '', '', '',
                             qt1_name, qt1_source, '', '', '',
                             zbgj_name, zbgj_source, '', '', '',
                             kcg_name, kcg_source, '', '', '',
                             qtzhsy_name, qtzhsy_source, '', '', '',
                             yygj_name, yygj_source, '', '', '',
                             wfplr_name, wfplr_source, '', '', '',
                             qt2_name, qt2_source, '', '', '',
                             ssgdly_name, ssgdly_source, '', '', '',
                             syzqyhj_name, syzqyhj_source, '', '', '',
                             ])
                vv = self._read_item(item.value_13, item.value_14, item.value_3, item.value_4, item.value_5,
                                     item.value_6, item.value_7
                                     , item.value_8, item.value_9, item.value_10, item.value_11, item.value_12)
                body.extend(vv)

        header.extend(body)
        return header

    def _read_item(self, *items):
        prefix = data = [''] * 5
        sign = dict(self.env['account.ledger.value.line']._fields['value_sign'].selection)
        # value_type = dict(self.env['account.ledger.value.line']._fields['value_type'].selection)
        if not items:
            return prefix
        max_length = max([max([len(x.ledger_line_ids) for x in items]),
                          max([len(x.cash_flow_ids) for x in items]),
                          max([len(x.other_value_ids) for x in items])])
        data = [[''] * 3 for i in range(max_length)]
        for index, item in enumerate(items):
            if item.from_type == 'ledger_line':
                line_ids = item.ledger_line_ids
            elif item.from_type == 'cash_flow':
                line_ids = item.cash_flow_ids
            else:
                line_ids = item.other_value_ids

            for num, line in enumerate(line_ids):
                expect_row_length = 5 * index + 5
                length_diff = expect_row_length - len(data[num])
                if length_diff > 0:
                    data[num].extend([''] * length_diff)

                if item.from_type == 'ledger_line':
                    data[num].extend([sign[line.value_sign], line.account_id.code, line.value_type.name])
                elif item.from_type == 'cash_flow':
                    data[num].extend([sign[line.value_sign], line.cash_flow_id.name, line.value_type.name])
                else:
                    data[num].extend([sign[line.value_sign], line.value_from_id.name])
        return data