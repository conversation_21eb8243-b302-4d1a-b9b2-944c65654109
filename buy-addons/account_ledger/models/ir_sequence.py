# -*- coding: utf-8 -*-
import datetime

from odoo import models, fields, api
from odoo.exceptions import ValidationError, MissingError, UserError


def get_date_end_month(date_start):
    """根据开始日期计算一整月时间段的结束日期

    :param date_start: 日期对象
    :return: 结束日期，日期对象
    """
    if date_start.month == 12:
        year_end = date_start.year + 1
        month_end = 1
    else:
        year_end = date_start.year
        month_end = date_start.month + 1

    if date_start.day == 29:
        day_end = date_start.day - 1
    else:
        day_end = date_start.day

    date_end = datetime.date(year_end, month_end, day_end) - datetime.timedelta(days=1)
    return date_end


def get_monday(today):
    """
    根据传入的日期返回本周周一日期
    """
    today = datetime.datetime.strptime(str(today), "%Y-%m-%d")
    return (today - datetime.timedelta(today.weekday())).date()


def get_sunday(today):
    """
    根据传入的日期返回本周周日日期
    """
    today = datetime.datetime.strptime(str(today), "%Y-%m-%d")
    return (today + datetime.timedelta(6 - today.weekday())).date()


class IrSequence(models.Model):
    _name = 'ir.sequence'
    _inherit = 'ir.sequence'

    date_range_type = fields.Selection([('year', '年'), ('month', '月'), ('week', '周')], string="子序列周期", default='year')

    def _next(self, sequence_date=None):
        """修改原生序列只能自动以年为周期创建子序列"""
        if self.use_date_range:
            dt = fields.Date.today()
            if self._context.get('ir_sequence_date'):
                dt = self._context.get('ir_sequence_date')
            seq_date = self.env['ir.sequence.date_range'].search(
                [('sequence_id', '=', self.id), ('date_from', '<=', dt), ('date_to', '>=', dt)], limit=1)

            # 新建按月划分的日期范围子序列
            if not seq_date and self.date_range_type == 'month':
                if isinstance(dt, str):
                    dt = datetime.datetime.strptime(dt, '%Y-%m-%d')
                date_from = datetime.date(dt.year, dt.month, 1)
                date_to = get_date_end_month(date_from)
                # 日期后存在子序列修正截至日期
                date_range = self.env['ir.sequence.date_range'].search(
                    [('sequence_id', '=', self.id), ('date_from', '>=', dt), ('date_from', '<=', date_to)],
                    order='date_from desc', limit=1)
                if date_range:
                    date_to = date_range.date_from + datetime.timedelta(days=-1)

                # 日期前存在子序列修正起始日期
                date_range = self.env['ir.sequence.date_range'].search(
                    [('sequence_id', '=', self.id), ('date_to', '>=', date_from), ('date_to', '<=', dt)],
                    order='date_to desc', limit=1)
                if date_range:
                    date_from = date_range.date_to + datetime.timedelta(days=1)

                # 创建子序列
                self.env['ir.sequence.date_range'].sudo().create({
                    'date_from': date_from,
                    'date_to': date_to,
                    'sequence_id': self.id,
                })
            # 新建按周划分的日期范围子序列
            if not seq_date and self.date_range_type == 'week':
                if isinstance(dt, str):
                    dt = datetime.datetime.strptime(dt, '%Y-%m-%d')
                date_from = get_monday(datetime.datetime.today().date())
                date_to = get_sunday(datetime.datetime.today().date())
                # 日期后存在子序列修正截至日期
                date_range = self.env['ir.sequence.date_range'].search(
                    [('sequence_id', '=', self.id), ('date_from', '>=', dt), ('date_from', '<=', date_to)],
                    order='date_from desc', limit=1)
                if date_range:
                    date_to = date_range.date_from + datetime.timedelta(days=-1)

                # 日期前存在子序列修正起始日期
                date_range = self.env['ir.sequence.date_range'].search(
                    [('sequence_id', '=', self.id), ('date_to', '>=', date_from), ('date_to', '<=', dt)],
                    order='date_to desc', limit=1)
                if date_range:
                    date_from = date_range.date_to + datetime.timedelta(days=1)

                # 创建子序列
                self.env['ir.sequence.date_range'].sudo().create({
                    'date_from': date_from,
                    'date_to': date_to,
                    'sequence_id': self.id,
                })

        return super(IrSequence, self)._next()
