# -*- coding: utf-8 -*-
# &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
# QQ:*********
# Author：'wangshuai'
# Date：2020/11/25 0025
# &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
from odoo import api, fields, models

ACCOUNTING_ELEMENTS = [
    ('asset', '资产'),
    ('liability', '负债'),
    ('equity', '所有者权益'),
    ('income', '收入'),
    ('expense', '费用'),
    ('profit', '利润'),
]


class AccountAccountTypeExitension(models.Model):
    """科目类型：原生科目类型扩展"""
    _inherit = "account.account.type"
    _description = "科目类型"

    internal_group = fields.Selection(ACCOUNTING_ELEMENTS, string="会计要素")