# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.osv import expression
from odoo.exceptions import ValidationError


class AccountAnalyticTag(models.Model):
    _name = 'account.analytic.tag'
    _description = 'Analytic Tags'
    name = fields.Char(string='Analytic Tag', index=True, required=True)
    color = fields.Integer('Color Index')
    active = fields.Boolean(default=True, help="Set active to false to hide the Analytic Tag without removing it.")
    active_analytic_distribution = fields.<PERSON><PERSON>an('Analytic Distribution')
    analytic_distribution_ids = fields.One2many('account.analytic.distribution', 'tag_id', string="Analytic Accounts")
    company_id = fields.Many2one('res.company', string='Company')
    account_move_line_id = fields.Many2many("account.move.line")


class AccountAnalyticDistribution(models.Model):
    _name = 'account.analytic.distribution'
    _description = 'Analytic Account Distribution'
    _rec_name = 'account_id'

    account_id = fields.Many2one('account.analytic.account', string='Analytic Account', required=True)
    percentage = fields.Float(string='Percentage', required=True, default=100.0)
    name = fields.Char(string='Name', related='account_id.name', readonly=False)
    tag_id = fields.Many2one('account.analytic.tag', string="Parent tag", required=True)

    _sql_constraints = [
        ('check_percentage', 'CHECK(percentage >= 0 AND percentage <= 100)',
         'The percentage of an analytic distribution should be between 0 and 100.')
    ]


class AccountAnalyticTagInherit(models.Model):
    """分析标签：原生分析标签扩展"""
    _inherit = 'account.analytic.tag'
    _description = '分析标签'

    name = fields.Char(string='Analytic Tag', index=True, required=True)
    color = fields.Integer('Color Index')
    active = fields.Boolean(default=True, help="Set active to false to hide the Analytic Tag without removing it.")
    active_analytic_distribution = fields.Boolean('Analytic Distribution')
    analytic_distribution_ids = fields.One2many('account.analytic.distribution', 'tag_id', string="Analytic Accounts")
    category_id = fields.Many2one('account.analytic.category', string='分析类别')
    company_id = fields.Many2one('res.company', string='Company')
    coding = fields.Char(string='编码')

    # ===================
    # 公用方法
    # ===================
    @api.model
    def get_tags(self, ids):
        """模型方法：获取分析标签"""
        tags = self.search_read(fields=['id', 'name', 'category_id'], domain=[('id', 'in', ids)])
        return tags


class AccountAnalyticCategory(models.Model):
    _name = 'account.analytic.category'
    _description = '分析类别'

    name = fields.Char(string="名称", index=True, required=True)
    code = fields.Char(string='代码', index=True, required=True)
    company_id = fields.Many2one('res.company', string='公司')


class AccountAnalyticLedger(models.Model):
    _inherit = 'account.analytic.account'
    _description = '分析账户'

    coding = fields.Char(string='编码')
