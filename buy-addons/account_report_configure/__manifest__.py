# -*- coding: utf-8 -*-
{
    'name': "报表/科目初始化",

    'summary': """
        报表/科目初始化""",

    'description': """
       报表/科目初始化
    """,

    'author': "openerphk Team",
    'website': "http://cdn.openerp.hk",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/16.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': '财务管理/财务管理',
    'version': '********',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account_ledger','financial_settings'],

    # always loaded
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'wizard/account_report_init.xml'
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
    'sequence': 1,
    'license': 'LGPL-3',
}
