# -*- coding: utf-8 -*-

# from odoo import models, fields, api


# class account_report_data_extend(models.Model):
#     _name = 'account_report_data_extend.account_report_data_extend'
#     _description = 'account_report_data_extend.account_report_data_extend'

#     name = fields.Char()
#     value = fields.Integer()
#     value2 = fields.Float(compute="_value_pc", store=True)
#     description = fields.Text()
#
#     @api.depends('value')
#     def _value_pc(self):
#         for record in self:
#             record.value2 = float(record.value) / 100
