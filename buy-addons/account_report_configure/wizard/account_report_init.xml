<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="account_report_init_form_view" model="ir.ui.view">
            <field name="name">初始化报表</field>
            <field name="model">account.report.init</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button
                                confirm="确认要初始化科目吗？此操作会造成数据丢失！" name="confirm_init_account" class="btn btn-primary"
                                type="object">初始化科目
                        </button>
                        <button
                                confirm="确认要初始化报表配置吗？此操作会造成数据丢失！" name="confirm_init" class="btn btn-primary"
                                type="object">初始化报表
                        </button>
                    </header>
                </form>
            </field>
        </record>


        <record id="act_server_account_report_init" model="ir.actions.server">
            <field name="name">初始化报表</field>
            <field name="model_id" ref="model_account_report_init"/>
            <field name="binding_model_id" ref="model_account_report_init"/>
            <field name="state">code</field>
            <field name="code">model.confirm_init()</field>
        </record>

        <record id="account_report_init_act_window" model="ir.actions.act_window">
            <field name="name">初始化报表</field>
            <field name="res_model">account.report.init</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <!--        <menuitem id="account_report_init_menu" parent="account_ledger.BaseSettingMainMenu" name="报表初始化" sequence="26"-->
        <!--                  action="act_server_account_report_init"/>-->
<!--         <menuitem id="account_report_init_act_menu" parent="account_ledger.BaseSettingMainMenu" name="科目/报表初始化" -->
<!--                   groups="account_report_configure.account_report_configure_rule" -->
<!--                   sequence="27" -->
<!--                   action="account_report_init_act_window"/> -->
        <menuitem id="account_report_init_act_menu" parent="financial_settings.settings_menu" name="科目/报表初始化"
                  groups="account_report_configure.account_report_configure_rule"
                  action="account_report_init_act_window"/>
    </data>

</odoo>