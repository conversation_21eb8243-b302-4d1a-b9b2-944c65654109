import json
import os.path
import logging
from odoo import models

_log = logging.getLogger(__name__)


class AccountReportInit(models.TransientModel):
    _name = "account.report.init"

    def handle_value_other_value(self, value: dict):
        value_object = self.env['account.ledger.value'].create({
            'name': value.get('name'),
            'from_type': value.get('from_type'),
            'report_type': value.get('report_type')
        })
        other_value_ids = value.get("other_value_ids")
        if other_value_ids:
            for line in other_value_ids:
                self.env['account.ledger.value.line'].create({
                    "value_id": value_object.id,
                    "value_sign": line.get("value_sign"),
                    "value_from_id": self.env['account.ledger.value'].search(
                        [['name', '=', line.get("value_from_name")]],
                        limit=1).id
                })
        return value_object

    def handle_value_ledger_line(self, value: dict):
        result = self.env['account.ledger.value'].create({
            "name": value.get("name"),
            "from_type": 'ledger_line',
            "report_type": value.get("'report_type'"),
        })
        for line in value.get("ledger_line_ids"):
            self.env['account.ledger.value.line'].create({
                "value_id": result.id,
                "value_sign": line.get("value_sign"),
                "account_id": self.env['account.account'].search([['code', '=', line.get('account_code')]], limit=1).id,
                "value_type": self.env['account.ledger.value.type'].search([['name', '=', line.get('value_type')]],
                                                                           limit=1).id
            })
        return result

    def handle_value_cash_flow(self, value: dict):
        result = self.env['account.ledger.value'].create({
            "name": value.get("name"),
            "from_type": 'cash_flow',  # 修改1222
            "report_type": value.get('report_type'),
        })
        for line in value.get("ledger_line_ids"):
            self.env['account.ledger.value.line'].create({
                "value_id": result.id,
                "value_sign": line.get("value_sign"),
                "account_id": self.env['account.account'].search([['code', '=', line.get('account_code')]], limit=1).id,
                "cash_flow_id": self.env['account.cash.flow'].search([['name', '=', line.get('cash_flow_name')]],
                                                                     limit=1).id,
                "value_type": self.env['account.ledger.value.type'].search([['name', '=', line.get('value_type')]],
                                                                           limit=1).id
            })
        return result

    def handle_value(self, value: dict, report, item, item_field):
        if not value.get("name"):
            return
        if value.get("from_type") == 'ledger_line':
            v = self.handle_value_ledger_line(value)
            v.report_id = report.id
            if hasattr(item, item_field):
                setattr(item, item_field, v.id)
        elif value.get("from_type") == 'cash_flow':
            v = self.handle_value_cash_flow(value)
            v.report_id = report.id
            if hasattr(item, item_field):
                setattr(item, item_field, v.id)
        elif value.get("from_type") == 'other_value':
            v = self.handle_value_other_value(value)
            v.report_id = report.id
            if hasattr(item, item_field):
                setattr(item, item_field, v.id)

    def handle_profit(self, data: dict):
        """
        利润表处理
        """
        account_report = self.env['account.ledger.report'].create({
            "name": data.get("name"),
            "report_type": data.get("report_type"),
            "cancel_out": data.get("cancel_out"),
        })

        for item in data.get("general_item_ids"):
            report_item = self.env['account.ledger.report.item'].create({
                'name': item.get("name"),
                'code': item.get("code"),
                'report_id': account_report.id,
            })

            value_1_data = item.get("value_1")
            value_2_data = item.get("value_2")
            if value_1_data:
                self.handle_value(value_1_data, account_report, report_item, 'value_1')
            if value_2_data:
                self.handle_value(value_2_data, account_report, report_item, 'value_2')

    def handle_cash_flow(self, data: dict):
        """
        现金流量表
        """
        account_report = self.env['account.ledger.report'].create({
            "name": data.get("name"),
            "cancel_out": data.get("cancel_out"),
            "report_type": data.get("report_type")
        })

        for item in data.get("general_item_ids"):
            report_item = self.env['account.ledger.report.item'].create({
                "code": item.get("code"),
                "name": item.get("name"),
                'report_id': account_report.id
            })

            value_1_data = item.get("value_1")
            value_2_data = item.get("value_2")
            if value_1_data:
                self.handle_value(value_1_data, account_report, report_item, 'value_1')
            if value_2_data:
                self.handle_value(value_2_data, account_report, report_item, 'value_2')

    def handle_assets_liability(self, data: dict):
        """
        资产负债表
        """
        account_report = self.env['account.ledger.report'].create({
            "name": data.get("name"),
            "cancel_out": data.get("cancel_out"),
            "report_type": data.get("report_type"),
        })

        for item in data.get("assets_item_ids"):
            report_item = self.env['account.ledger.report.item'].create({
                "code": item.get("code"),
                "name": item.get("name"),
                "col_in": "assets",
                'report_id': account_report.id,
            })
            value_1_data = item.get("value_1")
            value_2_data = item.get("value_2")
            if value_1_data:
                self.handle_value(value_1_data, account_report, report_item, 'value_1')
            if value_2_data:
                self.handle_value(value_2_data, account_report, report_item, 'value_2')

        for item in data.get("liability_item_ids"):
            report_item = self.env['account.ledger.report.item'].create({
                "code": item.get("code"),
                "name": item.get("name"),
                "col_in": "liability",
                'report_id': account_report.id,
            })
            value_1_data = item.get("value_1")
            value_2_data = item.get("value_2")
            if value_1_data:
                self.handle_value(value_1_data, account_report, report_item, 'value_1')
            if value_2_data:
                self.handle_value(value_2_data, account_report, report_item, 'value_2')

    def handle_owner(self, data: dict):
        """
        所有者权益变动表
        """
        account_report = self.env['account.ledger.report'].create({
            "name": data.get("name"),
            "cancel_out": data.get("cancel_out"),
            "report_type": data.get("report_type")
        })

        for item in data.get("general_item_ids"):
            report_item = self.env['account.ledger.report.item'].create({
                "code": item.get("code"),
                "name": item.get("name"),
                'report_id': account_report.id
            })

            if item.get("value_3"):
                self.handle_value(item.get("value_3"), account_report, report_item, 'value_3')
            if item.get("value_4"):
                self.handle_value(item.get("value_4"), account_report, report_item, 'value_4')
            if item.get("value_5"):
                self.handle_value(item.get("value_5"), account_report, report_item, 'value_5')
            if item.get("value_6"):
                self.handle_value(item.get("value_6"), account_report, report_item, 'value_6')
            if item.get("value_7"):
                self.handle_value(item.get("value_7"), account_report, report_item, 'value_7')
            if item.get("value_8"):
                self.handle_value(item.get("value_8"), account_report, report_item, 'value_8')
            if item.get("value_9"):
                self.handle_value(item.get("value_9"), account_report, report_item, 'value_9')
            if item.get("value_10"):
                self.handle_value(item.get("value_10"), account_report, report_item, 'value_10')
            if item.get("value_11"):
                self.handle_value(item.get("value_11"), account_report, report_item, 'value_11')
            if item.get("value_12"):
                self.handle_value(item.get("value_12"), account_report, report_item, 'value_12')
            if item.get("value_13"):
                self.handle_value(item.get("value_13"), account_report, report_item, 'value_13')
            if item.get("value_14"):
                self.handle_value(item.get("value_14"), account_report, report_item, 'value_14')

    def read_data(self, data: list):
        for item in data:
            report_type = item.get("report_type")
            if report_type == 'profit':
                self.handle_profit(item)
            elif report_type == 'cash_flow':
                self.handle_cash_flow(item)
            elif report_type == 'assets_liability':
                self.handle_assets_liability(item)
            elif report_type == 'owner':
                self.handle_owner(item)
            else:
                raise Exception("类型错误")

    def confirm_init_account(self):
        cr = self.env.cr
        company_id = self.env.company.id
        cr.execute("DELETE FROM account_move_line WHERE company_id = %s", (company_id,))
        cr.execute("DELETE FROM account_move WHERE company_id = %s", (company_id,))
        cr.execute(
            "DELETE FROM ir_property WHERE name = 'property_account_payable_id' or name = 'property_account_receivable_id'"
        )
        cr.execute(
            "UPDATE account_journal SET default_account_id = NULL, suspense_account_id = NULL, profit_account_id = NULL WHERE company_id = %s",
            (company_id,))

        cr.execute(
            "DELETE FROM account_transfer_model_line"
        )

        cr.execute("DELETE FROM account_account WHERE company_id = %s", (company_id,))

        with open(os.path.join(os.path.dirname(__file__), "account_account.json"), 'r', encoding='utf-8') as f:
            data = json.loads(f.read())

            for account in data:
                account_type = account.get("type")
                account_type_obj = self.env['account.account.type'].search([['name', '=', account_type]], limit=1)

                if not account_type_obj:
                    account_type_obj = self.env['account.account.type'].create({
                        "name": account_type,
                        "internal_group": "equity"
                    })

                code = account.get("code")
                name = account.get("name")
                reconcile = account.get("reconcile")
                fr_parent_account_id = None
                code_list = code.split('.')
                if len(code_list) == 2:
                    parent = self.env['account.account'].search([
                        ['code', '=', code_list[0]],
                        ['company_id', '=', self.env.company.id],
                    ])
                    parent.fr_as_leaf = False
                    fr_parent_account_id = parent.id
                result = self.env['account.account'].create({
                    "code": code,
                    "name": name,
                    "reconcile": reconcile,
                    "user_type_id": account_type_obj.id,
                    "fr_parent_account_id": fr_parent_account_id,
                    "company_id": self.env.company.id,
                })

                _log.info('account.account create: %s,%s' % (result.code, result.name))

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'account.account',
            'views': [[False, 'tree'], [False, 'form']]
        }

    def confirm_init(self):

        self.env['account.ledger.report'].search(
            [
                ['name', 'in', ['利润表', '现金流量表', '资产负债表', '所有者权益变动表']],
                ['company_id', '=', self.env.company.id]
            ]).unlink()
        # self.env['account.ledger.report.item'].search([]).unlink()
        # self.env['account.ledger.value'].search([]).unlink()
        # self.env['account.ledger.value.line'].search([]).unlink()

        with open(os.path.join(os.path.dirname(__file__), "报表导出.json"), 'r', encoding='utf-8') as f:
            data = json.loads(f.read())
            self.read_data(data)

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'account.ledger.report',
            'views': [[False, 'tree'], [False, 'form']]
        }
