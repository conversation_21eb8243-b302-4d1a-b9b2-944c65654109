# -*- coding: utf-8 -*-
# from odoo import http


# class AccountReportDataExtend(http.Controller):
#     @http.route('/account_report_data_extend/account_report_data_extend', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/account_report_data_extend/account_report_data_extend/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('account_report_data_extend.listing', {
#             'root': '/account_report_data_extend/account_report_data_extend',
#             'objects': http.request.env['account_report_data_extend.account_report_data_extend'].search([]),
#         })

#     @http.route('/account_report_data_extend/account_report_data_extend/objects/<model("account_report_data_extend.account_report_data_extend"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('account_report_data_extend.object', {
#             'object': obj
#         })
