# -*- coding: utf-8 -*-

from . import controllers
from . import models
from . import wizard

import json

from odoo.sql_db import Cursor
import os


def handle_value_other_value(value: dict, cr: Cursor):
    cr.execute("INSERT INTO account_ledger_value(name, from_type, report_type) VALUES (%s,%s,%s) RETURNING id",
               (value.get('name'), value.get('from_type'), value.get('report_type')))
    value_id = cr.dictfetchone()['id']

    other_value_ids = value.get("other_value_ids")
    if other_value_ids:
        for line in other_value_ids:
            cr.execute("SELECT id FROM account_ledger_value WHERE name = %s", (line.get("value_from_name"),))
            data = cr.dictfetchone()
            value_from_id = data[0] if data else None

            cr.execute("INSERT INTO account_ledger_value_line VALUES (%s, %s, %s)",
                       (value_id, line.get("value_sign"), value_from_id))

    return value_id


def insert_report(report: dict, cr: Cursor):
    cr.execute("INSERT INTO account_ledger_report(name, report_type, cancel_out) VALUES(%s, %s, %s) RETURNING id",
               (report.get("name"), report.get("report_type"), report.get("cancel_out")))
    return cr.dictfetchone()['id']


def find_value_type_id(value_type, cr: Cursor):
    cr.execute("SELECT id FROM account_ledger_value_type WHERE name = %S", (value_type,))
    data = cr.dictfetchone()
    if data and "id" in data.keys():
        return data['id']


def find_account(account_code, cr: Cursor):
    cr.execute("SELECT id FROM account_account WHERE code = %s limit 1", (account_code,))
    data = cr.dictfetchone()
    if data and 'id' in data.keys():
        return data['id']


def insert_value_line_ledger_line(line: dict, cr: Cursor, value_id: int):
    account_id = find_account(line.get("account_code"), cr)
    value_type_id = find_value_type_id(line.get("value_type"), cr)
    cr.execute(
        "INSERT INTO account_ledger_value_line(value_id, value_sign, value_type, account_id) VALUES  (%s,%s,%s,%s)",
        (value_id, line.get("value_sign"), value_type_id, account_id))


def insert_value(value: dict, cr: Cursor):
    cr.execute(
        "INSERT INTO account_ledger_value(name, from_type, report_type, report_id) VALUES (%s,%s,%s,%s) RETURNING id",
        (value.get("name"), value.get("from_type"), value.get('report_type'), value.get("report_id")))
    return cr.dictfetchone()['id']


def insert_report_item_profit(item: dict, cr: Cursor, report_id):
    cr.execute("INSERT INTO account_ledger_report_item(name, code, report_id) VALUES (%s, %s,%s) RETURNING id",
               (item.get("name"), item.get("code"), report_id))

    item_id = cr.dictfetchone()['id']

    for v in item.get("value_1").items():
        v['report_id'] = report_id
        value_id = insert_value(v, cr)
        cr.execute("UPDATE account_ledger_report_item SET value_1 = %s", (value_id,))
        for line in v.get("ledger_line_ids"):
            insert_value_line_ledger_line(line, cr, value_id)

    for v in item.get("value_2"):
        v['report_id'] = report_id
        value_id = insert_value(v, cr)
        cr.execute("UPDATE account_ledger_report_item SET value_2 = %s", (value_id,))
        for line in v.get("ledger_line_ids"):
            insert_value_line_ledger_line(line, cr, value_id)
    return item_id


def handle_data(data: list, cr: Cursor):
    for report in data:
        report_id = insert_report(report, cr)

        if report.get("report_type") == "profit":
            for item in report.get("general_item_ids"):
                insert_report_item_profit(item, cr, report_id)
        elif report.get("report_type") == 'cash_flow':
            pass
        elif report.get("report_type") == 'assets_liability':
            pass
        elif report.get("report_type") == 'owner':
            pass


def read_account_json():
    file = open(os.path.join(os.path.dirname(__file__), "account_account.json"), 'r', encoding="utf-8")
    data = file.read()
    file.close()
    return json.loads(data)


def read_json():
    file = open(os.path.join(os.path.dirname(__file__), "报表导出.json"), 'r', encoding='utf-8')
    data = file.read()
    file.close()
    return json.loads(data)


def remove_origin_data(cr: Cursor):
    cr.execute("DELETE FROM account_account")
    cr.execute("DELETE FROM account_move_line")
    cr.execute("DELETE FROM account_move")
    cr.execute("DELETE FROM account_move_line")
    cr.execute(
        "UPDATE account_chart_template SET property_account_receivable_id = NULL, property_account_payable_id = NULL")

    cr.execute(
        "UPDATE account_journal SET default_account_id = NULL, suspense_account_id = NULL, profit_account_id = NULL")


def find_account_type_id(name: str, cr: Cursor):
    cr.execute("SELECT id FROM account_account_type where name->>'zh_CN' = %s", (name,))
    data = cr.dictfetchone()
    if data:
        return data['id']

    cr.execute("INSERT INTO account_account_type(name,internal_group ) VALUES (%s,'equity') RETURNING id", (json.dumps({
        "en_US": name,
        "zh_CN": name
    }, ensure_ascii=False),))

    return cr.dictfetchone()['id']


def insert_account(account_list: list, cr: Cursor):
    cr.execute("select id from res_company limit 1")

    company_id = cr.dictfetchone()['id']

    for account in account_list:
        type_id = find_account_type_id(account.get("type"), cr)
        cr.execute(
            "INSERT INTO account_account(code,name,user_type_id, reconcile, company_id, account_type, create_asset) VALUES (%s,%s,%s,%s,%s,%s,%s)",
            (account.get("code"), account.get("name"), type_id, account.get("reconcile"), company_id, 'asset_current',
             'no'))


def post_init_hook(cr: Cursor, registry):
    print("init code")
    # remove_origin_data(cr)
    # insert_account(read_account_json(), cr)
    #
    # data = read_json()
    # handle_data(data, cr)
