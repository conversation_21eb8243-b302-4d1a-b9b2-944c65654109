<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="account_report_data_extend.list">
      <field name="name">account_report_data_extend list</field>
      <field name="model">account_report_data_extend.account_report_data_extend</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="account_report_data_extend.action_window">
      <field name="name">account_report_data_extend window</field>
      <field name="res_model">account_report_data_extend.account_report_data_extend</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="account_report_data_extend.action_server">
      <field name="name">account_report_data_extend server</field>
      <field name="model_id" ref="model_account_report_data_extend_account_report_data_extend"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="account_report_data_extend" id="account_report_data_extend.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="account_report_data_extend.menu_1" parent="account_report_data_extend.menu_root"/>
    <menuitem name="Menu 2" id="account_report_data_extend.menu_2" parent="account_report_data_extend.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="account_report_data_extend.menu_1_list" parent="account_report_data_extend.menu_1"
              action="account_report_data_extend.action_window"/>
    <menuitem name="Server to list" id="account_report_data_extend" parent="account_report_data_extend.menu_2"
              action="account_report_data_extend.action_server"/>
-->
  </data>
</odoo>