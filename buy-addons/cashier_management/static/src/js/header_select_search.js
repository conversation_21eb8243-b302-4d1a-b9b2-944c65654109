odoo.define('header_select_search', function (require) {
    "use strict";
    var ListController = require("web.ListController")
    var core = require('web.core');
    var session = require('web.session');
    var rpc = require('web.rpc');

    var _t = core._t;
    var currentCompanyId = session.user_context.company_id
    ListController.include({
        renderButtons: async function ($node) {
            this._super.apply(this, arguments);
            const date = new Date()
            const year = date.getFullYear()
            const month = date.getMonth() + 1
            let allowed_company_ids = session.user_context.allowed_company_ids;
            const currentDate = year + "-" + month.toString().padStart(2, "0") + "-01"
            if (this.modelName === 'account.payment' || this.modelName === 'account.move' || this.modelName === 'stock.psi' || this.modelName === 'stock.send.receiving') {
                if (this.model.loadParams.context.hasOwnProperty("required_search") && this.model.loadParams.context.required_search) {
                    const data = await loadFiscalyear(allowed_company_ids)
                    let rootElement = '<div style="display: flex; float: right; margin-left: 50px;"><span style="width: 100px; padding: 5px 0;">会计期间</span><select id="select_ct">'
                    rootElement += "<option value='0'>全部</option>"
                    const views = this.actionViews.map(item => {
                        return [item.viewID, item.type]
                    })
                    // 根据会计期间生成选项元素
                    for (let item of data) {
                        for (let per of item.period) {
                            // 设置默当前会计期间
                            if (!this.model.loadParams.context.noFirstLoad) {
                                if (per.date_start === currentDate) {
                                    const optionElement = `<option selected value="${per.id}" date-start="${per.date_start}" date-end="${per.date_end}">${per.name}</option>`
                                    rootElement += optionElement
                                } else {
                                    const optionElement = `<option value="${per.id}" date-start="${per.date_start}" date-end="${per.date_end}">${per.name}</option>`
                                    rootElement += optionElement
                                }
                            } else {
                                if (per.id === this.model.loadParams.context.current) {
                                    const optionElement = `<option selected value="${per.id}" date-start="${per.date_start}" date-end="${per.date_end}">${per.name}</option>`
                                    rootElement += optionElement
                                } else {
                                    const optionElement = `<option value="${per.id}" date-start="${per.date_start}" date-end="${per.date_end}">${per.name}</option>`
                                    rootElement += optionElement
                                }
                            }
                        }
                    }
                    rootElement += '</select></div>'


                    this.$buttons.append(rootElement);
                    this.$buttons.on("change", "#select_ct", async (e) => {
                        const selectedOption = e.target.options[e.target.selectedIndex]
                        const {context, domain} = loadContextAndDomain(this, selectedOption)
                        console.log("context", context)
                        console.log("domain", domain)
                        this.do_action({
                            type: 'ir.actions.act_window',
                            name: _t(this._title),
                            res_model: this.modelName,
                            target: 'main',
                            context,
                            views, domain
                        })
                    })
                }
            }

            function loadContextAndDomain(self, selectedOption) {
                let domain = []

                let context = {
                    ...self.model.loadParams.context,
                    noFirstLoad: true
                }
                // 取消默认当前会计期间
                context.search_default_month = 0
                context.search_default_current_period = 0

                if (selectedOption) {
                    context.current = parseInt(selectedOption.value)
                    // 全部期间
                    if (context.current === 0) {
                        context.current = null
                    } else {
                        domain.push(['date', '>=', selectedOption.getAttribute("date-start")])
                        domain.push(['date', '<=', selectedOption.getAttribute("date-end")])
                    }
                } else {
                    domain.push(['date', '>=', currentDate])
                    domain.push(['date', '<=', year + "-" + (month + 1).toString().padStart(2, "0") + "-01"])
                }


                // 银行内部转账
                if (self.model.loadParams.context.default_is_internal_transfer) {
                    domain.push(['is_internal_transfer', '=', true])
                } else {
                    if (self.modelName === 'account.payment') {
                        domain.push(['is_internal_transfer', '=', false])
                        if (self.model.loadParams.context.hasOwnProperty("default_partner_type")) {
                            let partner_type = self.model.loadParams.context.default_partner_type
                            if (partner_type === 'customer') {
                                domain.push(['partner_id.customer_rank', '=', 1])
                            } else if (partner_type === 'supplier') {
                                domain.push(['partner_id.supplier_rank', '=', 1])
                            }
                        }

                        if (self.model.loadParams.context.hasOwnProperty("default_payment_type")) {
                            domain.push(['payment_type', '=', self.model.loadParams.context.default_payment_type])
                        }
                    } else if (self.modelName === 'account.move') {
                        if (self.model.loadParams.context.hasOwnProperty("default_move_type")) {
                            domain.push(['move_type', '=', self.model.loadParams.context.default_move_type])
                        }
                    }
                }

                const originDomain = getOriginDomain(self.model.loadParams.domain)
                domain = [...domain, ...originDomain]
                // 过滤掉默认当前会计期间
                if (self.modelName === 'account.move') {
                    domain = domain.filter(item => {
                        return item[0] !== "fr_period_state"
                    })
                }

                return {
                    domain,
                    context
                }
            }

            function removeFirstDuplicate(arr) {
                const seen = new Set();
                const result = [];
                for (const item of arr) {
                    const key = JSON.stringify(item); // 将数组元素转换为字符串以进行比较
                    if (!seen.has(key)) {
                        seen.add(key);
                        result.push(item);
                    }
                }
                return result;
            }

            function getOriginDomain(domain) {
                let data = []
                for (let item of domain) {
                    if (item !== "&" && item !== "|") {
                        if (item[0] !== 'date') {
                            data.push(item)
                        }
                    }
                }
                return removeFirstDuplicate(data)
            }

            async function loadFiscalyear(allowed_company_ids) {
                const data = await rpc.query({
                    model: "fr.account.fiscalyear",
                    method: 'search_read',
                    kwargs: {
                        domain: [['company_id', 'in', allowed_company_ids]]
                    }
                })
                const result = []
                for (let year of data) {
                    const account_date = await rpc.query({
                        model: "fr.account.period",
                        method: 'search_read',
                        kwargs: {
                            domain: [['id', 'in', year.period_ids]]
                        }
                    })

                    result.push({
                        id: year.id,
                        name: year.name,
                        period: account_date
                    })

                }
                return result
            }
        },
    });
})