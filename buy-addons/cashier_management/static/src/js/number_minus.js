odoo.define("cashier_management.ReverseNumberWidget", function (require) {
    "use strict";

    var core = require('web.core');
    var fieldRegistry = require('web.field_registry');
    var AbstractField = require('web.AbstractField');

    var ReverseNumberWidget = AbstractField.extend({
        _render: function () {
            // 数据取相反数
            var reversedValue = -parseFloat(this.value);
            this.$el.text(reversedValue.toFixed(2)); // 设置显示的小数位数
        },
    });

    fieldRegistry.add('reverse_number', ReverseNumberWidget);

    return ReverseNumberWidget;

})