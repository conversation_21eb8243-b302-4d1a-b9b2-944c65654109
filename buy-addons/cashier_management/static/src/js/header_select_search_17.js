/** @odoo-module **/

import {ListController} from "@web/views/list/list_controller";
import {patch} from "@web/core/utils/patch";
import {ActionMenus} from "@web/search/action_menus/action_menus";
import {Layout} from "@web/search/layout";
import {MultiRecordViewButton} from "@web/views/view_button/multi_record_view_button";
import {ViewButton} from "@web/views/view_button/view_button";
import {SearchBar} from "@web/search/search_bar/search_bar";
import {CogMenu} from "@web/search/cog_menu/cog_menu";
import {onWillStart} from "@odoo/owl";

const model_list = [
    'account.payment',
    'account.move',
    'stock.psi',
    'stock.psi.move',
    'stock.send.receiving'
]

function getLastDayOfMonth(date) {
    // 如果未提供日期，则默认为当前日期
    date = date || new Date();

    // 将日期设置为下个月的第0天，这样它就会自动回退到上个月的最后一天
    date.setMonth(date.getMonth() + 1, 0);

    // 格式化日期（可选），这里只返回日期部分
    let day = date.getDate();
    // 如果需要返回完整的日期字符串，可以使用下面的方式
    // let lastDayStr = date.toISOString().split('T')[0]; // 返回如 "2023-04-30" 的字符串

    // 返回当月的最大日期（即最后一天）
    return day;
}

function loadContextAndDomain(originContext, origindomian, modelName, selectedOption) {
    let domain = []

    let context = {
        ...originContext,
        noFirstLoad: true,
        hasDate: true
    }
    // 取消默认当前会计期间
    context.search_default_month = 0
    context.search_default_current_period = 0

    if (selectedOption) {
        context.current = parseInt(selectedOption.value)
        // 全部期间
        if (context.current === 0) {
            context.current = null
        } else {
            domain.push(['date', '>=', selectedOption.getAttribute("date-start")])
            domain.push(['date', '<=', selectedOption.getAttribute("date-end")])
        }
    } else {
        const date = new Date()
        let year = date.getFullYear()
        let next_year = date.getFullYear()
        const month = date.getMonth() + 1
        let next_moth = date.getMonth() + 2
        if (next_moth > 12) {
            next_moth = 1
            next_year = next_year + 1
        }
        const currentDate = year + "-" + month.toString().padStart(2, "0") + "-01"
        domain.push(['date', '>=', currentDate])
        let today = new Date();
        let last_day = getLastDayOfMonth(today)
        domain.push(['date', '<', next_year + "-" + month.toString().padStart(2, "0") + '-' + last_day.toString().padStart(2, "0")])
    }


    // 银行内部转账
    if (originContext.default_is_internal_transfer) {
        domain.push(['is_internal_transfer', '=', true])
    } else {
        if (modelName === 'account.payment') {
            if (originContext.default_partner_type !== 'other') {
                domain.push(['is_internal_transfer', '=', false])
            }
            if (originContext.hasOwnProperty("default_partner_type")) {
                let partner_type = originContext.default_partner_type
                if (partner_type === 'customer') {
                    domain.push(['partner_id.customer_rank', '>', 0])
                } else if (partner_type === 'supplier') {
                    domain.push(['partner_id.supplier_rank', '>', 0])
                }
            }

            if (originContext.hasOwnProperty("default_payment_type")) {
                domain.push(['payment_type', '=', originContext.default_payment_type])
            }
        } else if (modelName === 'account.move') {
            if (originContext.hasOwnProperty("default_move_type")) {
                domain.push(['move_type', '=', originContext.default_move_type])
            }
        }
    }

    const originDomain = getOriginDomain(origindomian)
    domain = [...domain, ...originDomain]
    // 过滤掉默认当前会计期间
    if (modelName === 'account.move') {
        domain = domain.filter(item => {
            return item[0] !== "fr_period_state"
        })
    }

    return {
        domain,
        context
    }
}

function getOriginDomain(domain) {
    let data = []
    for (let item of domain) {
        if (item !== "&" && item !== "|") {
            if (item[0] !== 'date') {
                data.push(item)
            }
        }
    }
    return removeFirstDuplicate(data)
}

function removeFirstDuplicate(arr) {
    const seen = new Set();
    const result = [];
    for (const item of arr) {
        const key = JSON.stringify(item); // 将数组元素转换为字符串以进行比较
        if (!seen.has(key)) {
            seen.add(key);
            result.push(item);
        }
    }
    return result;
}

patch(ListController.prototype, {
    setup() {
        super.setup()
        this.props.selected_section = false
        var required_search = this.props.context.hasOwnProperty("required_search") && this.props.context.required_search
        if (model_list.indexOf(this.props.resModel) >= 0 && required_search) {
            this.props.selected_section = true
            // this.noFirstLoad = false

            // const currentDate = year + "-" + month.toString().padStart(2, "0") + "-01"

        }

        onWillStart(async () => {
            if (model_list.indexOf(this.props.resModel) >= 0 && required_search) {
                this.props.selected_section = true
                let allowed_company_ids = this.props.context.allowed_company_ids;
                this.financial_data = await this.loadFiscalyear(allowed_company_ids)
                const date = new Date()
                const year = date.getFullYear()
                const month = date.getMonth() + 1
                this.currentDate = year + "-" + month.toString().padStart(2, "0") + "-01"
                const {
                    context,
                    domain
                } = loadContextAndDomain(this.props.context, this.props.domain, this.props.resModel, false)
                this.env.searchModel._domain = domain
                this.env.searchModel._context = context
                this.env.searchModel.search()
            }

        });

    },
    async loadFiscalyear(allowed_company_ids) {
        const data = await this.rpc('/web/dataset/call_kw', {
            model: "fr.account.fiscalyear",
            method: 'get_financial_year',
            args: [[]],
            kwargs: {
                domain: [['company_id', 'in', allowed_company_ids]]
            }
        });
        return await data
    },
    async checkForChanges(e) {
        const selectedOption = e.target.options[e.target.selectedIndex]
        const {
            context,
            domain
        } = loadContextAndDomain(this.props.context, this.props.domain, this.props.resModel, selectedOption)
        // console.log("context", context)
        // console.log("domain", domain)
        this.env.searchModel._domain = domain
        this.env.searchModel._context = context
        this.env.searchModel.search()
        this.currentDate = selectedOption.getAttribute("date-start")

    },
    get_selected_section() {
        let selected_section = false
        var required_search = this.props.context.hasOwnProperty("required_search") && this.props.context.required_search
        if (model_list.indexOf(this.props.resModel) >= 0 && required_search) {
            selected_section = true
        }
        return selected_section
    }
})
// patch(ListController, setup())
//
//
// export class AccountingPeriod extends Component {
//     setup() {
//
//     }
// }