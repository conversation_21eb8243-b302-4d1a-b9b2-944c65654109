<?xml version="1.0" encoding="UTF-8"?>
<template>

    <t t-name="web.accounting_period">
        <div style="display: flex; float: right; margin-left: 50px;">
            <span style="width: 100px; padding: 5px 0;">会计期间:</span>
            <select id="select_ct" t-on-change="checkForChanges">
                <option value='0'>全部</option>

                <t t-foreach="financial_data" t-as="item" t-key="item.id">
                    <t t-foreach="item.period" t-as="per" t-key="per.id">
                        <option t-att-value="per.id" t-att-date-start="per.date_start"
                                t-att-selected="per.date_start === currentDate"
                                t-att-date-end="per.date_end">
                            <t t-esc="per.name"/>
                        </option>
                    </t>
                </t>
            </select>
        </div>
    </t>
    <t t-inherit="web.ListView" t-inherit-mode="extension">
        <xpath expr="//t[@t-set-slot='control-panel-additional-actions']" position="inside">
            <t t-if="get_selected_section()">
                <t t-call="web.accounting_period"/>
            </t>
        </xpath>

    </t>


</template>