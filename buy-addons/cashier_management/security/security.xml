<?xml version="1.0" encoding="UTF-8" ?>


<odoo>
    <data>
        <record id="account_payment_bill_rule" model="ir.rule">
            <field name="name">支付账单多公司权限</field>
            <field name="model_id" ref="model_account_payment_bill"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id', '=', False), ('company_id', 'in', company_ids)]
            </field>
        </record>
        <record id="account_payment_company_rule" model="ir.rule">
            <field name="name">日记账多公司权限</field>
            <field name="model_id" ref="model_account_payment"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id', '=', False), ('company_id', 'in', company_ids)]
            </field>
        </record>
    </data>
</odoo>