from odoo import fields, models, api


class AccountBankStatement(models.Model):
    _inherit = "account.bank.statement"
    d_amount = fields.Float("期开余额", compute="_computed_d_amount")

    @api.depends("balance_start", "line_ids")
    def _computed_d_amount(self):
        for record in self:
            result = record.balance_start

            for line in record.line_ids:
                result += line.amount

            record.d_amount = result
