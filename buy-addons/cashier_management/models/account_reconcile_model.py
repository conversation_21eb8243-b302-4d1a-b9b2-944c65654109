from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta


class AccountReconcileModel(models.Model):
    _inherit = "account.reconcile.model"

    reconcile_type = fields.Selection([
        ('inbound', '付款核销'),
        ('outbound', '收款核销')
    ], required=True, default='inbound')

    interval_type = fields.Selection([('minutes', '分钟'),
                                      ('hours', '小时'),
                                      ('days', '天'),
                                      ('weeks', '周'),
                                      ('months', '月')], string='周期单位', default='days', required=True)

    numbercall = fields.Integer(string='周期', default=1, required=True)

    is_use = fields.Boolean(string='是否生效')
    cron_id = fields.Many2one('ir.cron', string='定时任务')

    @api.constrains('is_use')
    def _check_unique_active_data(self):
        for one in self:
            active_records = self.search([('is_use', '=', True), ('reconcile_type', '=', one.reconcile_type),
                                          ('company_id', '=', one.company_id.id)])
            if len(active_records) > 1:
                raise ValidationError('一个公司核销规则只能生效一个')
            else:
                if active_records.cron_id:
                    if one.interval_type == 'minutes':
                        nextcall = datetime.now() + timedelta(minutes=self.numbercall)
                    elif one.interval_type == 'hours':
                        nextcall = datetime.now() + timedelta(hours=self.numbercall)
                    elif one.interval_type == 'days':
                        nextcall = datetime.now() + timedelta(days=self.numbercall)
                    elif one.interval_type == 'weeks':
                        nextcall = datetime.now() + timedelta(days=self.numbercall * 7)
                    else:
                        nextcall = datetime.now() + timedelta(days=self.numbercall * 30)
                    active_records.cron_id.write({
                        "interval_type": self.interval_type,
                        "interval_number": self.numbercall,
                        "nextcall": nextcall - timedelta(hours=8)
                    })

    def process_handle_method(self):
        if self.reconcile_type == 'outbound':
            self.process_handle_method_outbound(company=self.company_id.id)
        else:
            self.process_handle_method_inbound(company=self.company_id.id)

    def process_handle_method_outbound(self, company=None):
        if company:
            reconcile = self.sudo().search(
                [('reconcile_type', '=', 'outbound'), ('is_use', '=', True), ('company_id', '=', company)])
            if reconcile:
                domain = [('move_type', 'in', ["out_invoice", "out_refund", "out_receipt"]),
                          ('journal_id.type', '=', 'sale'), ('state', '=', 'posted'),
                          ('payment_state', 'in', ('not_paid', 'in_payment', 'partial')), ('company_id', '=', company)]
                if reconcile.rule_type == 'invoice_matching':
                    today = fields.Date.today()  # 获取当前日期
                    past_months_limit = reconcile.past_months_limit
                    if past_months_limit > 0:
                        past_date = datetime.strftime(today - relativedelta(months=int(past_months_limit)), "%Y-%m-%d")
                        domain.append(['invoice_date_due', '>=', past_date])
                    if reconcile.matching_order == 'old_fist':
                        records = self.env['account.move'].sudo().search(domain,
                                                                         order='invoice_date_due ASC,create_date ASC')
                    else:
                        records = self.env['account.move'].sudo().search(domain,
                                                                         order='invoice_date_due desc,create_date desc')

                else:
                    records = self.env['account.move'].sudo().search(domain,
                                                                     order='invoice_date_due ASC,create_date ASC')
                for move in records:
                    if move.state != 'posted' \
                            or move.payment_state not in ('not_paid', 'partial') \
                            or not move.is_invoice(include_receipts=True):
                        continue
                    payment_move = self.env['account.payment'].sudo().search(
                        [["partner_type", "=", "customer"], ["payment_type", "=", "inbound"], ['state', '=', 'posted'],
                         ['partner_id', '=', move.partner_id.id],
                         ["is_internal_transfer", "=", False]]).move_id
                    pay_term_lines = payment_move.line_ids \
                        .filtered(lambda line: line.account_id.account_type in ('asset_receivable', 'liability_payable'))
                    domain = [
                        ('id', 'in', pay_term_lines.ids),
                        '|', ('amount_residual', '!=', 0.0), ('amount_residual_currency', '!=', 0.0),
                    ]

                    if move.is_inbound():
                        domain.append(('balance', '<', 0.0))
                    else:
                        domain.append(('balance', '>', 0.0))

                    for line in self.env['account.move.line'].search(domain, order='date ASC,create_date ASC'):
                        if move.payment_state != 'paid':
                            move.js_assign_outstanding_line(line.id)

    def process_handle_method_inbound(self, company=None):
        if company:
            reconcile = self.sudo().search(
                [('reconcile_type', '=', 'inbound'), ('is_use', '=', True), ('company_id', '=', company)])
            if reconcile:
                domain = [('move_type', 'in', ["in_invoice", "in_refund", "in_receipt"]),
                          ('journal_id.type', '=', 'purchase'), ('state', '=', 'posted'),
                          ('payment_state', 'in', ('not_paid', 'in_payment', 'partial')), ('company_id', '=', company)]
                if reconcile.rule_type == 'invoice_matching':
                    today = fields.Date.today()  # 获取当前日期
                    past_months_limit = reconcile.past_months_limit
                    if past_months_limit > 0:
                        past_date = datetime.strftime(today - relativedelta(months=int(past_months_limit)), "%Y-%m-%d")
                        domain.append(['invoice_date_due', '>=', past_date])
                    if reconcile.matching_order == 'old_fist':
                        records = self.env['account.move'].sudo().search(domain,
                                                                         order='invoice_date_due ASC,create_date ASC')
                    else:
                        records = self.env['account.move'].sudo().search(domain,
                                                                         order='invoice_date_due desc,create_date desc')

                else:
                    records = self.env['account.move'].sudo().search(domain,
                                                                     order='invoice_date_due ASC,create_date ASC')
                for move in records:
                    if move.state != 'posted' \
                            or move.payment_state not in ('not_paid', 'partial') \
                            or not move.is_invoice(include_receipts=True):
                        continue
                    payment_move = self.env['account.payment'].sudo().search(
                        [["partner_type", "=", "supplier"], ["payment_type", "=", "outbound"], ['state', '=', 'posted'],
                         ['partner_id', '=', move.partner_id.id],
                         ["is_internal_transfer", "=", False]]).move_id
                    pay_term_lines = payment_move.line_ids \
                        .filtered(lambda line:  line.account_id.account_type in ('asset_receivable', 'liability_payable'))
                    domain = [
                        ('id', 'in', pay_term_lines.ids),
                        '|', ('amount_residual', '!=', 0.0), ('amount_residual_currency', '!=', 0.0),
                    ]

                    if move.is_inbound():
                        domain.append(('balance', '<', 0.0))
                    else:
                        domain.append(('balance', '>', 0.0))

                    for line in self.env['account.move.line'].search(domain, order='date ASC,create_date ASC'):
                        if move.payment_state != 'paid':
                            move.js_assign_outstanding_line(line.id)

    @api.model_create_multi
    def create(self, vals_list):
        result = super(AccountReconcileModel, self).create(vals_list)
        Cron = self.env['ir.cron']
        for record in result:
            if record.reconcile_type == 'outbound':
                res = Cron.env['ir.cron'].sudo().search([('name', '=', f'每日收款核销{str(record.company_id.id)}')])
                if res:
                    record.cron_id = res.id
                    if record.is_use:
                        res.write({
                            "name": f'每日收款核销{str(record.company_id.id)}',
                            "model_id": self.env['ir.model'].search([('model', '=', "account.reconcile.model")],
                                                                    limit=1).id,
                            "code": f"model.process_handle_method_outbound(company={record.company_id.id})",
                            "interval_number": record.numbercall,
                            "interval_type": record.interval_type,
                            "numbercall": -1,
                            "doall": False,
                        })
                else:
                    res = Cron.create({
                        "name": f'每日收款核销{str(record.company_id.id)}',
                        "model_id": self.env['ir.model'].search([('model', '=', "account.reconcile.model")],
                                                                limit=1).id,
                        "code": f"model.process_handle_method_outbound(company={record.company_id.id})",
                        "interval_number": record.numbercall,
                        "interval_type": record.interval_type,
                        "numbercall": -1,
                        "doall": False,
                    })
                    record.cron_id = res.id
            else:
                res = Cron.env['ir.cron'].sudo().search([('name', '=', f'每日付款核销{str(record.company_id.id)}')])
                if res:
                    record.cron_id = res.id
                    if record.is_use:
                        res.write({
                            "name": f"每日付款核销{str(record.company_id.id)}",
                            "model_id": self.env['ir.model'].search([('model', '=', "account.reconcile.model")],
                                                                    limit=1).id,
                            "code": f"model.process_handle_method_inbound(company={record.company_id.id})",
                            "interval_number": record.numbercall,
                            "interval_type": record.interval_type,
                            "numbercall": -1,
                            "doall": False,
                        })
                else:
                    res = Cron.create({
                        "name": f"每日付款核销{str(record.company_id.id)}",
                        "model_id": self.env['ir.model'].search([('model', '=', "account.reconcile.model")],
                                                                limit=1).id,
                        "code": f"model.process_handle_method_inbound(company={record.company_id.id})",
                        "interval_number": record.numbercall,
                        "interval_type": record.interval_type,
                        "numbercall": -1,
                        "doall": False,
                    })
                    record.cron_id = res.id
        return result

    def write(self, vals):
        res = super(AccountReconcileModel, self).write(vals)
        data = {}
        if vals.get('interval_type'):
            data.update({
                "interval_type": self.interval_type
            })
        if vals.get('numbercall'):
            data.update({
                "interval_number": self.numbercall
            })
        if data and self.is_use and self.cron_id:
            if self.interval_type == 'minutes':
                nextcall = datetime.now() + timedelta(minutes=self.numbercall)
            elif self.interval_type == 'hours':
                nextcall = datetime.now() + timedelta(hours=self.numbercall)
            elif self.interval_type == 'days':
                nextcall = datetime.now() + timedelta(days=self.numbercall)
            elif self.interval_type == 'weeks':
                nextcall = datetime.now() + timedelta(days=self.numbercall * 7)
            else:
                nextcall = datetime.now() + timedelta(days=self.numbercall * 30)
            data.update({
                "nextcall": nextcall - timedelta(hours=8)
            })
            self.cron_id.write(data)
        return res
