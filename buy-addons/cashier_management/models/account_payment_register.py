from odoo import models


class AccountPaymentRegister(models.TransientModel):
    _inherit = "account.payment.register"

    def _create_payments(self):
        self.ensure_one()
        result = super(AccountPaymentRegister, self)._create_payments()

        if self.line_ids.move_id.partner_id != result.partner_id:
            result.partner_id = self.line_ids.move_id.partner_id

        if self.line_ids.move_id.partner_id.partner_type_id:
            result.partner_type_id = self.line_ids.move_id.partner_id.partner_type_id

        if self.line_ids.move_id.sale_order_ids:
            result.sale_order_ids = [(6, 0, self.line_ids.move_id.sale_order_ids.ids)]

        if self.line_ids.move_id.purchase_order_ids:
            result.purchase_order_ids = [(6, 0, self.line_ids.move_id.purchase_order_ids.ids)]

        return result
