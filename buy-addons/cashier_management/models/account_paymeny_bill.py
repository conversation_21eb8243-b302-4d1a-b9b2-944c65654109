import logging
from datetime import datetime, timedelta

from odoo import fields, models, api

_log = logging.getLogger(__name__)


def get_previous_month_range(date_str):
    date = datetime.strptime(date_str, '%Y-%m-%d')
    first_day_of_previous_month = date.replace(day=1) - timedelta(days=1)
    first_day_of_previous_month = first_day_of_previous_month.replace(day=1)
    last_day_of_previous_month = date.replace(day=1) - timedelta(days=1)
    return (first_day_of_previous_month.strftime('%Y-%m-%d'), last_day_of_previous_month.strftime('%Y-%m-%d'))


def get_month_range(date_str):
    date = datetime.strptime(date_str, "%Y-%m-%d")
    first_day = date.replace(day=1).strftime("%Y-%m-%d")
    if date.month == 12:
        next_month = date.replace(year=date.year + 1, month=1, day=1)
    else:
        next_month = date.replace(month=date.month + 1, day=1)
    last_day = (next_month - timedelta(days=1)).strftime("%Y-%m-%d")
    return (first_day, last_day)


class AccountPaymentExtend(models.Model):
    _inherit = "account.payment"

    bill_id = fields.Many2one("account.payment.bill", string='账单')

    service_type = fields.Char(string='业务类型', compute="_compute_service_type", store=False)

    completed_date = fields.Datetime("完成时间")

    per_start_amount = fields.Float("期初余额")
    per_end_amount = fields.Float("期末余额")
    forward_amount = fields.Float('发生额', compute="_compute_forward_amount", store=True)

    @api.depends("amount", "payment_type")
    def _compute_forward_amount(self):
        for record in self:
            if record.payment_type == 'outbound':
                record.forward_amount = -record.amount
            else:
                record.forward_amount = record.amount

    @api.depends("payment_type", 'payment_type')
    def _compute_service_type(self):
        for record in self:
            if record.payment_type == 'inbound':
                if record.partner_type == 'customer':
                    record.service_type = '客户收款'
                elif record.partner_type == 'supplier':
                    record.service_type = '供应商收款'
                else:
                    record.service_type = '其他收款'
            else:
                if record.partner_type == 'customer':
                    record.service_type = '客户付款'
                elif record.partner_type == 'supplier':
                    record.service_type = '供应商付款'
                else:
                    record.service_type = '其他付款'

    def action_post(self):
        result = super(AccountPaymentExtend, self).action_post()
        for rec in self:
            if rec.journal_id.type in ['cash', 'bank']:
                current_date = datetime.now().strftime("%Y-%m-%d")
                start, end = get_month_range(current_date)
                FAR = rec.env['fr.account.period']
                period_id = FAR.search([('date_start', '=', start), ('date_end', '=', end)], limit=1)

                bill = rec.env['account.payment.bill'].search([
                    ('period_id', '=', period_id.id),
                    ('journal_id', '=', rec.journal_id.id),
                    ('company_id', '=', rec.company_id.id)
                ], limit=1)
                # 没有则新建，新建的期初余额为上一期的期末余额
                if not bill:
                    # 判断是否有上一期
                    last_start, last_end = get_previous_month_range(current_date)
                    balance_start = 0
                    last_period_id = FAR.search([('date_start', '=', last_start), ('date_end', '=', last_end)], limit=1)
                    if last_period_id:
                        last_bill = rec.env['account.payment.bill'].search([
                            ('period_id', '=', last_period_id.id),
                            ('journal_id', '=', rec.journal_id.id),
                            ('company_id', '=', rec.company_id.id)
                        ])
                        if last_bill:
                            # 期初余额修改为上一期的期末余额
                            balance_start = last_bill.balance_end

                    bill = rec.env['account.payment.bill'].create({
                        "period_id": period_id.id,
                        "journal_id": rec.journal_id.id,
                        "company_id": rec.company_id.id,
                        "balance_start": balance_start,
                        "balance_end": balance_start
                    })

                # 设置支付单期初余额为账单的期末余额
                rec.per_start_amount = bill.balance_end

                # 设置支付单期末余额
                rec.per_end_amount = rec.per_start_amount + rec.forward_amount

                # 设置账单的期末余额
                bill.balance_end = rec.per_end_amount

                rec.bill_id = bill.id
                rec.completed_date = datetime.now()

        return result


class AccountPaymentBill(models.Model):
    _name = "account.payment.bill"

    _sql_constraints = [
        ('name_key', 'UNIQUE (company_id,period_id,journal_id)', '数据重复')
    ]

    period_id = fields.Many2one("fr.account.period", string='会计期间')
    journal_id = fields.Many2one("account.journal", string='日记账')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)
    balance_start = fields.Float(string="期初余额")
    balance_end = fields.Float(string="期末余额")
    payment_lines = fields.One2many("account.payment", 'bill_id', string='交易信息')

    def name_get(self):
        result = []
        for record in self:
            result.append((record.id, "%s-%s" % (record.journal_id.name, record.period_id.name)))

        return result
