from odoo import models, fields, api, _


class FrAccountFircalyear(models.Model):
    _inherit = 'fr.account.fiscalyear'

    def get_financial_year(self, *args, **kwargs):
        domain = kwargs.pop('domain', [])
        domain.append(['state', '=', 'activated'])
        data = self.search_read(domain=domain)
        year_data = []
        result = []
        if data:
            for year in data:
                if year['name'] not in year_data:
                    account_date = self.env['fr.account.period'].sudo().search_read(domain=[['id', 'in', year.get('period_ids')]])
                    if account_date:
                        result.append({
                            'id': year.get('id'),
                            'name': year.get('name'),
                            'period': account_date,
                        })
                year_data.append(year['name'])
        return result
