from odoo import models


class SaleOrderExtend(models.Model):
    _inherit = "sale.order"

    def _create_invoices(self, grouped=False, final=False,date=None):
        moves = super(SaleOrderExtend, self)._create_invoices(grouped=grouped, final=final,date=date)

        for record in self:
            for invoice_id in record.invoice_ids:
                invoice_id.sale_order_ids = [(4, record.id)]

                for picking in record.picking_ids:
                    invoice_id.picking_ids = [(4, picking.id)]
        return moves