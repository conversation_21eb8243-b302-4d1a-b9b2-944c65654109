from odoo import models, api, fields


class Account<PERSON>ove(models.Model):
    _inherit = "account.move"
    sale_order_ids = fields.Many2many("sale.order", string='销售订单')
    purchase_order_ids = fields.Many2many("purchase.order", string='采购订单')
    picking_ids = fields.Many2many('stock.picking', string='库存单据')

    @api.onchange('move_type')
    def _onchange_move_type(self):
        if self.move_type == 'out_invoice':
            return {
                "domain": {
                    "partner_id": [
                        ("customer_rank", '>', 0),
                    ]
                }
            }

        if self.move_type == 'in_invoice':
            return {
                "domain": {
                    "partner_id": [
                        ("supplier_rank", '>', 0),
                    ]
                }
            }

    def show_tally_voucher(self):
        """
        跳转至记账凭证
        """
        return {
            'name': self.name,
            'view_mode': 'form',
            'res_id': self.id,
            'res_model': 'account.move',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('account_ledger.NewAccountMoveViewForm').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', '=', self.id)],
        }

    def show_picking(self):
        """
        库存单据
        """
        return {
            'name': '库存单据',
            'view_mode': 'tree,form',
            'res_model': 'stock.picking',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('stock.vpicktree').id, 'tree'],
                [self.env.ref('stock.view_picking_form').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', 'in', self.picking_ids.ids)],
        }

    def show_purchase_order(self):
        """
         跳转至采购订单
        """
        return {
            'name': '采购订单',
            'view_mode': 'tree',
            'res_model': 'purchase.order',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('purchase.purchase_order_view_tree').id, 'tree'],
                [self.env.ref('purchase.purchase_order_form').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', 'in', self.purchase_order_ids.ids)],
        }

    def show_sale_order(self):
        """
         跳转至销售订单
        """
        return {
            'name': '销售订单',
            'view_mode': 'tree,form',
            'res_model': 'sale.order',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('sale.view_order_tree').id, 'tree'],
                [self.env.ref('sale.view_order_form').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', 'in', self.sale_order_ids.ids)],
        }

    @api.onchange("partner_id")
    def _onchange_partner_id(self):
        if self.env.context.get("default_move_type") == "in_refund":
            return {
                'domain': {
                    'partner_id': [('supplier_rank', '>', 0)]
                }
            }
        if self.env.context.get("default_move_type") == "out_refund":
            return {
                'domain': {
                    'partner_id': [('customer_rank', '>', 0)]
                }
            }
