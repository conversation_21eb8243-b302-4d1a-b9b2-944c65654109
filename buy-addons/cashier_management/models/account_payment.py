from odoo import models, fields, api, _
import logging

_log = logging.getLogger(__name__)


class AccountJournal(models.Model):
    _inherit = 'account.journal'

    inbound_payment_method_ids = fields.Many2many(
        comodel_name='account.payment.method',
        relation='account_journal_inbound_payment_method_rel',
        column1='journal_id',
        column2='inbound_payment_method',
        domain=[('payment_type', '=', 'inbound')],
        string='Inbound Payment Methods',
        compute='_compute_inbound_payment_method_ids',
        store=True,
        readonly=False,
        help="Manual: Get paid by cash, check or any other method outside of Odoo.\n"
             "Electronic: Get paid automatically through a payment acquirer by requesting a transaction"
             " on a card saved by the customer when buying or subscribing online (payment token).\n"
             "Batch Deposit: Encase several customer checks at once by generating a batch deposit to"
             " submit to your bank. When encoding the bank statement in Odoo,you are suggested to"
             " reconcile the transaction with the batch deposit. Enable this option from the settings."
    )
    outbound_payment_method_ids = fields.Many2many(
        comodel_name='account.payment.method',
        relation='account_journal_outbound_payment_method_rel',
        column1='journal_id',
        column2='outbound_payment_method',
        domain=[('payment_type', '=', 'outbound')],
        string='Outbound Payment Methods',
        compute='_compute_outbound_payment_method_ids',
        store=True,
        readonly=False,
        help="Manual:Pay bill by cash or any other method outside of Odoo.\n"
             "Check:Pay bill by check and print it from Odoo.\n"
             "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your"
             " bank. Enable this option from the settings."
    )

    @api.depends('type')
    def _compute_inbound_payment_method_ids(self):
        for journal in self:
            if journal.type in ('bank', 'cash'):
                journal.inbound_payment_method_ids = journal._default_inbound_payment_methods()
            else:
                journal.inbound_payment_method_ids = False

    @api.depends('type')
    def _compute_outbound_payment_method_ids(self):
        for journal in self:
            if journal.type in ('bank', 'cash'):
                journal.outbound_payment_method_ids = journal._default_outbound_payment_methods()
            else:
                journal.outbound_payment_method_ids = False


class AccountPayment(models.Model):
    _inherit = 'account.payment'
    payment_type = fields.Selection([
        ('outbound', '付款'),
        ('inbound', '收款'),
    ], string='支付类型', default='outbound', required=True, tracking=True)

    partner_type = fields.Selection([
        ('customer', '客户'),
        ('supplier', '供应商'),
        ('employee', '内部员工'),
        ('other', '其他')
    ])

    other_service_type = fields.Selection([
        ('customer', '客户'),
        ('supplier', '供应商'),
    ], default='customer')

    hide_payment_method = fields.Boolean(
        compute='_compute_payment_method_fields',
        help="Technical field used to hide the payment method if the selected journal has only one available which is 'manual'")

    available_payment_method_ids = fields.Many2many('account.payment.method',
                                                    compute='_compute_payment_method_fields')

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company.id)

    # def button_open_statements(self):
    #     ''' Redirect the user to the statement line(s) reconciled to this payment.
    #     :return:    An action on account.move.
    #     '''
    #     self.ensure_one()
    #
    #     action = {
    #         'name': _("Matched Statements"),
    #         'type': 'ir.actions.act_window',
    #         'res_model': 'account.bank.statement',
    #         'context': {'create': False},
    #     }
    #     if len(self.reconciled_statement_ids) == 1:
    #         action.update({
    #             'view_mode': 'form',
    #             'res_id': self.reconciled_statement_ids.id,
    #         })
    #     else:
    #         action.update({
    #             'view_mode': 'list,form',
    #             'domain': [('id', 'in', self.reconciled_statement_ids.ids)],
    #         })
    #     return action

    @api.depends('payment_type',
                 'journal_id.inbound_payment_method_ids',
                 'journal_id.outbound_payment_method_ids')
    def _compute_payment_method_fields(self):
        for pay in self:
            if pay.payment_type == 'inbound':
                pay.available_payment_method_ids = pay.journal_id.inbound_payment_method_ids
                pay.account_id_payment = pay.partner_id.property_account_receivable_id.id or pay.account_id_payment
            else:
                pay.available_payment_method_ids = pay.journal_id.outbound_payment_method_ids
                pay.account_id_payment = pay.partner_id.property_account_payable_id.id or pay.account_id_payment

            pay.hide_payment_method = len(
                pay.available_payment_method_ids) == 1 and pay.available_payment_method_ids.code == 'manual'

    def partner_type_id_domain(self):
        if self._context.get("default_partner_type") == 'other':
            return [('name', 'not in', ['客户', '供应商'])]

        if self._context.get("default_partner_type") == 'supplier':
            return [('name', '=', '供应商')]

        if self._context.get("default_partner_type") == 'customer':
            return [('name', 'in', ['客户'])]

        return []

    def default_partner_type_id(self):
        if self._context.get("default_partner_type") == 'supplier':
            return self.env['res.partner.type'].search([('name', '=', '供应商')], limit=1)

        if self._context.get("default_partner_type") == 'customer':
            return self.env['res.partner.type'].search([('name', '=', '客户')], limit=1)

        return self.env['res.partner.type'].search([('name', '=', '员工')], limit=1)

    partner_type_id = fields.Many2one("res.partner.type", '业务合作伙伴类型',
                                      default=lambda self: self.default_partner_type_id(),
                                      domain=lambda self: self.partner_type_id_domain(),
                                      tracking=True)
    #
    # def partner_id_domain(self):
    #     if self._context.get("default_partner_type") == 'customer':
    #         return [('customer_rank', '>', 0), ('parent_id', '=', False)]
    #
    #     if self._context.get("default_partner_type") == 'supplier':
    #         return [('supplier_rank', '>', 0), ('parent_id', '=', False)]
    #
    #     return [('customer_rank', '=', 0), ('supplier_rank', '=', 0), ('parent_id', '=', False)]

    partner_id = fields.Many2one("res.partner", string='业务合作伙伴')

    sale_order_ids = fields.Many2many("sale.order", string='销售订单', domain="[('partner_id', '=', partner_id)]")
    purchase_order_ids = fields.Many2many("purchase.order", string='采购订单',
                                          domain="[('partner_id', '=', partner_id)]")

    partner_type_name = fields.Char('业务合作伙伴类型', related="partner_type_id.name")

    @api.onchange("partner_type")
    def _onchange_partner_type(self):
        self.ensure_one()
        if self.partner_type == 'customer':
            self.cash_flow_id = self.env['account.cash.flow'].search([('name', '=', '销售商品、提供劳务收到的现金')],
                                                                     limit=1)
            return {
                "domain": {
                    "partner_id": [('customer_rank', '>', 0), ('parent_id', '=', False)]
                }
            }
        elif self.partner_type == 'supplier':
            self.cash_flow_id = self.env['account.cash.flow'].search([('name', '=', '购买商品、接受劳务支付的现金')],
                                                                     limit=1)
            return {
                "domain": {
                    "partner_id": [('supplier_rank', '>', 0), ('parent_id', '=', False)]
                }
            }
        else:
            self.cash_flow_id = self.env['account.cash.flow'].search([], limit=1)
            return {
                "domain": {
                    "partner_id": [('customer_rank', '=', 0), ('supplier_rank', '=', 0), ('parent_id', '=', False)]
                }
            }

    def show_purchase_order(self):
        """
         跳转至采购订单
        """
        return {
            'name': '采购订单',
            'view_mode': 'tree',
            'res_model': 'purchase.order',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('purchase.purchase_order_view_tree').id, 'tree'],
                [self.env.ref('purchase.purchase_order_form').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', 'in', self.purchase_order_ids.ids)],
        }

    def show_sale_order(self):
        """
         跳转至销售订单
        """
        return {
            'name': '销售订单',
            'view_mode': 'tree,form',
            'res_model': 'sale.order',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('sale.view_order_tree').id, 'tree'],
                [self.env.ref('sale.view_order_form').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', 'in', self.sale_order_ids.ids)],
        }

    def sale_move_id(self):
        """
        跳转至记账凭证
        """
        return {
            'name': '记账凭证',
            'view_mode': 'form',
            'res_id': self.move_id.id,
            'res_model': 'account.move',
            'type': 'ir.actions.act_window',
            'views': [
                [self.env.ref('account_ledger.NewAccountMoveViewForm').id, 'form'],
            ],
            'target': 'current',
            'domain': [('id', '=', self.move_id.id)],
        }

    def action_post(self):
        result = super().action_post()
        for record in self:
            for move_line in record.move_id.line_ids:
                move_line.fr_cash_flow_id = record.cash_flow_id
            if not self._context.get('register', False):
                if record.is_internal_transfer or record.partner_type_id.name not in ['客户', '供应商']:
                    record.partner_type = 'other'
        return result

    @api.model_create_multi
    def create(self, vals_list):
        result = super(AccountPayment, self.with_context(pay=True)).create(vals_list)
        if self._context.get("default_partner_type") == 'other':
            for record in result:
                record.partner_type = 'other'
        return result

    @api.onchange("is_internal_transfer")
    def _onchange_internal_transfer(self):
        for payment in self:
            if payment.is_internal_transfer:
                payment.partner_id = payment.journal_id.company_id.partner_id.id
                payment.account_id_payment = payment.journal_id.default_account_id.id

    def _get_aml_default_display_map(self):
        return {
            ('outbound', 'customer'): _("客户退款"),
            ('inbound', 'customer'): _("客户付款"),
            ('outbound', 'supplier'): _("供应商付款"),
            ('inbound', 'supplier'): _("供应商退款"),
            ('outbound', 'other'): "其他 付款",
            ('inbound', 'other'): "其他 收款",
        }
