from odoo import models


class PurchaseOrderExtend(models.Model):
    _inherit = "purchase.order"

    def action_create_invoice(self):
        result = super(PurchaseOrderExtend, self).action_create_invoice()

        for record in self:
            for invoice_id in record.invoice_ids:
                invoice_id.purchase_order_ids = [(4, record.id)]

                for picking in record.picking_ids:
                    invoice_id.picking_ids = [(4, picking.id)]

        return result
