# -*- coding: utf-8 -*-
# from odoo import http


# class AccountAnalyticExtend(http.Controller):
#     @http.route('/account_analytic_extend/account_analytic_extend/', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/account_analytic_extend/account_analytic_extend/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('account_analytic_extend.listing', {
#             'root': '/account_analytic_extend/account_analytic_extend',
#             'objects': http.request.env['account_analytic_extend.account_analytic_extend'].search([]),
#         })

#     @http.route('/account_analytic_extend/account_analytic_extend/objects/<model("account_analytic_extend.account_analytic_extend"):obj>/', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('account_analytic_extend.object', {
#             'object': obj
#         })
