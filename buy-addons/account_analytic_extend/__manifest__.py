# -*- coding: utf-8 -*-
{
    'name': "会计分析扩展",

    'summary': """
        会计分析扩展""",

    'description': """
        会计分析扩展
    """,

    'author': "openerphk Team",
    'website': "http://cdn.openerp.hk",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': '财务管理/财务管理',
    'version': '********',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account', 'analytic', 'account_accountant','account_ledger'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/menus.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
    'sequence': 1,
    'license': 'LGPL-3',
}
