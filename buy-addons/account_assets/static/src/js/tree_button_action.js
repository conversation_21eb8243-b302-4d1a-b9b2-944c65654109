/** @odoo-module **/
import { registry } from "@web/core/registry";
import { listView } from "@web/views/list/list_view";
import { ListController } from "@web/views/list/list_controller";

export class OwlAccountAssetMove extends ListController {

    setup() {
        super.setup();
    }

    action_def() {
        this.actionService.doAction({
                name: "折旧明细",
                res_model: "wizard.asset.query",
                // 注意tree视图类型在js中使用'list'
                // views: [[false, 'list'], [false, 'form']],
                views: [[false, 'form']],
                type: 'ir.actions.act_window',
                target: "new",
                context: {
                    'default_tag': false
                },
                flags: {
                    action_buttons: false,
                }
            });
    }

}

export const OwlAccountAssetMoveView = {
     ...listView,
    Controller: OwlAccountAssetMove,
    buttonTemplate: "OwlAccountAssetMove",
}

registry.category("views").add("owl_account_asset_move", OwlAccountAssetMoveView);
