# coding=utf-8
# @Time : 2021/4/9 0008 14:24
# <AUTHOR> 王志
# @qq : **********
# @File : asset_move_line.py
from odoo import fields, models, _
from odoo.exceptions import UserError


class AssetMoveLine(models.Model):
    _name = 'asset.move.line'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = '折算明细'

    jd_asset_move_id = fields.Many2one('account.asset', string='资产')
    jd_ref = fields.Char(string='参考')
    jd_currency_id = fields.Many2one('res.currency', related='jd_asset_move_id.currency_id', string='外汇币种')
    jd_depreciation_date = fields.Date(string='折旧日期')
    jd_amount = fields.Monetary(string='折旧', currency_field='jd_currency_id')
    jd_cumulative_value = fields.Monetary(string='累计折旧', currency_field='jd_currency_id', )
    jd_remaining_value = fields.Monetary(string='残值', currency_field='jd_currency_id')
    jd_move_check = fields.Boolean(defaut=False, string='已连接')
    jd_move_posted_check = fields.Boolean(string='已过账', default=False)
    jd_parent_state = fields.Selection([('draft', '草稿'),
                                        ('open', '运行中'),
                                        ('close', '关闭')], string='资产状态', default='draft')

    jd_assets_code = fields.Char(related='jd_asset_move_id.jd_assets_code', string='固定资产编码')
    jd_department = fields.Many2one('hr.department', related='jd_asset_move_id.jd_department', string='所属部门')

    asset_category = fields.Many2one('account.asset', related='jd_asset_move_id.asset_category', string='资产类别',
                                     domain=[('asset_type', '=', 'purchase'), ('state', '=', 'model')])

    jd_original_value = fields.Monetary(string='原始价值', related='jd_asset_move_id.original_value',
                                        currency_field='jd_currency_id')

    def create_account_move(self, obj=None, state=None, move_list=None):
        """ 生成凭证 """

        period = self.env['fr.account.period'].search([
            ('date_end', '>=', self.jd_depreciation_date),
            ('date_start', '<=', self.jd_depreciation_date),
            ('company_id', '=', self.jd_asset_move_id.company_id.id)
        ])
        if not period:
            raise UserError(_('凭证日期对应的会计期间不存在！检查日期的会计年度是否已创建。'))

        dict_line = {
            'journal_id': obj.journal_id.id,
            'ref': self.jd_ref,
            'date': fields.Date.today() if period.state == 'close' else self.jd_depreciation_date,
            'state': 'draft',
            'currency_id': self.jd_currency_id.id,
            'jd_asset_id': [(6, 0, [obj.id])],
            'move_type': 'entry',
            'line_ids': []
        }
        if move_list:
            account = [(0, 0, {
                'account_id': item[0].id,
                'name': obj.name,
                'debit': item[1],
                'credit': item[2],
                # 2022/5/10 JIIE添加，为了在固定资产确认时费用科目勾选了分析账号或分析标签的话
                # 生成的凭证明细也要添加对应的分析账户和分析标签
                'analytic_tag_ids': obj.analytic_tag_ids if item[0].label_bool else False,
                'analytic_account_id': obj.account_analytic_id.id if item[0].analysis_bool else False,
            }) for item in move_list]
            dict_line['line_ids'] = account
        if state:
            self.write({'jd_move_check': True, 'jd_move_posted_check': True, 'jd_parent_state': 'open'})
            self.jd_move_posted_check = True
            self.jd_parent_state = 'open'
        return dict_line
