# coding=utf-8
# @Time : 2021/4/8 0008 14:24
# <AUTHOR> 王志
# @qq : **********
# @File : inherit_account_asset.py
import calendar
from datetime import datetime
from typing import Any, Union

from dateutil.relativedelta import relativedelta

from odoo import api, fields, models, _
from odoo.tools import float_is_zero, float_round, UserError


class InheritAccountAsset(models.Model):
    _inherit = 'account.asset'

    jd_assets_code = fields.Char(string='固定资产编码', readonly=True, default=lambda self: _('New'))
    jd_place_storage = fields.Char(string='存放位置', tracking=True)
    jd_specification = fields.Char(string='规格型号', tracking=True)
    jd_original_value = fields.Float(string='原始价值', compute='jd_compute_original_value', tracking=True, store=True)
    jd_method_number = fields.Float(string='折旧期', compute='jd_compute_method_number', tracking=True, store=True)
    jd_salvage_value = fields.Float(string='净残值', compute='jd_compute_salvage_value', tracking=True, store=True)
    jd_method = fields.Selection([('linear', '直线'), ('degressive', '下降')], default='linear', tracking=True)
    jd_department = fields.Many2one('hr.department', string='所属部门', tracking=True)
    asset_category = fields.Many2one('account.asset', string='资产类别',
                                     domain=[('asset_type', '=', 'purchase'), ('state', '=', 'model')], tracking=True)
    jd_line_id = fields.One2many('asset.move.line', 'jd_asset_move_id', string='折算明细', tracking=True)
    note = fields.Char(string='备注')
    prorata = fields.Boolean(
        string='Prorata Temporis', readonly=True,
        help='Indicates that the first depreciation entry for this asset have to be done '
             'from the asset date (purchase date) instead of the first January / Start date of fiscal year'
    )
    first_depreciation_date = fields.Date(
        string='First Depreciation Date',
        compute='_compute_first_depreciation_date', store=True, readonly=False,
        help='Note that this date does not alter the computation of the first journal '
             'entry in case of prorata temporis assets. It simply changes its accounting date',
    )
    # odoo17  取消字段
    asset_type = fields.Selection(
        [('sale', 'Sale: Revenue Recognition'), ('purchase', 'Purchase: Asset'), ('expense', 'Deferred Expense')],
        compute='_compute_asset_type', store=True, index=True, copy=True)

    @api.depends('original_move_line_ids')
    @api.depends_context('asset_type')
    def _compute_asset_type(self):
        for record in self:
            if not record.asset_type and 'asset_type' in self.env.context:
                record.asset_type = self.env.context['asset_type']
            if not record.asset_type and record.original_move_line_ids:
                account = record.original_move_line_ids.account_id
                record.asset_type = account.asset_type


    @api.depends('original_value')
    def jd_compute_original_value(self):
        for item in self:
            item.jd_original_value = item.original_value

    @api.depends('method_number')
    def jd_compute_method_number(self):
        for item in self:
            item.jd_method_number = item.method_number

    @api.depends('salvage_value')
    def jd_compute_salvage_value(self):
        for item in self:
            item.jd_salvage_value = item.salvage_value

    @api.onchange('jd_method')
    def onchange_method_value(self):
        """ 折旧方式 """
        self.method = self.jd_method

    @api.onchange('original_value')
    def onchange_original_value(self):
        """ 净残值按照原始价值的5%计算 """
        self.salvage_value = self.original_value * 0.05

    @api.onchange('jd_department')
    def onchange_department(self):
        """ 存放位置为所属部门位置 """
        self.jd_place_storage = self.jd_department.name

    def asset_variation(self):
        """ 返回打开资产变动向导的操作 """
        self.ensure_one()
        new_wizard = self.env['wizard.variation'].create({
            'jd_asset_id': self.id,
        })
        return {
            'name': '资产变动',
            'view_mode': 'form',
            'res_model': 'wizard.variation',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': new_wizard.id,
            'context': self.env.context,
        }

    @api.onchange('name')
    def onchange_acquisition_date(self):
        """ 默认当前日期 """
        self.acquisition_date = fields.Date.today()

    @api.model
    def create(self, vals):
        vals['jd_assets_code'] = self.env['ir.sequence'].next_by_code('account.asset') or _('New')
        result = super(InheritAccountAsset, self).create(vals)
        return result

    def action_asset_modify(self):
        """ 返回打开资产修改向导的操作 """
        self.ensure_one()
        new_wizard = self.env['asset.modify'].create({
            'asset_id': self.id,
            'jd_original_value': self.original_value,
            'method_number': self.method_number,
        })
        return {
            'name': _('Modify Asset'),
            'view_mode': 'form',
            'res_model': 'asset.modify',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': new_wizard.id,
            'context': self.env.context,
        }

    def action_asset_pause(self):
        """ 返回一个打开资产暂停向导的操作 """
        self.ensure_one()
        new_wizard = self.env['account.asset.pause'].create({
            'asset_id': self.id,
        })
        return {
            'name': _('Pause Asset'),
            'view_mode': 'form',
            'res_model': 'account.asset.pause',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': new_wizard.id,
        }

    def action_set_to_close(self):
        """ 返回一个打开资产销售或处置向导的操作。"""
        self.ensure_one()
        new_wizard = self.env['account.asset.sell'].create({
            'asset_id': self.id,
        })
        return {
            'name': _('Sell Asset'),
            'view_mode': 'form',
            'res_model': 'account.asset.sell',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': new_wizard.id,
        }

    @api.onchange('asset_category')
    def onchange_category_data(self):
        """资产类别，带出会计信息"""
        if self.asset_category:
            self.method = self.asset_category.method
            self.method_number = self.asset_category.method_number
            self.method_period = self.asset_category.method_period
            self.prorata = self.asset_category.prorata
            self.account_asset_id = self.asset_category.account_asset_id.id
            self.account_depreciation_id = self.asset_category.account_depreciation_id.id
            self.account_depreciation_expense_id = self.asset_category.account_depreciation_expense_id.id
            self.journal_id = self.asset_category.journal_id.id
            self.method_progress_factor = self.asset_category.method_progress_factor
            self.jd_method = self.asset_category.jd_method
            # if self.asset_category.account_analytic_id:
            #     self.account_analytic_id = self.asset_category.account_analytic_id.id
            # if self.asset_category.analytic_tag_ids:
            #     self.write({'analytic_tag_ids': [(6, 0, [item.id for item in self.analytic_tag_ids.analytic_tag_ids])]})

    def validate(self):
        """确认按钮"""
        self.write({'state': 'open'})
        # 2022/5/10 JIIE添加，在确认固定资产时，如果费用科目勾选了分析账户或分析标签的话
        # 固定资产单也要添加对应的分析账户和分析标签
        for order in self:
            if order.account_depreciation_expense_id.analysis_bool and not order.account_analytic_id:
                raise UserError('请添加分析账户或取消勾选费用科目中的分析账户！')
            if order.account_depreciation_expense_id.label_bool and not order.analytic_tag_ids:
                raise UserError('请添加分析标签或取消勾选费用科目中的分析标签！')

        if not self.jd_line_id:
            self.compute_depreciation_board()
        else:
            self.create_account_move_data()

    def compute_depreciation_board(self, depreciation_date=None, depreciation_num=None, residual_amount=None):
        """生成折旧明细"""
        self.ensure_one()
        if self.state == 'draft':
            self.jd_line_id.unlink()
        residual_amount = residual_amount or self.value_residual
        depreciation_num = depreciation_num or self.method_number
        depreciation_num_copy = depreciation_num
        prorata = self.prorata
        staring_sequence = 0
        depreciation_date = depreciation_date or self.first_depreciation_date
        move_vals = []
        if self.prorata:
            depreciation_num += 1
        for asset_sequence in range(staring_sequence + 1, depreciation_num + 1):
            prorata_factor = 1
            if self.jd_method == 'linear':
                if asset_sequence == depreciation_num:
                    amount = residual_amount
                else:
                    amount = min(self.value_residual / depreciation_num_copy, residual_amount)
            elif self.jd_method == 'degressive':
                if asset_sequence == depreciation_num:
                    amount = residual_amount
                else:
                    amount = min(residual_amount * self.method_progress_factor, residual_amount)
            jd_ref = self.name + '(%s/%s)' % (prorata and asset_sequence - 1 or asset_sequence, depreciation_num_copy)
            # 按比例分配时且是第一个折算明细时执行
            if prorata and asset_sequence == 1:
                jd_ref = self.name + ' ' + '(按比例输入)'
                first_date = self.prorata_date
                if int(self.method_period) % 12 != 0:
                    month_days = calendar.monthrange(first_date.year, first_date.month)[1]
                    days = month_days - first_date.day + 1
                    prorata_factor = days / month_days
                else:
                    total_days = (depreciation_date.year % 4) and 365 or 366
                    days = (self.company_id.compute_fiscalyear_dates(first_date)['date_to'] - first_date).days + 1
                    prorata_factor = days / total_days
            amount = self.currency_id.round(amount * prorata_factor)
            if float_is_zero(amount, precision_rounding=self.currency_id.rounding):
                continue

            # 残值计算
            residual_amount -= amount
            move_vals.append({
                'jd_ref': jd_ref,
                'jd_asset_move_id': self.id,
                'jd_depreciation_date': depreciation_date,
                'jd_amount': amount,
                # 'jd_cumulative_value': self.value_residual - residual_amount,
                'jd_cumulative_value': self.original_value - self.salvage_value - residual_amount,
                'jd_remaining_value': float_round(residual_amount, precision_rounding=self.currency_id.rounding),
            })
            # 计算下一次折旧时间
            if depreciation_date:
                depreciation_date = depreciation_date + relativedelta(months=int(self.method_period))
            if int(self.method_period) % 12 != 0:
                max_day_in_month = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                depreciation_date = depreciation_date.replace(day=max_day_in_month)
        asset = self.env['asset.move.line'].create(move_vals)

        # 生成折旧明细时，将已过折旧日期的明细生成凭证
        if self.state != 'draft':
            self.create_account_move_data()

    def create_account_move_data(self):
        """ 将已过折旧日期的明细生成凭证 """
        now_date = fields.Date.today()
        move_dict = []
        for item in self.jd_line_id:
            if item.jd_depreciation_date and item.jd_depreciation_date <= now_date and item.jd_parent_state == 'draft':
                move_lists = [(self.account_depreciation_id, 0, item.jd_amount),
                              (self.account_depreciation_expense_id, item.jd_amount, 0)]
                dict_line = item.create_account_move(obj=self, state=True, move_list=move_lists)
                move_dict.append(dict_line)
        move = self.env['account.move'].create(move_dict)
        move.action_post()

    @api.depends('depreciation_move_ids.state', 'parent_id')
    def _entry_count(self):
        """已过账条目计算"""
        for asset in self:
            res = self.env['account.move'].search_count(
                [('jd_asset_id', 'in', [asset.id]), ('state', '=', 'posted'), ('reversal_move_id', '=', False)])
            depreciation_move_ids = self.env['account.move'].search(
                [('jd_asset_id', 'in', [asset.id]), ('state', '=', 'posted'), ('reversal_move_id', '=', False)])
            asset.depreciation_entries_count = res or 0
            asset.total_depreciation_entries_count = len(depreciation_move_ids)
            asset.gross_increase_count = len(asset.children_ids)

    def open_entries(self):
        """已过账条目明细跳转到会计凭证"""
        return {
            'name': _('Journal Entries'),
            'view_mode': 'tree,form',
            'res_model': 'account.move',
            'views': [(self.env.ref('account.view_move_tree').id, 'tree'), (False, 'form')],
            'type': 'ir.actions.act_window',
            'domain': [('jd_asset_id', 'in', self.id)],
            'context': dict(self._context, create=False),
        }

    @api.depends('original_value', 'salvage_value', 'already_depreciated_amount_import', 'depreciation_move_ids.state')
    def _compute_value_residual(self):
        for record in self:
            record.value_residual = record.original_value - record.salvage_value - record.already_depreciated_amount_import - abs(
                sum(record.jd_line_id.filtered(lambda m: m.jd_parent_state == 'open').mapped('jd_amount')))

    def _compute_first_depreciation_date(self):
        """ 自动生成开始折旧日期 """
        for record in self:
            pre_depreciation_date = record.acquisition_date or min(record.original_move_line_ids.mapped('date'), default=record.acquisition_date) or fields.Date.today()
            depreciation_date = pre_depreciation_date + relativedelta(months=1) + relativedelta(day=31)
            # ... or fiscalyear depending the number of period
            if record.method_period == '12':
                depreciation_date = depreciation_date + relativedelta(month=int(record.company_id.fiscalyear_last_month))
                depreciation_date = depreciation_date + relativedelta(day=record.company_id.fiscalyear_last_day)
                if depreciation_date < pre_depreciation_date:
                    depreciation_date = depreciation_date + relativedelta(years=1)
            record.first_depreciation_date = depreciation_date

    def onchange(self, values, field_name, field_onchange):
        # Force the re-rendering of computed fields on the o2m
        if field_name == 'depreciation_move_ids':
            return super().onchange(values, False, {
                fname: spec
                for fname, spec in field_onchange.items()
                if fname.startswith('depreciation_move_ids')
            })
        # if isinstance(field_name, list) and not len(field_name):
        #     return
        return super().onchange(values, field_name, field_onchange)


class AccountMove(models.Model):
    _inherit = 'account.move'
    jd_asset_id = fields.Many2many('account.asset')
