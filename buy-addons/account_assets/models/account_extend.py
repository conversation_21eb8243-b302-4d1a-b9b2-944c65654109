# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _


class AccountExtend(models.Model):
    _inherit = 'account.account'

    asset_type = fields.Selection(
        [('sale', 'Deferred Revenue'), ('expense', 'Deferred Expense'), ('purchase', 'Asset')],
        compute='_compute_can_create_asset')

    @api.depends('account_type')
    def _compute_can_create_asset(self):
        for record in self:
            if record.auto_generate_asset():
                record.asset_type = 'purchase'
            elif record.auto_generate_deferred_revenue():
                record.asset_type = 'sale'
            elif record.auto_generate_deferred_expense():
                record.asset_type = 'expense'
            else:
                record.asset_type = False

            record.can_create_asset = record.account_type in ('asset_fixed', 'asset_non_current')
            record.form_view_ref = 'account_asset.view_account_asset_form'

    def auto_generate_asset(self):
        return self.account_type in ('asset_fixed', 'asset_non_current')

    def auto_generate_deferred_revenue(self):
        return self.account_type in ('liability_non_current', 'liability_current')

    def auto_generate_deferred_expense(self):
        return self.account_type in ('asset_prepayments', 'asset_current')
