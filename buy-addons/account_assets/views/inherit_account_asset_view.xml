<?xml version="1.0"?>
<odoo>
    <record model="ir.actions.act_window" id="action_account_asset_form_1">
        <field name="name">Assets</field>
        <field name="res_model">account.asset</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('account_asset.view_account_asset_tree')}),
            (0, 0, {'view_mode': 'kanban'}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('account_asset.view_account_asset_form')})]"/>
        <field name="domain">[('asset_type', '=', 'purchase'), ('state', '!=', 'model'), ('parent_id', '=', False)]
        </field>
        <field name="context">{'asset_type': 'purchase', 'default_asset_type': 'purchase'}</field>
    </record>

    <record id="inherit_account_asset_view_form" model="ir.ui.view">
        <field name="name">Inherit Account Asset Form</field>
        <field name="model">account.asset</field>
        <field name="inherit_id" ref="account_asset.view_account_asset_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form/header/button[@name='action_save_model']" position="before">
                <button name="asset_variation" string="资产变动" type="object"
                        invisible="state not in ['open', 'paused']"/>
            </xpath>
            <xpath expr="//form//field[@name='method']" position="after">
                <field name="jd_method" string="折旧方式" widget="radio" options="{'horizontal': true}"/>
            </xpath>
            <xpath expr="//field[@name='model_id']" position="replace">

            </xpath>
            <xpath expr="//field[@name='acquisition_date']" position="after">
                <field name="asset_category" string="资产类别"
                       required="state != 'model'"/>
            </xpath>
            <xpath expr="//form/sheet/group" position="inside">
                <group invisible="state == 'model'" string="资产信息">
                    <field name="jd_assets_code" string="固定资产编码"/>
                    <field name="jd_specification" string="规格型号"/>
                    <field name="jd_department" string="所属部门" readonly="state == 'draft'"/>
                    <field name="jd_place_storage" string="存放位置" readonly="state != 'draft'"/>
                </group>
            </xpath>
            <xpath expr="//page[@name='depreciation_board']" position="after">
                <page string="折旧明细">
                    <field name="jd_line_id" string="折旧明细">
                        <tree decoration-info="jd_parent_state == 'draft'" create="0"
                              editable="bottom"
                              default_order="jd_depreciation_date asc, id asc">
                            <field name="jd_ref" string="参考"/>
                            <field name="jd_depreciation_date" string="折旧日期"/>
                            <field name="jd_amount" string="折旧" widget="monetary"/>
                            <field name="jd_cumulative_value" widget="monetary" string="累计折旧"/>
                            <field name="jd_remaining_value" widget="monetary" string="残值"/>
                            <field name="jd_move_check" string="已连接" invisible="1"/>
                            <field name="jd_move_posted_check" string="已过账" invisible="1"/>
                            <field name="jd_parent_state" string="资产状态" invisible="1"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//form//field[@name='already_depreciated_amount_import']" position="after">
                <field name="note" string="备注"/>
            </xpath>


            <xpath expr="//form//field[@name='original_value']" position="after">
                <field name="jd_original_value" string="原始价值" invisible="1"/>
                <field name="jd_method_number" string="折旧期" invisible="1"/>
                <field name="jd_salvage_value" string="净残值" invisible="1"/>
            </xpath>
            <xpath expr="//form//field[@name='method']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//notebook" position="attributes">
                <attribute name="invisible">state == 'model'</attribute>
            </xpath>
            <xpath expr="//form//field[@name='salvage_value']" position="attributes">
                <attribute name="string">净残值</attribute>
            </xpath>
            <xpath expr="//form//field[@name='already_depreciated_amount_import']" position="attributes">
                <attribute name="string">已折旧金额</attribute>
            </xpath>
            <xpath expr="//page[@name='depreciation_board']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="inherit_account_asset_view_tree" model="ir.ui.view">
        <field name="name">Inherit Account Asset Tree</field>
        <field name="model">account.asset</field>
        <field name="inherit_id" ref="account_asset.view_account_asset_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree/field[@name='name']" position="before">
                <field name="asset_category" string="资产类别"/>
            </xpath>
            <xpath expr="//tree/field[@name='name']" position="after">
                <field name="jd_assets_code" string="固定资产编码"/>
            </xpath>
            <xpath expr="//tree/field[@name='book_value']" position="before">
                <field name="original_value" string="原始价值" sum="总计"/>
            </xpath>
            <xpath expr="//tree/field[@name='value_residual']" position="after">
                <field name="salvage_value" string="净残值" sum="总计"/>
            </xpath>
            <xpath expr="//tree/field[@name='company_id']" position="after">
                <field name="jd_department" string="所属部门"/>
                <field name="acquisition_date" string="购买日期"/>
            </xpath>
            <xpath expr="//tree/field[@name='value_residual']" position="attributes">
                <attribute name="sum">总计</attribute>
            </xpath>
            <xpath expr="//tree/field[@name='book_value']" position="attributes">
                <attribute name="sum">总计</attribute>
            </xpath>
            <xpath expr="//tree/field[@name='company_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//tree/field[@name='currency_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="inherit_account_asset_view_kanban" model="ir.ui.view">
        <field name="name">Inherit Account Asset Kanban</field>
        <field name="model">account.asset</field>
        <field name="inherit_id" ref="account_asset.view_account_asset_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban//div[@class='row mb4']" position="before">
                <div class="row mb4">
                    <div class="col-6">
                        <b>
                            <field name="jd_assets_code" string="固定资产编码"/>
                        </b>
                    </div>
                    <div class="col-6 text-right">
                        <b>原始价值:
                            <field name="original_value" string="原始价值"/>
                        </b>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="inherit_account_move_view_form" model="ir.ui.view">
        <field name="name">Inherit Account Move</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='journal_div']" position="after">
                <field name="jd_asset_id" string="资产" widget="many2many_tags" invisible="1"/>
            </xpath>
        </field>
    </record>

</odoo>