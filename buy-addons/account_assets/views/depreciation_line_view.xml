<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<odoo>
    <record id="depreciation_line_tree_view" model="ir.ui.view">
        <field name="name">折旧明细</field>
        <field name="model">asset.move.line</field>
        <field name="arch" type="xml">
            <tree default_order="id desc" create="0" js_class="owl_account_asset_move">
                <field name="asset_category" string="资产类别"/>
                <field name="jd_asset_move_id" string="资产名称"/>
                <field name="jd_assets_code" string="固定资产编码"/>
                <field name="jd_original_value" string="原始价值" sum="总计"/>
                <field name="jd_department" string="所属部门"/>
                <field name="jd_ref" string="参考"/>
                <field name="jd_depreciation_date" string="折旧日期"/>
                <field name="jd_amount" string="本期折旧" widget="monetary" sum="总计"/>
                <field name="jd_cumulative_value" widget="monetary" string="累计折旧" sum="总计"/>
                <field name="jd_remaining_value" widget="monetary" string="残值" sum="总计"/>
                <field name="jd_move_check" string="已连接" invisible="1"/>
                <field name="jd_move_posted_check" string="已过账" invisible="1"/>
                <field name="jd_parent_state" string="资产状态" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="depreciation_line_action_view" model="ir.actions.act_window">
        <field name="name">资产折旧明细</field>
        <field name="res_model">asset.move.line</field>
        <field name="view_mode">tree</field>
<!--        <field name="view_id" ref="account_assets.jd_account_asset_tree_view"/>-->
        <field name="domain">[('jd_move_posted_check', '=', True)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                折旧明细
            </p>
        </field>
    </record>

</odoo>