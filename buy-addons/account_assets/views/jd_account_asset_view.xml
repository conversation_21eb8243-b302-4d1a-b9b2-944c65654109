<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<odoo>
    <record id="jd_account_asset_tree_view" model="ir.ui.view">
        <field name="name">资产盘点</field>
        <field name="model">account.asset</field>
        <field name="arch" type="xml">
            <tree create="0" default_order="id desc" decoration-info="(state == 'draft')" decoration-muted="(state == 'close')" decoration-warning="(state == 'close' and value_residual != 0)">
                <field name="asset_category" string="资产类别"/>
                <field name="name" string="资产名称"/>
                <field name="jd_assets_code" string="固定资产编码"/>
                <field name="jd_specification" string="规格型号"/>
                <field name="original_value" string="原始价值" sum="总计"/>
                <field name="value_residual" string="可贬值价值" sum="总计"/>
                <field name="jd_department" string="所属部门"/>
                <field name="jd_place_storage" string="存放位置"/>
                <field name="acquisition_date" string="购买日期"/>
                <field name="state" string="状态"/>
            </tree>
        </field>
    </record>

    <record id="jd_account_asset_action_view" model="ir.actions.act_window">
        <field name="name">资产盘点</field>
        <field name="res_model">account.asset</field>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="account_assets.jd_account_asset_tree_view"/>
        <field name="domain">[('state', '!=', 'model')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                资产盘点
            </p>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_account_asset_model_form_type">
        <field name="name">资产类别</field>
        <field name="res_model">account.asset</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('account_asset.view_account_asset_model_tree')}),
            (0, 0, {'view_mode': 'kanban'}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('account_asset.view_account_asset_form')})]"/>
        <field name="search_view_id" ref="account_asset.view_account_asset_model_search"/>
        <field name="domain">[('asset_type', '=', 'purchase'), ('state', '=', 'model')]</field>
        <field name="context">{'asset_type': 'purchase', 'default_asset_type': 'purchase', 'default_state': 'model'}</field>
    </record>

</odoo>