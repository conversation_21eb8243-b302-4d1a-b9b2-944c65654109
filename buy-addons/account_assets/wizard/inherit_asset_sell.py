# @Time : 2021/4/18 0018 10:21 
# <AUTHOR> 王志
# @qq : **********
# @File : inherit_asset_sell.py
from odoo import models, fields


class InheritAssetSell(models.TransientModel):
    _inherit = 'account.asset.sell'

    def do_action(self):
        self.ensure_one()
        asset = self.asset_id
        line = asset.jd_line_id.filtered(lambda x: x.jd_parent_state == 'draft')
        if len(line) > 0:
            line.unlink()
        asset.write({'state': 'close'})
        move_line_dict = {
            'jd_ref': asset.name,
            'jd_asset_move_id': asset.id,
            'jd_depreciation_date': fields.Date.today(),
            'jd_amount': asset.original_value,
            'jd_cumulative_value': asset.original_value - asset.book_value,
        }
        if self.action == 'dispose':
            move_line_dict['jd_ref'] += ':处置'
            data = self.env['asset.move.line'].create(move_line_dict)
            move_lists = [
                (asset.account_asset_id, 0, asset.original_value),
                (asset.account_depreciation_id, asset.original_value - asset.book_value, 0),
                (self.loss_account_id, asset.book_value, 0),
            ]
            move_dict = data.create_account_move(obj=asset, move_list=move_lists)
        else:
            move_line_dict['jd_ref'] += ':出售'
            data = self.env['asset.move.line'].create(move_line_dict)
            account = self.invoice_id.invoice_line_ids
            account_line_id = account if len(account) == 1 else self.invoice_line_id
            difference = asset.book_value - account_line_id.price_subtotal
            move_lists = [(asset.account_asset_id, 0, asset.original_value),
                          (asset.account_depreciation_id, asset.original_value - asset.book_value, 0),
                          (account_line_id.account_id, account_line_id.price_subtotal, 0),
                          (self.loss_account_id, difference if difference >= 0 else 0, -difference if difference < 0 else 0)]
            move_dict = data.create_account_move(obj=asset, move_list=move_lists)
        move = self.env['account.move'].create(move_dict)
        form = self.env.ref('account.view_move_form')
        return {
            'type': 'ir.actions.act_window',
            'name': '会计凭证',
            'res_model': 'account.move',
            'views': [(form.id, 'form')],
            'res_id': move.id,
            'view_type': 'form',
            'view_mode': 'form',
            'target': 'current'
        }