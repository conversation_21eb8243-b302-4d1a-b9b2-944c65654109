# @Time : 2021/4/13 0013 14:13 
# <AUTHOR> 王志
# @qq : 1282319340
# @File : inherit_asset_modify.py
import calendar

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InheritAssetModify(models.TransientModel):
    _inherit = 'asset.modify'

    jd_original_value = fields.Float(string='原始价值')

    @api.onchange('jd_original_value')
    def onchange_original_value_wizard(self):
        self.salvage_value = self.jd_original_value * 0.05

    def modify(self):
        asset = self.asset_id
        line = asset.jd_line_id.filtered(lambda x: x.jd_parent_state == 'draft')
        posted = asset.jd_line_id.filtered(lambda x: x.jd_move_posted_check == True)
        if len(posted):
            depreciation_num = self.method_number - len(posted)
        else:
            depreciation_num = self.method_number
        if not depreciation_num:
            raise UserError(_("时长应大于已折旧期数量"))
        if len(line):
            line.unlink()
        asset.write({
            'state': 'open',
            'original_value': self.jd_original_value,
            'salvage_value': self.salvage_value,
            'method_number': self.method_number,
            'method_period': self.method_period,
        })
        if asset.prorata:
            asset.compute_depreciation_board(self.date, self.method_number, asset.value_residual)
        else:
            max_day_in_month = calendar.monthrange(self.date.year, self.date.month)[1]
            depreciation_date = self.date.replace(day=max_day_in_month)
            asset.compute_depreciation_board(depreciation_date, depreciation_num, asset.value_residual)

    @api.depends('asset_id', 'value_residual', 'salvage_value')
    def _compute_need_date(self):
        for record in self:
            record.need_date = False

    @api.depends('asset_id', 'value_residual', 'salvage_value')
    def _compute_gain_value(self):
        for record in self:
            record.gain_value = False
