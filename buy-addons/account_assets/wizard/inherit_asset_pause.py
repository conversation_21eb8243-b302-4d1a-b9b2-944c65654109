# @Time : 2021/4/13 0013 10:05 
# <AUTHOR> 王志
# @qq : **********
# @File : inherit_asset_pause.py
from odoo import models, _
from odoo.exceptions import UserError


class InheritAssetPause(models.TransientModel):
    _inherit = 'account.asset.pause'

    def do_action(self):
        for record in self:
            if record.asset_id.jd_line_id:
                before_pause = record.asset_id.jd_line_id.filtered(lambda x: x.jd_depreciation_date <= record.date)
                after_pause = record.asset_id.jd_line_id.filtered(lambda x: x.jd_depreciation_date > record.date)
                if record.asset_id.prorata:
                    max_before = before_pause and max(before_pause, key=lambda x: x.jd_depreciation_date)
                    min_after = after_pause and min(after_pause, key=lambda x: x.jd_depreciation_date)
                    try:
                        day = (record.date - max_before.jd_depreciation_date).days
                        all_day = (min_after.jd_depreciation_date - max_before.jd_depreciation_date).days
                        time_ratio = day / all_day
                        jd_amount = max_before.jd_amount * time_ratio
                        record.asset_id.write({
                            'jd_line_id': [(0, 0, {
                                'jd_ref': record.asset_id.name + ':资产冻结',
                                'jd_asset_move_id': record.asset_id.id,
                                'jd_depreciation_date': record.date,
                                'jd_amount': jd_amount,
                                'jd_remaining_value': record.asset_id.value_residual - jd_amount,
                                'jd_cumulative_value': max_before.jd_cumulative_value + jd_amount,
                            })]
                        })
                    except ZeroDivisionError:
                        pass
                after_pause.unlink()
                line = record.asset_id.jd_line_id.filtered(lambda x: x.jd_parent_state == 'draft')
                # line.create_account_move(obj=record.asset_id)
                record.asset_id.write({'state': 'paused'})
            else:
                raise UserError(_("Trying to pause an asset without any future depreciation line"))
