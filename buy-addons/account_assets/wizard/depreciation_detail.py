# @Time : 2021/4/12 0012 11:36 
# <AUTHOR> 王志
# @qq : **********
# @File : depreciation_detail.py
from odoo import models, fields, _, api
from odoo.exceptions import UserError


class DepreciationDetail(models.TransientModel):
    _name = 'depreciation.detail'
    _description = 'DepreciationDetail'

    date = fields.Date('Account Date', required=False, default=fields.Date.today())
    fiscalyear_id = fields.Many2one('fr.account.fiscalyear', string='会计年度')
    period_id = fields.Many2one('fr.account.period', string='会计期间')
    period_state = fields.Selection(related='period_id.state', string='期间状态')

    @api.onchange('fiscalyear_id')
    def onchange_period(self):
        if self.fiscalyear_id:
            period = self.env['fr.account.period'].search([('fiscalyear_id', '=', self.fiscalyear_id.id)])
            period_data = period.filtered(lambda obj: obj.date_start <= fields.Date.today() and obj.state in ['open', 'ongoing'])
            ids = [item.id for item in period_data]
            return {'domain': {'period_id': [('id', 'in', ids)]}}

    def asset_compute(self):
        asset = self.env['account.asset'].search(
            [('asset_type', '=', self._context.get('asset_type')), ('state', '=', 'open')])
        categorys = set([item.asset_category for item in asset if item.asset_category])
        # move_line = self.env['asset.move.line'].search([('jd_parent_state', '=', 'draft')])
        all_res = []
        for category in categorys:
            asset_list = []
            for item in asset:
                if item.asset_category == category:
                    # if item.jd_line_id:
                    #     print(item.id,item.name)
                    for obj in item.jd_line_id:
                        # print(obj.id,obj.jd_ref,obj.jd_depreciation_date,obj.jd_parent_state)
                        if obj.jd_depreciation_date <= self.period_id.date_end and obj.jd_parent_state == 'draft':
                            dict_line = {
                                'asset_id': item.id,
                                'amount': obj.jd_amount
                            }
                            asset_list.append(dict_line)
                            obj.write({'jd_move_check': True, 'jd_move_posted_check': True, 'jd_parent_state': 'open'})

            if asset_list:
                amount = sum([asset_data['amount'] for asset_data in asset_list])
                ids = [asset_data['asset_id'] for asset_data in asset_list]

                dict_line = {
                    'journal_id': category.journal_id.id,
                    'ref': category.name + '(分组)',
                    'date': self.period_id.date_end,
                    'state': 'draft',
                    'jd_asset_id': [(6, 0, ids)],
                    'move_type': 'entry',
                    'line_ids': []
                }
                depreciation = (0, 0, {
                    'account_id': category.account_depreciation_id.id,
                    'name': category.name + '(分组)',
                    'credit': amount
                })
                dict_line['line_ids'].append(depreciation)
                depreciation_expense = (0, 0, {
                    'account_id': category.account_depreciation_expense_id.id,
                    'name': category.name + '(分组)',
                    'debit': amount
                })
                dict_line['line_ids'].append(depreciation_expense)
                asset_list.append(dict_line)

                move = self.env['account.move'].create(dict_line)
                try:
                    move.action_post()
                except Exception as e:
                    print(e)
            all_res += asset_list
        if all_res:
            return {}
        else:
            raise UserError(_('该期间不存在待生成固定资产分录.'))



