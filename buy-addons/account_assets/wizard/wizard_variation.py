# @Time : 2021/4/18 0018 16:11 
# <AUTHOR> 王志
# @qq : **********
# @File : wizard_variation.py
from odoo import models, fields


class WizardVariation(models.TransientModel):
    _name = 'wizard.variation'
    _description = 'WizardVariation'

    jd_asset_id = fields.Many2one('account.asset', string='资产')
    jd_place_storage = fields.Char(string='存放位置')
    jd_department = fields.Many2one('hr.department', string='所属部门')

    def asset_compute(self):
        if self.jd_asset_id:
            asset = self.jd_asset_id
            asset.jd_place_storage = self.jd_place_storage
            asset.jd_department = self.jd_department.id

