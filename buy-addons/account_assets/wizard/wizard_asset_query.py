# @Time : 2021/4/19 0019 11:54 
# <AUTHOR> 王志
# @qq : 1282319340
# @File : wizard_asset_query.py
from odoo import models, fields


class WizardAssetQuery(models.TransientModel):
    _name = 'wizard.asset.query'
    _description = 'WizardAssetQuery'

    start_date = fields.Date(string='开始时间')
    end_date = fields.Date(string='结束时间')

    def process(self):
        return {
            'name': "折旧明细",
            'view_type': 'form',
            'view_mode': 'tree,form',
            'res_model': 'asset.move.line',
            'domain': [
                ('jd_depreciation_date', '>=', self.start_date),
                ('jd_depreciation_date', '<=', self.end_date),
                ('jd_move_posted_check', '=', True)
            ],
            'view_id': False,
            'views': [
                (self.env.ref('account_assets.depreciation_line_tree_view').id, 'tree'),
            ],
            'type': 'ir.actions.act_window',
        }

