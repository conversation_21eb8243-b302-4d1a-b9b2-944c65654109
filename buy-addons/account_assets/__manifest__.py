# -*- coding: utf-8 -*-
{
    'name': "固定资产",

    'summary': """
        固定资产""",

    'description': """
       固定资产
    """,

    'author': "openerp.hk Team",
    'website': "http://cdn.openerp.hk",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/14.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': '财务管理/财务管理',
    'version': '********',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account', 'account_asset', 'hr', 'account_accountant', 'account_ledger'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'wizard/depreciation_detail_view.xml',
        'views/inherit_account_asset_view.xml',
        'views/jd_account_asset_view.xml',
        'views/depreciation_line_view.xml',
        'data/asset_default_data.xml',
        'views/menu_view.xml',
        'wizard/wizard_variation_view.xml',
        'wizard/wizard_asset_query_view.xml',
        'wizard/inherit_asset_modify_view.xml',

    ],
    # only loaded in demonstration mode
    # 'qweb': [
    #     'static/src/xml/add_button.xml',
    # ],
    'assets': {
        'web.assets_backend': [
            'account_assets/static/src/xml/add_button.xml',
            'account_assets/static/src/js/tree_button_action.js',
        ]
    },

    'application': True,
    'installable': True,
    'auto_install': False,
    'sequence': 1,
    'license': 'LGPL-3',
}
