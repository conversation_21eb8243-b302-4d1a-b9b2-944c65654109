# -*- coding: utf-8 -*-
# from odoo import http


# class AccountAssets(http.Controller):
#     @http.route('/account_assets/account_assets/', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/account_assets/account_assets/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('account_assets.listing', {
#             'root': '/account_assets/account_assets',
#             'objects': http.request.env['account_assets.account_assets'].search([]),
#         })

#     @http.route('/account_assets/account_assets/objects/<model("account_assets.account_assets"):obj>/', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('account_assets.object', {
#             'object': obj
#         })
