<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="business_trip_application_view_tree" model="ir.ui.view">
            <field name="name">business.trip.application.view.tree</field>
            <field name="model">business.trip.application</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="trip_type"/>
                    <field name="trip_cause"/>
                    <field name="applicant_id" string='申请人'/>
                    <field name="department_id" string='申请人部门'/>
                    <field name="project_id" string='项目名称'/>
                </tree>
            </field>
        </record>

        <record id="business_trip_application_view_form" model="ir.ui.view">
            <field name="name">business.trip.application.view.form</field>
            <field name="model">business.trip.application</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_confirm" invisible="state != 'draft'" type="object" string="确认"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,reimbursed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="code" readonly="True"/>
                                <field name="applicant_id" readonly="True"/>
                                <field name="manager_id" invisible="trip_type != 'project'" readonly="True"/>
                                <field name="department_id" readonly="True" invisible="trip_type == 'project'"
                                       string="申请人部门"/>
                                <field name="department_manager_id" readonly="True"
                                       invisible="trip_type == 'project'"/>
                            </group>
                            <group>
                                <field name="trip_type"/>
                                <field name="opportunity_id"
                                       invisible="trip_type != 'opportunity'"
                                       required="trip_type == 'opportunity'"
                                       options="{'no_create': True}"/>
                                <field name="project_id"
                                       invisible="trip_type != 'project'"
                                       required="trip_type == 'project'"
                                       options="{'no_create': True}"/>
                                <field name="start_date" required="1" widget='date'/>
                                <field name="end_date" required="1" widget='date'/>
                            </group>
                            <group>
                                <field name="trip_cause" widget="text"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="行程明细">
                                <field name="trip_ids" mode="tree">
                                    <tree editable="bottom">
                                        <field name="name" readonly="True"/>
                                        <field name="travel_kind"/>
                                        <field name="traffic"/>
                                        <field name="origin_city"/>
                                        <field name="destination_city"/>
                                        <field name="start_time" required="1" widget='date'/>
                                        <field name="end_time" required="1" widget='date'/>
                                        <field name="duration"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="business_trip_application_view_action" model="ir.actions.act_window">
            <field name="name">差旅申请</field>
            <field name="res_model">business.trip.application</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建一条记录
                </p>
            </field>
        </record>

        <record id="business_trip_application_view_search" model="ir.ui.view">
            <field name="name">business.trip.application.view.search</field>
            <field name="model">business.trip.application</field>
            <field name="arch" type="xml">
                <search string="差旅管理">
                    <field name="manager_id"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="trip_type"/>
                    <field name="project_id"/>
                    <field name="trip_cause"/>
                    <field name="department_id"/>
                </search>
            </field>
        </record>

        <menuitem id="business_trip_menu_root"
                  name="差旅管理"
                  web_icon="business_trip,static/description/icon.png"/>

        <menuitem id="buisness_trip_application_menu"
                  parent="business_trip_menu_root"
                  action="business_trip_application_view_action"/>
    </data>
</odoo>