from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class BusinessTripApplication(models.Model):
    _name = "business.trip.application"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "差旅申请"
    _rec_name = "name"

    _sql_constraints = [('check_date', 'check (end_date >= start_date)', '开始时间必须小于结束时间')]

    @api.depends("applicant_id")
    def _compute_department_manager_id(self):
        for record in self:
            if not record.applicant_id:
                record.department_manager_id = False
                return
            employee_id = self.env["hr.employee"].search(
                [("user_id", "=", record.applicant_id.id)], limit=1
            )
            manager_id = employee_id.parent_id
            if manager_id:
                record.department_manager_id = manager_id.user_id
            else:
                record.department_manager_id = None

    name = fields.Char(string="标题", index=True, required=True)
    code = fields.Char(string="编号", index=True)
    trip_type = fields.Selection(
        [("project", "项目"), ("nonproject", "非项目"), ("opportunity", "商机")],
        string="差旅类型",
        default="project",
    )
    opportunity_id = fields.Many2one("crm.lead", string="商机名称")
    project_id = fields.Many2one("project.project", string="项目名称")
    trip_cause = fields.Text(string="差旅事由")
    trip_ids = fields.One2many(
        "business.trip.application.line", "application_id", string="行程明细"
    )
    company_id = fields.Many2one(
        "res.company",
        string="Company",
        required=True,
        readonly=True,
        index=True,
        default=lambda self: self.env.company,
    )
    applicant_id = fields.Many2one(comodel_name="res.users", string="申请人", default=lambda self: self.env.user.id)
    manager_id = fields.Many2one(related="project_id.user_id", string="项目经理")
    department_id = fields.Many2one('hr.department', string='部门',
                                    default=lambda self: self.env.user.employee_id.department_id
                                    )
    department_manager_id = fields.Many2one(
        comodel_name="res.users",
        string="部门经理",
        compute="_compute_department_manager_id",
    )
    start_date = fields.Datetime(string='开始时间')
    end_date = fields.Datetime(string='结束时间')
    state = fields.Selection(
        string="状态",
        selection=[("draft", "草稿"), ("confirmed", "确认"), ("reimbursed", "已报销")],
        default='draft'
    )
    feishu_id = fields.Char(string='关联飞书申请ID')

    @api.model
    def create(self, vals):
        vals["code"] = self.env["ir.sequence"].next_by_code(
            "business.trip.application"
        )
        result = super(BusinessTripApplication, self).create(vals)

        for data in result:
            if data.trip_ids.filtered(
                    lambda trip: trip.start_time.date() < data.start_date.date() or trip.end_time.date() > data.end_date.date()):
                raise ValidationError('申请单：%s开始时间和结束时间错误！如果是在审批中，请驳回申请，重新填写！')
        return result

    def write(self, vals):
        res = super(BusinessTripApplication, self).write(vals)
        for data in self:
            if not data.start_date or not data.end_date:
                raise ValidationError(
                    '申请单：【%s】开始时间和结束时间错误！如果是在审批中，请驳回申请，重新填写！' % data.name)

            if data.trip_ids.filtered(lambda trip: not trip.start_time or not trip.end_time):
                raise ValidationError(
                    '申请单：【%s】明细行-开始时间和结束时间错误！如果是在审批中，请驳回申请，重新填写！' % data.name)

            if data.trip_ids.filtered(
                    lambda trip: trip.start_time.date() < data.start_date.date() or trip.end_time.date() > data.end_date.date()):
                raise ValidationError('申请单：【%s】开始时间和结束时间错误！请检查，行程明细时间与开始结束时间。'
                                      '如果是在审批中，请驳回申请，重新填写！' % data.name)
        return res

    def action_confirm(self):
        self.write({"state": "confirmed"})


class BusinessTripApplicationLine(models.Model):
    _name = "business.trip.application.line"
    _description = "差旅申请明细行"
    _rec_name = "name"

    @api.depends("start_time", "end_time")
    def _compute_duration(self):
        for record in self:
            value = 0
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                value += delta.days
                value += 1
            record.duration = value

    name = fields.Char(string="行程编号", index=True)
    travel_kind = fields.Selection(
        [("0", "单程"), ("1", "往返")], string="行程类型", default="0"
    )
    traffic = fields.Selection(
        [("0", "飞机"), ("1", "火车"), ("2", "汽车"), ("3", "其它")],
        string="交通方式",
        default="0",
    )
    origin_city = fields.Char(string="出发城市")
    destination_city = fields.Char(string="到达城市")
    start_time = fields.Datetime(string="开始时间")
    end_time = fields.Datetime(string="结束时间")
    duration = fields.Float(string="时长(天)", compute="_compute_duration", digits=(12, 1), store=True)
    application_id = fields.Many2one("business.trip.application", string="关联申请")
    project_id = fields.Many2one("project.project", string='项目', related="application_id.project_id", store=True)

    @api.model
    def create(self, vals):
        vals["name"] = self.env["ir.sequence"].next_by_code(
            "business.trip.application.line"
        )
        result = super(BusinessTripApplicationLine, self).create(vals)
        for data in result:
            if data.start_time and data.application_id and data.application_id.start_date and \
                    data.start_time.date() < data.application_id.start_date.date():
                raise ValidationError('开始时间不能小于申请开始时间')
            if data.end_time and data.application_id and data.application_id.end_date and \
                    data.end_time.date() > data.application_id.end_date.date():
                raise ValidationError('结束时间不能大于申请结束时间')
        return result

    def write(self, vals):
        res = super(BusinessTripApplicationLine, self).write(vals)
        for data in self:
            if data.start_time and data.application_id and data.application_id.start_date and \
                    data.start_time.date() < data.application_id.start_date.date():
                raise ValidationError('开始时间不能小于申请开始时间')
            if data.end_time and data.application_id and data.application_id.end_date and \
                    data.end_time.date() > data.application_id.end_date.date():
                raise ValidationError('结束时间不能大于申请结束时间')
        return res

    @api.constrains("start_time", "end_time")
    def _check_datetime(self):
        for record in self:
            if record.end_time and record.start_time and record.end_time.date() < record.start_time.date():
                raise ValidationError("结束时间必须大于起始时间")
