<?xml version="1.0" encoding="UTF-8" ?>


<odoo>
    <data>
        <record id="register_business_payment_view_form" model="ir.ui.view">
            <field name="name">register.business.payment form</field>
            <field name="model">register.business.payment</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group name="group1">
                            <field name="journal_id" widget="selection" required="1"/>
                            <field name="partner_bank_id"/>
                            <field name="company_id" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="partner_id" invisible="1"/>
                            <field name="amount_all" invisible="1"/>
                            <field name="business_payment_id" invisible="1"/>
                        </group>
                        <group name="group2">
                            <label for="amount"/>
                            <div name="amount_div" class="o_row">
                                <field name="amount"/>
                            </div>
                            <field name="payment_date" required="1"/>
                            <field name="communication"/>
                        </group>
                        <group name="group3"
                               invisible="payment_difference == 0.0"
                               groups="account.group_account_readonly">
                            <label for="payment_difference"/>
                            <div>
                                <field name="payment_difference"/>
                                <field name="payment_difference_handling" widget="radio" nolabel="1"/>
                                <div invisible="payment_difference_handling == 'open'">
                                    <label for="writeoff_account_id" string="过账差额在" class="oe_edit_only"/>
                                    <field name="writeoff_account_id"
                                           string="过账差额在"
                                           options="{'no_create': True}"
                                           required="payment_difference_handling == 'reconcile'"/>
                                    <label for="writeoff_label" class="oe_edit_only" string="标签"/>
                                    <field name="writeoff_label"
                                           required="payment_difference_handling == 'reconcile'"/>
                                </div>
                            </div>
                        </group>
                    </group>
                    <footer>
                        <button name="confirm" type="object" string="创建付款" class="btn-primary"/>
                        <button special="cancel" string="取消" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>