from odoo import api, fields, models


class RegisterBusinessPayment(models.TransientModel):
    _name = 'register.business.payment'
    _description = '业务付款创建付款记录向导'

    journal_id = fields.Many2one('account.journal', string='日记账',
                                 domain="[('company_id', '=', company_id),('type', 'in', ['bank', 'cash'])]")
    company_id = fields.Many2one('res.company', string='公司')
    currency_id = fields.Many2one(related='company_id.currency_id', string='币种')
    amount_all = fields.Monetary(string='总金额')
    amount = fields.Monetary(string='金额')
    payment_difference = fields.Monetary(string='付款差异', compute='_compute_payment_difference')
    # fr_cash_flow_id = fields.Many2one('account.cash.flow', string='现金流量项目')
    writeoff_account_id = fields.Many2one('account.account', string='过账差额在')
    writeoff_label = fields.Char(string='标签', default='Write-Off')
    payment_date = fields.Date(string='付款日期', default=fields.Date.today())
    partner_bank_id = fields.Many2one('res.partner.bank', string='收款银行账号',
                                      domain="['|', ('company_id', '=', False), ('company_id', '=', company_id),('partner_id', '=', partner_id)]")
    communication = fields.Char(string='备忘')
    partner_id = fields.Many2one('res.partner', string='收款人')
    business_payment_id = fields.Many2one('business.payment', string='业务付款')
    payment_difference_handling = fields.Selection([
        ('open', '保持打开'),
        ('reconcile', '标记为全额付款'),
    ], default='open', string="付款差异处理")

    @api.depends('amount')
    def _compute_payment_difference(self):
        for order in self:
            order.payment_difference = order.amount_all - order.amount

    @api.model
    def default_get(self, _fields):
        """
        为向导赋值
        :param _fields:
        :return:
        """
        res = super(RegisterBusinessPayment, self).default_get(_fields)
        business_payment = self.env['business.payment'].browse(self.env.context['active_id'])
        res.update({
            'company_id': business_payment.payment_company_id.id,
            'amount': business_payment.amount_request - business_payment.x_payment_amount,
            'amount_all': business_payment.amount_request - business_payment.x_payment_amount,
            'partner_id': business_payment.payee_id.id
        })
        return res

    def confirm(self):
        payment_vals = {
            'payment_type': 'outbound' if self.business_payment_id.request_type == 'purchase' else 'inbound',
            'partner_type': 'supplier' if self.business_payment_id.request_type == 'purchase' else 'customer',
            'partner_id': self.partner_id.id,
            'destination_account_id': self.partner_id.property_account_payable_id.id,
            'is_internal_transfer': False,
            'company_id': self.company_id.id,
            'amount': self.amount,
            'date': self.payment_date,
            'ref': self.communication,
            'journal_id': self.journal_id.id,
            'partner_bank_id': self.partner_bank_id.id,
        }
        request_type = self.business_payment_id.request_type
        payment_model = self.env['account.payment']

        if hasattr(payment_model, 'partner_type_id'):
            if request_type == 'sale':
                payment_vals['partner_type_id'] = self.env['res.partner.type'].search(
                    [['name', '=', '客户']], limit=1).id
            elif request_type == 'purchase':
                payment_vals['partner_type_id'] = self.env['res.partner.type'].search(
                    [['name', '=', '供应商']], limit=1).id

        if not self.currency_id.is_zero(
                self.payment_difference) and self.payment_difference_handling == 'reconcile':
            payment_vals['write_off_line_vals'] = {
                'name': self.writeoff_label,
                'amount': self.payment_difference,
                'account_id': self.writeoff_account_id.id,
            }

        if request_type == 'purchase':
            residue = self.amount
            purchase_ids = self.business_payment_id.purchase_order_ids

            for purchase in purchase_ids:
                # 订单需要支付还剩余额度
                if purchase.x_nonpayment_taxed_amount and residue:
                    # 可以全部支付
                    if residue - purchase.x_nonpayment_taxed_amount >= 0:
                        residue -= purchase.x_nonpayment_taxed_amount
                        payment_vals['amount'] = purchase.x_nonpayment_taxed_amount
                        account_payment = self.env['account.payment'].create(payment_vals)
                        account_payment.action_post()
                        account_payment.x_purchase_ids = [(4, purchase.id, 0)]
                        # purchase.x_payment_ids = [(4, account_payment.id, 0)]
                        purchase.write({
                            'x_payment_ids': [(4, account_payment.id, 0)]
                        })
                        self.business_payment_id.x_payment_ids = [(4, account_payment.id, 0)]
                    # 部分支付
                    else:
                        payment_vals['amount'] = residue
                        account_payment = self.env['account.payment'].create(payment_vals)
                        account_payment.action_post()
                        account_payment.x_purchase_ids = [(4, purchase.id, 0)]
                        #
                        purchase.x_payment_ids = [(4, account_payment.id, 0)]
                        self.business_payment_id.x_payment_ids = [(4, account_payment.id, 0)]

        if request_type == 'sale':
            account_payment = self.env['account.payment'].create(payment_vals)
            account_payment.action_post()
            account_payment.x_sale_ids = [(4, self.business_payment_id.sale_order_id.id, 0,)]
            self.business_payment_id.sale_order_id.x_payment_ids = [(4, account_payment.id, False)]
            self.business_payment_id.x_payment_ids = [(4, account_payment.id, 0)]

        if request_type == 'earnest':
            account_payment = self.env['account.payment'].create(payment_vals)
            account_payment.action_post()
            self.business_payment_id.x_payment_ids = [(4, account_payment.id, 0)]
            self.business_payment_id.earnest_id.account_payment_id = account_payment.id
            self.business_payment_id.earnest_id.business_payment_id = self.business_payment_id.id
            self.business_payment_id.earnest_id.payment_date = account_payment.date
            self.business_payment_id.earnest_id.is_pay = True
            if self.business_payment_id.earnest_id.crm_tenderer_id:
                self.business_payment_id.earnest_id.crm_tenderer_id.payment_required_id = self.business_payment_id.id
            self.business_payment_id.x_payment_ids = [(4, account_payment.id, 0)]
        return True
