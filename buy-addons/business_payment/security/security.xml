<?xml version="1.0" encoding="UTF-8" ?>

<odoo>
    <data>
        <record id="business_payment_rule" model="ir.rule">
            <field name="name">营销方案对手多公司权限</field>
            <field name="model_id" ref="model_business_payment"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('payment_company_id', '=', False),
                                        ('payment_company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>