<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_move_form_inherit" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_register_payment']" position="replace">
                    <button class="oe_highlight" type="object" name="payment_request_action"
                            invisible="state != 'posted' or payment_state not in ('not_paid', 'partial') or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"
                            groups="account.group_account_invoice"
                    >付款登记
                    </button>
                </xpath>
            </field>
        </record>
    </data>
</odoo>