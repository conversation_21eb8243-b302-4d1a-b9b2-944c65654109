<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="business_payment_tree_view" model="ir.ui.view">
            <field name="name">business payment Tree</field>
            <field name="model">business.payment</field>
            <field name="arch" type="xml">
                <tree default_order="create_date desc">
                    <field name="name"/>
                    <field name="user_id" string="收款单位"/>
                    <field name="payee_id" string="收款单位"/>
                    <field name="requests_date"/>
                    <field name="payment_date" string="登记日期"/>
                    <field name="payment_company_id"/>
                    <field name="amount_request"/>
                    <field name="payment_done_amount"/>
                    <field name="stage_id" widget="badge" decoration-muted="stage_id == 'draft'"
                           decoration-success="stage_id == 'done'"
                           decoration-primary="stage_id == 'pass'" decoration-info="stage_id == 'submit'"/>
                </tree>
            </field>
        </record>

        <record id="business_payment_form_view" model="ir.ui.view">
            <field name="name">business payment Form</field>
            <field name="model">business.payment</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_register_payment" type="object" string="登记付款" class="oe_highlight"
                                invisible="show_payment == False or stage_id not in  ['pass', 'part']"/>
                        <button name="action_pass" type="object" invisible="stage_id != 'submit'" class="oe_highlight">
                            审批通过
                        </button>
                        <button name="action_submit" type="object" invisible="stage_id != 'draft'"
                                class="btn btn-primary">确认
                        </button>
                        <field name="stage_id" widget="statusbar"
                               options="{'fold_field': 'fold'}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <field name="purchase_count" invisible="True"/>
                            <button name="show_purchase_order" class="oe_stat_button"
                                    invisible="purchase_count == 0" type="object">
                                采购订单
                            </button>

                            <field name="x_payment_count" invisible="True"/>
                            <button name="show_payment_order" class="oe_stat_button"
                                    invisible="x_payment_count == 0" type="object">
                                支付单
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="user_id" readonly="stage_id != 'draft'" options="{'no_create': True}"/>
                                <field name="department_id" options="{'no_create': True}"/>
                                <field name="payment_category" readonly="stage_id != 'draft'"/>
                                <field name="request_type" readonly="stage_id != 'draft'"/>
                                <field name="purchase_order_ids" readonly="stage_id != 'draft'"
                                       widget="many2many_tags" options="{'no_create': True}"
                                       domain="[['x_nonpayment_taxed_amount', '>', 0]]"
                                       invisible="request_type != 'purchase'"/>

                                <field name="sale_order_id" readonly="stage_id != 'draft'"
                                       domain="[['x_nonpayment_taxed_amount', '>', 0]]"
                                       invisible="request_type != 'sale'" options="{'no_create': True}"/>
                                <field name="amount_request" readonly="stage_id != 'draft'"/>
                                <field name="amount" readonly="True"/>
<!--                                <field name="amount" readonly="stage_id != 'draft' or request_type in ['sale', 'purchase']"/>-->
                                <field name="order_amount" invisible="request_type not in ['sale', 'purchase']"/>
                                <field name="order_payment_amount" invisible="True"/>
                                <field name="invoice_amount" readonly="1"/>
                                <field name="show_payment" invisible="1"/>
                                <field name="details"/>
                            </group>
                            <group>
                                <field name="payment_method_id" readonly="stage_id != 'draft'"/>
                                <field name="payment_company_id" options="{'no_create': True}"
                                       readonly="stage_id != 'draft'"/>
                                <field name="requests_date" readonly="stage_id != 'draft'"/>
                                <field name="payment_date" readonly="stage_id != 'draft'"/>
                                <field name="payee_id" options="{'no_create': True}" readonly="stage_id != 'draft'"/>
                                <field name="bank_account_id" options="{'no_create': True}"
                                       readonly="stage_id != 'draft'"/>
                                <field name="bank_id" options="{'no_create': True}"/>
<!--                                <field name="payment_done_amount" readonly="1"/>-->
                                <field name="x_payment_amount" readonly="1"/>
                                <field name="note" readonly="stage_id != 'draft'"/>
                                <field name="attachments" widget="many2many_binary" readonly="stage_id != 'draft'"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="message_ids"/>
                        <field name="activity_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="business_payment_purchase_action" model="ir.actions.act_window">
            <field name="name">付款申请</field>
            <field name="res_model">business.payment</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[['request_type', '=', 'purchase']]</field>
            <field name="context">{'default_request_type': 'purchase'}</field>
        </record>

        <record id="business_payment_sale_action" model="ir.actions.act_window">
            <field name="name">付款申请</field>
            <field name="res_model">business.payment</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[['request_type', '=', 'sale']]</field>
            <field name="context">{'default_request_type': 'sale'}</field>
        </record>


        <menuitem id="purchase_requst_payment_menu" name="付款申请" action="business_payment_purchase_action" parent="purchase.menu_purchase_root" />

        <!--    在支付表单添加付款申请字段，以便支付关联到付款申请-->
        <!--        <record id="view_account_payment_form_extend" model="ir.ui.view">-->
        <!--            <field name="name">account.payment.form.inherit</field>-->
        <!--            <field name="model">account.payment</field>-->
        <!--            <field name="inherit_id" ref="account.view_account_payment_form"/>-->
        <!--            <field name="arch" type="xml">-->
        <!--                <xpath expr="//field[@name='payment_method_line_id']" position="after">-->
        <!--                    <field name="business_payment_id" string='付款申请' options="{'no_create': True, 'no_edit': True}"/>-->
        <!--                </xpath>-->
        <!--            </field>-->
        <!--        </record>-->

    </data>
</odoo>
