# -*- coding: utf-8 -*-
# from odoo import http


# class XcBusinessPayment(http.Controller):
#     @http.route('/xc_business_payment/xc_business_payment', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/xc_business_payment/xc_business_payment/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('xc_business_payment.listing', {
#             'root': '/xc_business_payment/xc_business_payment',
#             'objects': http.request.env['xc_business_payment.xc_business_payment'].search([]),
#         })

#     @http.route('/xc_business_payment/xc_business_payment/objects/<model("xc_business_payment.xc_business_payment"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('xc_business_payment.object', {
#             'object': obj
#         })

