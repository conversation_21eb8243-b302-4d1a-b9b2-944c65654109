import datetime

from odoo import _, api, fields, models
from odoo.exceptions import UserError

PAYMENT_TYPE = [
    ['sale', '销售'],
    ['purchase', '采购'],
    ['earnest', '保证金'],
]


class BusinessPaymentCategory(models.Model):
    _name = "business.payment.category"
    name = fields.Char("名称")


class BusinessPayment(models.Model):
    _name = "business.payment"
    _description = '业务付款'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='单号', default="草稿")
    account_move_id = fields.Many2one('account.move', string='账单')
    amount = fields.Float(string='应付金额')
    amount_request = fields.Float(string='本次请款金额')
    bank_account_id = fields.Many2one('res.partner.bank', string='银行账号', domain="[['partner_id', '=', payee_id]]")
    stage_id = fields.Selection(
        [('draft', '草稿'), ('submit', '已提交'), ('pass', '已审批待支付'), ('part', '部分支付'), ('done', '支付完成')],
        string='状态', default='draft')

    bank_id = fields.Many2one('res.bank', string='开户行', related='bank_account_id.bank_id')
    department_id = fields.Many2one('hr.department', string='申请人部门', related="employee_id.department_id")
    description = fields.Char(string='描述')
    details = fields.Text(string='付款说明')
    attachments = fields.Many2many('ir.attachment', string='附件')
    user_id = fields.Many2one('res.users', string='申请人', default=lambda self: self.env.user.id)
    employee_id = fields.Many2one('hr.employee', string='员工', related="user_id.employee_id")
    note = fields.Text(string='备注')
    payee_id = fields.Many2one('res.partner', string='收款人')

    payment_company_id = fields.Many2one('res.company', string='付款单位', default=lambda self: self.env.company.id)
    requests_date = fields.Date(string='请款日期', default=lambda self: datetime.date.today())
    payment_date = fields.Date(string='登记日期', default=lambda self: datetime.date.today())
    project_id = fields.Many2one('project.project', string='项目')
    partner_id = fields.Many2one('res.partner', string='供应商')
    payment_done_amount = fields.Float(string='已支付金额', compute='_compute_payment_done_amount')
    invoice_amount = fields.Float(string='已开票金额', compute="_compute_invoice_amount")
    order_amount = fields.Float("订单金额", compute="_compute_invoice_amount")

    def check_partner(self):
        if len(set(p.partner_id.id for p in self.purchase_order_ids)) > 1:
            self.purchase_order_ids = [(6, 0, self.purchase_order_ids.ids[0:-1])]

    @api.depends("request_type", "sale_order_id", "purchase_order_ids")
    def _compute_invoice_amount(self):
        for record in self:
            if record.request_type == 'purchase':
                # record.check_partner()
                if record.stage_id == 'draft':
                    record.amount_request = sum(
                        [p.x_nonpayment_taxed_amount for p in record.purchase_order_ids]
                    ) or record.amount_request
                record.amount = record.amount_request
                record.payee_id = record.purchase_order_ids[:1].partner_id.id
                record.invoice_amount = sum(
                    [p.x_invoice_taxed_amount for p in record.purchase_order_ids]
                )
                record.order_amount = sum(
                    purchase.amount_total for purchase in record.purchase_order_ids
                )
            elif record.request_type == 'sale':
                if record.stage_id == 'draft':
                    record.amount_request = record.sale_order_id.x_nonpayment_taxed_amount or record.amount_request
                record.amount = record.sale_order_id.x_nonpayment_taxed_amount
                record.payee_id = record.sale_order_id.partner_id.id
                record.invoice_amount = record.sale_order_id.x_invoice_taxed_amount
                record.order_amount = record.sale_order_id.amount_total
            else:
                if record.stage_id == 'draft':
                    record.amount_request = record.amount_request or record.earnest_id.amount

                if hasattr(record, 'earnest_id'):
                    record.amount = record.earnest_id.amount
                    record.invoice_amount = record.earnest_id.amount
                else:
                    record.invoice_amount = 0
                record.order_amount = 0

    paid_amount = fields.Float('合同已付金额')
    show_payment = fields.Boolean(string='展示登记付款按钮', compute='_compute_show_payment')
    company_id = fields.Many2one('res.company', string='公司')
    payment_method_id = fields.Many2one('payment.method', string='付款方式')
    sale_order_id = fields.Many2one("sale.order", string='销售订单')
    purchase_order_ids = fields.Many2many("purchase.order", 'purchase_payment_request', string='采购订单')

    purchase_count = fields.Integer(compute="_compute_purchase_count")

    @api.depends("purchase_order_ids")
    def _compute_purchase_count(self):
        for record in self:
            record.purchase_count = len(record.purchase_order_ids)

    account_payment_id = fields.Many2one("account.payment", string='应付单号', domain=[
        ['state', '=', 'draft'],
        ['payment_type', '=', 'outbound']
    ])

    x_payment_ids = fields.Many2many("account.payment", 'request_payment', string='支付信息')
    x_payment_amount = fields.Float("已支付金额", compute="_compute_x_payment", store=True)
    order_payment_amount = fields.Float("订单已支付金额", compute="_compute_order_payment_amount", store=True)

    @api.depends("request_type", "sale_order_id", "purchase_order_ids")
    def _compute_order_payment_amount(self):
        for record in self:
            if record.request_type == 'sale':
                record.order_payment_amount = record.sale_order_id.x_payment_taxed_amount
            elif record.request_type == 'purchase':
                record.order_payment_amount = sum(record.purchase_order_ids.mapped("x_payment_taxed_amount"))
            else:
                record.order_payment_amount = 0

    x_payment_count = fields.Integer(compute="_compute_x_payment", store=True)

    @api.depends("x_payment_ids")
    def _compute_x_payment(self):
        for record in self:
            record.x_payment_count = len(record.x_payment_ids)
            amount = 0

            for pay in record.x_payment_ids:
                amount += pay.amount_total

            record.x_payment_amount = amount
            if record.stage_id == 'pass':
                if record.amount != 0 and record.x_payment_amount >= record.amount_request:
                    record.stage_id = 'done'
                else:
                    record.stage_id = 'part'

    payment_category = fields.Many2one("business.payment.category", string='付款类别')

    request_type = fields.Selection(PAYMENT_TYPE, string='付款类型', required=True)

    @api.onchange("request_type")
    def _onchange_request_type(self):
        for record in self:
            if self.request_type == 'sale':
                record.purchase_order_ids = [(5, False, False)]
                if hasattr(record, 'earnest_id'):
                    record.earnest_id = None
            elif self.request_type == 'purchase':
                if hasattr(record, 'earnest_id'):
                    record.earnest_id = None
                record.sale_order_id = None
            elif self.request_type == 'earnest':
                record.sale_order_id = None
                record.purchase_order_ids = [(5, False, False)]

    @api.onchange('payee_id')
    def _onchange_payee_id(self):
        """根据收款人自动带出银行账号"""
        if self.payee_id:
            # main_bank_id字段存在模块account_base_extend
            if hasattr(self.payee_id, "main_bank_id") and self.payee_id.main_bank_id:
                self.bank_account_id = self.payee_id.main_bank_id.id
            else:
                self.bank_account_id = self.payee_id.bank_ids and self.payee_id.bank_ids[0] or False

    @api.depends("x_payment_amount", 'amount')
    def _compute_show_payment(self):
        """
        计算展示登记付款按钮值
        @return:
        """
        for order in self:
            order.show_payment = order.x_payment_amount < order.amount

    def show_purchase_order(self):
        """
        跳转至采购订单
        """
        return {
            'name': "采购订单",
            'res_model': 'purchase.order',
            'type': 'ir.actions.act_window',
            'views': [[False, "tree"], [False, 'form']],
            'target': "current",
            'domain': [['id', 'in', self.purchase_order_ids.ids]],
            'context': {
                "create": False
            }
        }

    def action_register_payment(self):
        """
        跳转到登记付款向导
        :return:
        """
        return {
            'name': _('登记付款'),
            'res_model': 'register.business.payment',
            'view_mode': 'form',
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': {
                'default_business_payment_id': self.id,
                'request_type': self.request_type,
                'order_data': {
                    'sale_id': self.sale_order_id.id,
                    'purchase_ids': self.purchase_order_ids.ids,
                    'earnest_id': self.earnest_id.id if hasattr(self, 'earnest_id') else None,
                }
            }
        }

    def action_submit(self):
        """
        提交按钮
        """
        self.ensure_one()
        if not self.payee_id:
            raise UserError("收款人为空")
        if self.amount == 0:
            raise UserError("应付金额为0！")
        self.write({
            "stage_id": "submit",
            "name": self.env['ir.sequence'].next_by_code("business.payment.seq")
        })

    def action_pass(self):
        """
        通过按钮
        """
        self.stage_id = 'pass'

    def action_back(self):
        """
        返回到已审批待支付
        """
        self.stage_id = 'pass'

    def action_cancel(self):
        """
        取消按钮
        """
        self.stage_id = 'draft'

    def _compute_payment_done_amount(self):
        """
        计算已支付金额
        @return:
        """
        for record in self:
            if record.request_type == 'purchase' and record.purchase_order_ids:
                record.payment_done_amount = sum(
                    [purchase.x_payment_taxed_amount for purchase in record.purchase_order_ids])
            elif record.request_type == 'earnest' and record.earnest_id.account_payment_id:
                record.payment_done_amount = record.earnest_id.account_payment_id.amount
            elif record.request_type == 'sale' and record.sale_order_id:
                record.payment_done_amount = record.sale_order_id.x_payment_taxed_amount
            else:
                record.payment_done_amount = 0

    def show_payment_order(self):
        return {
            "name": "支付单",
            'type': 'ir.actions.act_window',
            'res_model': self.x_payment_ids._name,
            'domain': [
                ['id', '=', self.x_payment_ids.ids]
            ],
            'context': {
                'create': False,
            },
            'views': [[False, 'tree'], [False, 'form']],
            'target': "current"
        }
